{"host": "localhost", "port": 5432, "database": "postgres", "schema": "public", "user": "postgres", "password": "gj0628", "additionalString": "", "timezone": "+08:00", "logPluginName": "pgoutput", "enableSsl": false, "sslMode": "disable", "sslCA": "", "sslCert": "", "sslKey": "", "sslPass": "", "connectionTimeout": 30000, "socketTimeout": 30000, "maxPoolSize": 10, "minPoolSize": 1, "maxIdleTime": 300000, "validationQuery": "SELECT 1", "validationInterval": 30000}