package com.tapdata.tm.license.service;

import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.commons.dag.DAG;
import com.tapdata.tm.commons.dag.Node;
import com.tapdata.tm.commons.dag.nodes.DataParentNode;
import com.tapdata.tm.commons.schema.DataSourceConnectionDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.ds.service.impl.DataSourceService;
import com.tapdata.tm.license.dto.TaskPipelineLimitDto;
import com.tapdata.tm.license.repository.TaskPipelineLimitRepository;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.user.service.UserService;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.internal.verification.Times;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class TaskPipelineServiceTest {
	private TaskPipelineLimitService taskPipelineService;
	private TaskService taskService;
	private UserService userService;
	private DataSourceService dataSourceService;
	private TaskPipelineLimitRepository repository;
	private BulkOperations bulkOperations;

	@BeforeEach
	void beforeEach() {
		taskPipelineService = mock(TaskPipelineLimitService.class);
		taskService = mock(TaskService.class);
		userService = mock(UserService.class);
		dataSourceService = mock(DataSourceService.class);
		repository = mock(TaskPipelineLimitRepository.class);
		ReflectionTestUtils.setField(taskPipelineService, "taskService", taskService);
		ReflectionTestUtils.setField(taskPipelineService, "userService", userService);
		ReflectionTestUtils.setField(taskPipelineService, "dataSourceService", dataSourceService);
		ReflectionTestUtils.setField(taskPipelineService, "repository", repository);
		bulkOperations = mock(BulkOperations.class);
		when(repository.bulkOperations(BulkOperations.BulkMode.UNORDERED)).thenReturn(bulkOperations);
	}

	@Nested
	class recordTaskPipelineGrantTest {
		@Test
		void testRecordTaskPipelineGrant() {
			TaskDto taskDto = mock(TaskDto.class);
			ObjectId taskId = mock(ObjectId.class);
			UserDetail user = mock(UserDetail.class);
			doCallRealMethod().when(taskPipelineService).recordTaskPipelineGrant(taskDto, 1, user);
			taskPipelineService.recordTaskPipelineGrant(taskDto, 1, user);
			verify(taskPipelineService, new Times(1)).cleanInvalidTaskOccupation();
			verify(taskPipelineService, new Times(1)).cleanInvalidPipeline();
			verify(taskPipelineService, new Times(1)).cleanCurrentTaskOccupation(taskDto);
		}
	}

	@Nested
	class validateTaskPipelineTest {
		@Test
		void testValidateTaskPipeline() {
			List<TaskDto> runningTasks = new ArrayList<>();
			TaskDto taskDto = mock(TaskDto.class);
			when(taskDto.getUserId()).thenReturn("111");
			runningTasks.add(taskDto);
			List<TaskPipelineLimitDto> taskPipelineLimitDtos = new ArrayList<>();
			taskPipelineLimitDtos.add(mock(TaskPipelineLimitDto.class));
			when(taskPipelineService.findAll(any(Query.class))).thenReturn(taskPipelineLimitDtos);
			when(taskPipelineService.getRunningOrScheduleTask(null)).thenReturn(runningTasks);
			doCallRealMethod().when(taskPipelineService).validateTaskPipeline();
			taskPipelineService.validateTaskPipeline();
			verify(taskPipelineService, new Times(1)).stopTaskIfNeed(taskDto, "111");
		}
	}

	@Nested
	class stopTaskIfNeedTest {
		@BeforeEach
		void beforeEach() {
			List<TaskPipelineLimitDto> taskPipelineDtos = new ArrayList<>();
			TaskPipelineLimitDto taskPipelineDto = new TaskPipelineLimitDto();
			taskPipelineDto.setPipelineId("7168b130b7e34a93c6c0098149f195ae");
			List<String> taskIds = new ArrayList<>();
			taskIds.add("66d17b19d3db6631b0346cef");
			taskPipelineDto.setTaskIds(taskIds);
			taskPipelineDtos.add(taskPipelineDto);
			when(taskPipelineService.findAll(any(Query.class))).thenReturn(taskPipelineDtos);
			doCallRealMethod().when(taskPipelineService).flushPipelineAndTaskIdsMap();
		}

		@Test
		@DisplayName("test stopTaskIfNeed when task is valid")
		void test1() {
			TaskDto taskDto = mock(TaskDto.class);
			ObjectId taskId = new ObjectId("66d17b19d3db6631b0346cef");
			when(taskDto.getId()).thenReturn(taskId);
			when(taskDto.getUserId()).thenReturn("66d055c410cfdf4d6a99a9ef");
			UserDetail user = mock(UserDetail.class);
			doCallRealMethod().when(taskPipelineService).stopTaskIfNeed(taskDto, "111");
			taskPipelineService.stopTaskIfNeed(taskDto, "66d055c410cfdf4d6a99a9ef");
			verify(taskService, new Times(0)).pause(taskId, user, false);
		}

		@Test
		@DisplayName("test stopTaskIfNeed when task is invalid")
		void test2() {
			TaskDto taskDto = mock(TaskDto.class);
			ObjectId taskId = new ObjectId("66d17b19d3db6631b0346ce1");
			when(taskDto.getId()).thenReturn(taskId);
			when(taskDto.getUserId()).thenReturn("66d055c410cfdf4d6a99a9ef");
			DAG dag = mock(DAG.class);
			when(taskDto.getDag()).thenReturn(dag);
			List<Node> sources = new ArrayList<>();
			DataParentNode source1 = mock(DataParentNode.class);
			sources.add(source1);
			List<Node> targets = new ArrayList<>();
			DataParentNode target = mock(DataParentNode.class);
			targets.add(target);
			when(taskDto.getDag()).thenReturn(dag);
			when(dag.getSourceNodes()).thenReturn(sources);
			when(dag.getTargets()).thenReturn(targets);
			Map<String, String> sourceInstanceInfo = new HashMap<>();
			String sourceId1 = "4df9e5f910213a56e9e1d903e39329db";
			sourceInstanceInfo.put("id", sourceId1);
			when(taskPipelineService.buildInstanceInfo(source1)).thenReturn(sourceInstanceInfo);
			Map<String, String> targetInstanceInfo = new HashMap<>();
			String targetId = "7e62e76bb5701454987485b28a70eecc";
			targetInstanceInfo.put("id", targetId);
			when(taskPipelineService.countTaskPipeline()).thenReturn(1L);
			when(taskPipelineService.buildInstanceInfo(target)).thenReturn(targetInstanceInfo);
			UserDetail user = mock(UserDetail.class);
			when(userService.loadUserById(any(ObjectId.class))).thenReturn(user);
			doCallRealMethod().when(taskPipelineService).generatePipelineId(anyString(), anyString());
			doCallRealMethod().when(taskPipelineService).stopTaskIfNeed(taskDto, "66d055c410cfdf4d6a99a9ef");
			taskPipelineService.stopTaskIfNeed(taskDto, "66d055c410cfdf4d6a99a9ef");
			verify(taskService, new Times(1)).pause(taskId, user, false);
		}
	}

	@Nested
	class cleanInvalidTaskOccupationTest {
		@BeforeEach
		void beforeEach() {
			List<TaskPipelineLimitDto> taskPipelineDtos = new ArrayList<>();
			TaskPipelineLimitDto taskPipelineDto = new TaskPipelineLimitDto();
			taskPipelineDto.setPipelineId("7168b130b7e34a93c6c0098149f195ae");
			List<String> taskIds = new ArrayList<>();
			taskIds.add("66d17b19d3db6631b0346cef");
			taskPipelineDto.setTaskIds(taskIds);
			taskPipelineDtos.add(taskPipelineDto);
			when(taskPipelineService.findAll(any(Query.class))).thenReturn(taskPipelineDtos);
			doCallRealMethod().when(taskPipelineService).flushPipelineAndTaskIdsMap();
		}

		@Test
		void testCleanInvalidTaskOccupation() {
			String id = "111";
			List<TaskDto> tasks = new ArrayList<>();
			when(taskPipelineService.getRunningOrScheduleTask(id)).thenReturn(tasks);
			doCallRealMethod().when(taskPipelineService).cleanInvalidTaskOccupation();
			taskPipelineService.cleanInvalidTaskOccupation();
			verify(bulkOperations, new Times(1)).updateOne(any(Query.class), any(Update.class));
		}
	}

	@Nested
	class getRunningOrScheduleTaskTest {
		@Test
		void testGetRunningOrScheduleTask() {
			doCallRealMethod().when(taskPipelineService).getRunningOrScheduleTask(null);
			taskPipelineService.getRunningOrScheduleTask(null);
			verify(taskService, new Times(1)).findAll(any(Query.class));
		}
	}

	@Nested
	class cleanInvalidPipelineTest {
		private List<TaskPipelineLimitDto> taskPipelines;

		@BeforeEach
		void beforeEach() {
			taskPipelines = new ArrayList<>();
			when(taskPipelineService.findAll(any(Query.class))).thenReturn(taskPipelines);
			doCallRealMethod().when(taskPipelineService).cleanInvalidPipeline();
		}

		@Test
		@DisplayName("test cleanInvalidPipeline method when taskIds is empty")
		void test1() {
			TaskPipelineLimitDto taskPipelineDto = new TaskPipelineLimitDto();
			String pipelineId = "eb3df76dfffc48b5251c960eff078925";
			taskPipelineDto.setId(mock(ObjectId.class));
			taskPipelineDto.setPipelineId(pipelineId);
			taskPipelineDto.setTaskIds(new ArrayList<>());
			taskPipelines.add(taskPipelineDto);
			taskPipelineService.cleanInvalidPipeline();
			verify(taskPipelineService, new Times(1)).deleteById(any(ObjectId.class));
		}

		@Test
		@DisplayName("test cleanInvalidPipeline method when taskIds is not empty")
		void test2() {
			TaskPipelineLimitDto taskPipelineDto = new TaskPipelineLimitDto();
			String pipelineId = "eb3df76dfffc48b5251c960eff078925";
			taskPipelineDto.setPipelineId(pipelineId);
			List<String> taskIds = new ArrayList<>();
			taskIds.add("66d18e477ffd37467cbd69f6");
			taskPipelineDto.setTaskIds(taskIds);
			taskPipelines.add(taskPipelineDto);
			taskPipelineService.cleanInvalidPipeline();
			verify(taskPipelineService, new Times(0)).deleteById(any(ObjectId.class));
		}
	}

	@Nested
	class cleanCurrentTaskOccupationTest {
		private TaskDto taskDto;

		@BeforeEach
		void beforeEach() {
			taskDto = mock(TaskDto.class);
			doCallRealMethod().when(taskPipelineService).flushPipelineAndTaskIdsMap();
			doCallRealMethod().when(taskPipelineService).cleanCurrentTaskOccupation(taskDto);
		}

		@Test
		@DisplayName("test cleanCurrentTaskOccupation method when contains taskId")
		void test1() {
			List<TaskPipelineLimitDto> taskPipelineDtos = new ArrayList<>();
			TaskPipelineLimitDto taskPipelineDto = new TaskPipelineLimitDto();
			taskPipelineDto.setPipelineId("eb3df76dfffc48b5251c960eff078925");
			List<String> taskIds = new ArrayList<>();
			String taskId = "66d18e477ffd37467cbd69f6";
			taskIds.add(taskId);
			taskPipelineDto.setTaskIds(taskIds);
			taskPipelineDtos.add(taskPipelineDto);
			when(taskPipelineService.findAll(any(Query.class))).thenReturn(taskPipelineDtos);
			when(taskDto.getId()).thenReturn(new ObjectId(taskId));
			taskPipelineService.cleanCurrentTaskOccupation(taskDto);
			verify(bulkOperations, new Times(1)).updateOne(any(Query.class), any(Update.class));
		}

		@Test
		@DisplayName("test cleanCurrentTaskOccupation methoo when not contains taskId")
		void test2() {
			taskPipelineService.cleanCurrentTaskOccupation(taskDto);
			verify(taskPipelineService, new Times(0)).update(any(Query.class), any(Update.class));
		}
	}

	@Nested
	class grantForCurrentTaskTest {
		private TaskDto taskDto;
		private Integer datasourcePipelineLimit;
		private UserDetail user;

		@BeforeEach
		void beforeEach() {
			taskDto = mock(TaskDto.class);
			datasourcePipelineLimit = 2;
			user = mock(UserDetail.class);
			List<TaskPipelineLimitDto> taskPipelineDtos = new ArrayList<>();
			TaskPipelineLimitDto taskPipelineDto = new TaskPipelineLimitDto();
			taskPipelineDto.setPipelineId("7168b130b7e34a93c6c0098149f195ae");
			List<String> taskIds = new ArrayList<>();
			taskIds.add("66d17b19d3db6631b0346cef");
			taskPipelineDto.setTaskIds(taskIds);
			taskPipelineDtos.add(taskPipelineDto);
			when(taskPipelineService.findAll(any(Query.class))).thenReturn(taskPipelineDtos);
			doCallRealMethod().when(taskPipelineService).flushPipelineAndTaskIdsMap();
			doCallRealMethod().when(taskPipelineService).generatePipelineId(anyString(), anyString());
			doCallRealMethod().when(taskPipelineService).grantForCurrentTask(taskDto, datasourcePipelineLimit, user);
			when(taskDto.getSyncType()).thenReturn(TaskDto.SYNC_TYPE_MIGRATE);
			when(taskDto.getId()).thenReturn(mock(ObjectId.class));
			DAG dag = mock(DAG.class);
			List<Node> sources = new ArrayList<>();
			DataParentNode source1 = mock(DataParentNode.class);
			DataParentNode source2 = mock(DataParentNode.class);
			sources.add(source1);
			sources.add(source2);
			List<Node> targets = new ArrayList<>();
			DataParentNode target = mock(DataParentNode.class);
			targets.add(target);
			when(taskDto.getDag()).thenReturn(dag);
			when(dag.getSourceNodes()).thenReturn(sources);
			when(dag.getTargets()).thenReturn(targets);
			Map<String, String> sourceInstanceInfo = new HashMap<>();
			String sourceId1 = "4df9e5f910213a56e9e1d903e39329db";
			sourceInstanceInfo.put("id", sourceId1);
			when(taskPipelineService.buildInstanceInfo(source1)).thenReturn(sourceInstanceInfo);
			Map<String, String> sourceInstanceInfo2 = new HashMap<>();
			String sourceId2 = "7e62e76bb5701454987485b28a70eecc";
			sourceInstanceInfo2.put("id", sourceId2);
			when(taskPipelineService.buildInstanceInfo(source2)).thenReturn(sourceInstanceInfo2);
			Map<String, String> targetInstanceInfo = new HashMap<>();
			String targetId = "0b4c1f3386211a4bdc62d88fba8aeb3d";
			targetInstanceInfo.put("id", targetId);
			when(taskPipelineService.countTaskPipeline()).thenReturn(1L);
			when(taskPipelineService.buildInstanceInfo(target)).thenReturn(targetInstanceInfo);
		}

		@Test
		@DisplayName("test grantForCurrentTask method when task type is logCollector")
		void test1() {
			when(taskDto.getSyncType()).thenReturn(TaskDto.SYNC_TYPE_LOG_COLLECTOR);
			boolean actual = taskPipelineService.grantForCurrentTask(taskDto, datasourcePipelineLimit, user);
			assertTrue(actual);
		}

		@Test
		@DisplayName("test grantForCurrentTask method for two source nodes")
		void test2() {
			when(taskPipelineService.save(any(TaskPipelineLimitDto.class), any(UserDetail.class))).thenReturn(mock(TaskPipelineLimitDto.class));
			boolean actual = taskPipelineService.grantForCurrentTask(taskDto, datasourcePipelineLimit, user);
			assertTrue(actual);
			verify(taskPipelineService, new Times(1)).update(any(Query.class), any(Update.class));
			verify(taskPipelineService, new Times(1)).countTaskPipeline();
			verify(taskPipelineService, new Times(1)).save(any(TaskPipelineLimitDto.class), any(UserDetail.class));
		}

		@Test
		@DisplayName("test grantForCurrentTask method when exceed limit")
		void test3() {
			datasourcePipelineLimit = 1;
			doCallRealMethod().when(taskPipelineService).grantForCurrentTask(taskDto, datasourcePipelineLimit, user);
			boolean actual = taskPipelineService.grantForCurrentTask(taskDto, datasourcePipelineLimit, user);
			assertFalse(actual);
			verify(taskPipelineService, new Times(1)).update(any(Query.class), any(Update.class));
			verify(taskPipelineService, new Times(1)).countTaskPipeline();
			verify(taskPipelineService, new Times(0)).save(any(TaskPipelineLimitDto.class), any(UserDetail.class));
		}
	}

	@Nested
	class generatePipelineIdTest {
		@Test
		@DisplayName("test generatePipelineId method normal")
		void test1() {
			String sourceInstanceId = "0b4c1f3386211a4bdc62d88fba8aeb3d";
			String targetInstanceId = "4df9e5f910213a56e9e1d903e39329db";
			doCallRealMethod().when(taskPipelineService).generatePipelineId(anyString(), anyString());
			String expect = "7168b130b7e34a93c6c0098149f195ae";
			String pipelineId1 = taskPipelineService.generatePipelineId(sourceInstanceId, targetInstanceId);
			String pipelineId2 = taskPipelineService.generatePipelineId(targetInstanceId, sourceInstanceId);
			assertEquals(expect, pipelineId1);
			assertEquals(expect, pipelineId2);
		}

		@Test
		@DisplayName("test generatePipelineId method when sourceInstanceId is blank")
		void test2() {
			String sourceInstanceId = "";
			String targetInstanceId = "4df9e5f910213a56e9e1d903e39329db";
			doCallRealMethod().when(taskPipelineService).generatePipelineId(sourceInstanceId, targetInstanceId);
			String pipelineId = taskPipelineService.generatePipelineId(sourceInstanceId, targetInstanceId);
			assertNull(pipelineId);
		}
	}

	@Nested
	class buildInstanceIdTest {
		@Test
		@DisplayName("test buildInstanceId method normal")
		void test1() {
			Node node = mock(DataParentNode.class);
			String connectionId = "66d19671ed75fe6a379a74a2";
			when(((DataParentNode) node).getConnectionId()).thenReturn(connectionId);
			DataSourceConnectionDto connectionDto = mock(DataSourceConnectionDto.class);
			when(dataSourceService.findById(any(ObjectId.class), anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(connectionDto);
			Map<String, Object> config = new HashMap<>();
			String instanceId = "0b4c1f3386211a4bdc62d88fba8aeb3d";
			config.put("datasourceInstanceId", instanceId);
			when(connectionDto.getConfig()).thenReturn(config);
			doCallRealMethod().when(taskPipelineService).buildInstanceInfo(node);
			Map<String, String> actual = taskPipelineService.buildInstanceInfo(node);
			assertEquals(instanceId, actual.get("id"));
		}

		@Test
		@DisplayName("test buildInstanceId method when node is not instance of DataParentNode")
		void test2() {
			Node node = mock(Node.class);
			doCallRealMethod().when(taskPipelineService).buildInstanceInfo(node);
			assertThrows(BizException.class, () -> taskPipelineService.buildInstanceInfo(node));
		}

		@Test
		@DisplayName("test buildInstanceId method when instance info is null")
		void test3() {
			Node node = mock(DataParentNode.class);
			String connectionId = "66d19671ed75fe6a379a74a2";
			when(((DataParentNode) node).getConnectionId()).thenReturn(connectionId);
			DataSourceConnectionDto connectionDto = mock(DataSourceConnectionDto.class);
			when(dataSourceService.findById(any(ObjectId.class), anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(connectionDto);
			Map<String, Object> config = new HashMap<>();
			config.put("ip", "127.0.0.1");
			when(connectionDto.getConfig()).thenReturn(config);
			ObjectId id = new ObjectId(connectionId);
			when(connectionDto.getId()).thenReturn(id);
			doCallRealMethod().when(taskPipelineService).buildInstanceInfo(node);
			Map<String, String> actual = taskPipelineService.buildInstanceInfo(node);
			assertEquals(connectionId, actual.get("id"));
		}
	}
}
