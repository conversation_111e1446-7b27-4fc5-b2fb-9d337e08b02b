package com.tapdata.tm.license.dto;

import com.tapdata.tm.commons.base.dto.BaseDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class TaskPipelineLimitDto extends BaseDto {
	/**
	 * 通道编号：md5(join("|", sort(source.instanceId, target.instanceId)))
	 */
	private String pipelineId;
	/**
	 * 数据源实例编号（{source.instanceId, source.instanceTag, source.pdkId}, {target.instanceId, target.instanceTag, target.pdkId}）
	 */
	private List<Map<String, String>> instanceInfos;
	/**
	 * 引用的任务编号
	 */
	private List<String> taskIds;
}
