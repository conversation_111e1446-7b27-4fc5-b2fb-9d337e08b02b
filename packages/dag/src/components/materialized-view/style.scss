.materialized-view-node {
  width: 300px;

  &.active:before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid #000;
    border-radius: 10px;
    z-index: -1;
    transform: translateZ(0);
  }

  code {
    font-family: $codeFontFamily;
  }

  .node-header {
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
    //border-top: 6px solid map.get($color, primary);
  }

  &:not(.--target) {
    &.active:before {
      border-color: #86909c;
    }
    .node-title {
      background-color: #86909c;
    }
    .field-icon {
      color: #86909c;
    }
  }

  &.--target {
    .field-icon {
      color: map.get($color, primary);
    }
  }

  &.--main-table {
    &.active:before {
      border-color: #f3961a;
    }
    .node-title {
      background-color: #f3961a;
    }
    .field-icon {
      color: #f3961a;
    }
  }

  .node-body {
    .el-tree-node__content {
      border-radius: 6px;
      .field-icon {
        left: -20px;
        top: 50%;
        transform: translateY(-50%);
      }
      .field-add-btn {
        display: none;
        left: -24px;
      }
      /*&:hover {
        .field-icon {
          display: none;
        }
        .field-add-btn {
          display: inline-flex;
        }
      }*/
    }
  }

  .node-schema-tree {
    max-height: 200px;
  }

  &.--target {
    .node-header {
      .el-input .el-input__inner {
        border-color: transparent;
      }
    }

    .node-body {
      border-bottom-left-radius: inherit;
      border-bottom-right-radius: inherit;
      border: 2px solid map.get($color, primary);
      border-top: none;
    }

    .node-schema-tree {
      max-height: 400px;
    }
  }

  .color-success-light-5 {
    color: #009a29;
  }
  .el-form-item {
    margin-bottom: 8px;

    .el-form-item__label {
      width: 100%;
      font-size: 12px;
      line-height: 20px;
      padding-bottom: 0;
      margin-bottom: 6px;
    }

    .el-button--ghost {
      border: 1px dashed #dcdfe6;
    }
  }

  .el-input {
    .el-input__inner {
      //padding-right: 24px;
      font-size: 12px;
    }
    .el-input__suffix {
      right: 0;
    }

    &:not(.el-input--prefix) .el-input__inner {
      padding-left: 8px;
    }
  }

  .table-select {
    .el-input__inner {
      height: 32px !important;
    }
  }
}
