package com.tapdata.tm.ds.dto;

import com.tapdata.tm.commons.base.dto.BaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: Zed
 * @Date: 2021/8/20
 * @Description: 数据源类型
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataSourceTypeDto extends BaseDto {
    /** 名称 Amazon S3 Connection Verifier */
    private String name;
    /** 类型  amazon_s3_connection_verifier */
    private String type;
    /**  */
    private String source;
    /** 源，目标， 源&目标 */
    private String connectionType;
    /**  */
    private String buildProfiles;
    /** 类名 */
    private String className;
    /** 支持的目标数据源 */
    private List<String> supportTargetDatabaseType;
    /** lib目录 */
    private String libDir;
    /** lib名 */
    private String libName;
    /** 版本 */
    private String version;

    private List<String> tags; // 标签  - 本地自建库(localDatabase) 云数据库(cloudDatabase) 消息队列(mq) NoSQL数据库(nosql) SaaS应用(saas)

    private Boolean isComing; // 即将上线
}
