package com.tapdata.manager.base.convert;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.bson.types.ObjectId;

import java.io.IOException;

import static com.tapdata.manager.utils.MongoUtils.toObjectId;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/8/15 2:06 下午
 * @description
 */
public class ObjectIdDeserialize extends JsonDeserializer<ObjectId> {
	@Override
	public ObjectId deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
		String value = p.getValueAsString();
		if (value != null)
			return toObjectId(value);
		return null;
	}
}
