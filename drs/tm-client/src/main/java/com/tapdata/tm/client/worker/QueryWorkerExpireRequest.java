package com.tapdata.tm.client.worker;

import com.tapdata.manager.common.http.MethodType;
import com.tapdata.manager.common.http.region.ProductDomain;
import com.tapdata.tm.client.BaseRequest;

public class QueryWorkerExpireRequest extends BaseRequest<QueryWorkerExpireResponse> {
    public QueryWorkerExpireRequest(ProductDomain productDomain) {
        super(productDomain, "/api/Workers/share/get");
        this.setMethod(MethodType.GET);
        this.putBodyParameter("isTCM", true);
    }

    @Override
    public Class<QueryWorkerExpireResponse> getResponseClass() {
        return QueryWorkerExpireResponse.class;
    }
}
