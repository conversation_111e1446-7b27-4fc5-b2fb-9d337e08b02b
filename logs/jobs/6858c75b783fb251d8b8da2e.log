[TRACE] 2025-06-23 11:19:53.749 - [任务 22] - Task initialization... 
[TRACE] 2025-06-23 11:19:53.749 - [任务 22] - Start task milestones: 6858c75b783fb251d8b8da2e(任务 22) 
[INFO ] 2025-06-23 11:19:53.749 - [任务 22] - Loading table structure completed 
[TRACE] 2025-06-23 11:19:53.993 - [任务 22] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-06-23 11:19:53.993 - [任务 22] - The engine receives 任务 22 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 11:19:54.143 - [任务 22] - Task started 
[TRACE] 2025-06-23 11:19:54.143 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:19:54.143 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:19:54.144 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:19:54.144 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:19:54.145 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:19:54.145 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:19:54.145 - [任务 22][表编辑] - Node table_rename_processor(表编辑: f5ba094b-b94d-4d22-892f-1397dbd13132) enable batch process 
[TRACE] 2025-06-23 11:19:54.146 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:19:54.146 - [任务 22][字段编辑] - Node migrate_field_rename_processor(字段编辑: edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea) enable batch process 
[TRACE] 2025-06-23 11:19:54.146 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 11:19:54.768 - [任务 22][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-23 11:19:54.768 - [任务 22][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 11:19:54.768 - [任务 22][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 11:19:54.805 - [任务 22][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-23 11:19:54.805 - [任务 22][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 11:19:54.806 - [任务 22][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-23 11:19:54.806 - [任务 22][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-23 11:19:54.806 - [任务 22][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-23 11:19:55.012 - [任务 22][sqlserver_ad] - building CT table for table RefreshToken 
[WARN ] 2025-06-23 11:19:55.176 - [任务 22][sqlserver_ad] - auto build CT table for table RefreshToken failed, please check 
[INFO ] 2025-06-23 11:19:55.177 - [任务 22][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"0000004E0001164B0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-23 11:19:55.227 - [任务 22][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-23 11:19:55.237 - [任务 22][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-23 11:19:55.237 - [任务 22][sqlserver_ad] - Starting batch read from table: RefreshToken 
[TRACE] 2025-06-23 11:19:55.295 - [任务 22][sqlserver_ad] - Table RefreshToken is going to be initial synced 
[TRACE] 2025-06-23 11:19:55.295 - [任务 22][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(8f792f7e-3142-4784-a6ae-4f099bc64374) 
[INFO ] 2025-06-23 11:19:55.445 - [任务 22][sqlserver_ad] - Table RefreshToken has been completed batch read 
[TRACE] 2025-06-23 11:19:55.447 - [任务 22][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-23 11:19:55.447 - [任务 22][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-23 11:19:55.448 - [任务 22][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-23 11:19:55.448 - [任务 22][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-23 11:19:55.450 - [任务 22][sqlserver_ad] - Starting stream read, table list: [RefreshToken], offset: {"currentStartLSN":"0000004E0001164B0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-23 11:19:55.450 - [任务 22][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-23 11:19:55.694 - [任务 22][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-23 11:19:55.694 - [任务 22][sqlserver_ad] - building CT table for table RefreshToken 
[WARN ] 2025-06-23 11:19:55.849 - [任务 22][sqlserver_ad] - build CT table for table RefreshToken failed 
[WARN ] 2025-06-23 11:19:55.849 - [任务 22][sqlserver_ad] - Cdc is not enabled for Table RefreshToken, cannot perform Cdc operation. 
[TRACE] 2025-06-23 11:19:57.323 - [任务 22][local_pg] - Process after table "refreshtoken" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-23 11:19:57.323 - [任务 22][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-23 11:20:23.901 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] running status set to false 
[TRACE] 2025-06-23 11:20:24.417 - [任务 22][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-23 11:20:26.926 - [任务 22][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_8f792f7e-3142-4784-a6ae-4f099bc64374_1750648794431 
[TRACE] 2025-06-23 11:20:26.926 - [任务 22][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_8f792f7e-3142-4784-a6ae-4f099bc64374_1750648794431 
[TRACE] 2025-06-23 11:20:26.929 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] schema data cleaned 
[TRACE] 2025-06-23 11:20:26.934 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] monitor closed 
[TRACE] 2025-06-23 11:20:26.935 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] close complete, cost 3030 ms 
[TRACE] 2025-06-23 11:20:26.935 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] running status set to false 
[TRACE] 2025-06-23 11:20:26.935 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] schema data cleaned 
[TRACE] 2025-06-23 11:20:26.935 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] monitor closed 
[TRACE] 2025-06-23 11:20:26.936 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] close complete, cost 3 ms 
[TRACE] 2025-06-23 11:20:26.936 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] running status set to false 
[TRACE] 2025-06-23 11:20:26.937 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] schema data cleaned 
[TRACE] 2025-06-23 11:20:26.937 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] monitor closed 
[TRACE] 2025-06-23 11:20:26.938 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] close complete, cost 1 ms 
[TRACE] 2025-06-23 11:20:26.938 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] running status set to false 
[TRACE] 2025-06-23 11:20:26.952 - [任务 22][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_50b4c5a7-fe31-45a9-94fe-e26288067d2a_1750648794452 
[TRACE] 2025-06-23 11:20:26.952 - [任务 22][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_50b4c5a7-fe31-45a9-94fe-e26288067d2a_1750648794452 
[TRACE] 2025-06-23 11:20:26.952 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] schema data cleaned 
[TRACE] 2025-06-23 11:20:26.953 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] monitor closed 
[TRACE] 2025-06-23 11:20:26.953 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] close complete, cost 15 ms 
[TRACE] 2025-06-23 11:20:35.089 - [任务 22] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 11:20:36.095 - [任务 22] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@1068868f 
[TRACE] 2025-06-23 11:20:36.097 - [任务 22] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3a4feae4 
[TRACE] 2025-06-23 11:20:36.097 - [任务 22] - Stop task milestones: 6858c75b783fb251d8b8da2e(任务 22)  
[TRACE] 2025-06-23 11:20:36.213 - [任务 22] - Stopped task aspect(s) 
[TRACE] 2025-06-23 11:20:36.214 - [任务 22] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 11:20:36.264 - [任务 22] - Task stopped. 
[TRACE] 2025-06-23 11:20:36.264 - [任务 22] - Remove memory task client succeed, task: 任务 22[6858c75b783fb251d8b8da2e] 
[TRACE] 2025-06-23 11:20:36.264 - [任务 22] - Destroy memory task client cache succeed, task: 任务 22[6858c75b783fb251d8b8da2e] 
[TRACE] 2025-06-23 11:34:22.552 - [任务 22] - Task initialization... 
[TRACE] 2025-06-23 11:34:22.553 - [任务 22] - Start task milestones: 6858c75b783fb251d8b8da2e(任务 22) 
[INFO ] 2025-06-23 11:34:22.696 - [任务 22] - Loading table structure completed 
[TRACE] 2025-06-23 11:34:22.696 - [任务 22] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-23 11:34:22.740 - [任务 22] - The engine receives 任务 22 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 11:34:22.740 - [任务 22] - Task started 
[TRACE] 2025-06-23 11:34:22.760 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:34:22.761 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:34:22.765 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:34:22.765 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:34:22.766 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:34:22.770 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:34:22.770 - [任务 22][表编辑] - Node table_rename_processor(表编辑: f5ba094b-b94d-4d22-892f-1397dbd13132) enable batch process 
[TRACE] 2025-06-23 11:34:22.770 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:34:22.770 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:34:22.770 - [任务 22][字段编辑] - Node migrate_field_rename_processor(字段编辑: edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea) enable batch process 
[INFO ] 2025-06-23 11:34:23.196 - [任务 22][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-23 11:34:23.196 - [任务 22][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 11:34:23.196 - [任务 22][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 11:34:23.208 - [任务 22][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-23 11:34:23.410 - [任务 22][local_pg] - The table refreshtoken has already exist. 
[INFO ] 2025-06-23 11:34:23.429 - [任务 22][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 11:34:23.429 - [任务 22][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-23 11:34:23.429 - [任务 22][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-23 11:34:23.429 - [任务 22][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-23 11:34:23.587 - [任务 22][sqlserver_ad] - building CT table for table RefreshToken 
[WARN ] 2025-06-23 11:34:23.587 - [任务 22][sqlserver_ad] - auto build CT table for table RefreshToken failed, please check 
[INFO ] 2025-06-23 11:34:23.757 - [任务 22][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"0000004E000117A50001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-23 11:34:23.758 - [任务 22][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-23 11:34:23.761 - [任务 22][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-23 11:34:23.761 - [任务 22][sqlserver_ad] - Starting batch read from table: RefreshToken 
[TRACE] 2025-06-23 11:34:23.841 - [任务 22][sqlserver_ad] - Table RefreshToken is going to be initial synced 
[TRACE] 2025-06-23 11:34:23.841 - [任务 22][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(8f792f7e-3142-4784-a6ae-4f099bc64374) 
[INFO ] 2025-06-23 11:34:23.958 - [任务 22][sqlserver_ad] - Table RefreshToken has been completed batch read 
[TRACE] 2025-06-23 11:34:23.959 - [任务 22][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-23 11:34:23.959 - [任务 22][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-23 11:34:23.959 - [任务 22][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-23 11:34:23.959 - [任务 22][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-23 11:34:23.959 - [任务 22][sqlserver_ad] - Starting stream read, table list: [RefreshToken], offset: {"currentStartLSN":"0000004E000117A50001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-23 11:34:23.959 - [任务 22][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-23 11:34:24.188 - [任务 22][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-23 11:34:24.188 - [任务 22][sqlserver_ad] - building CT table for table RefreshToken 
[WARN ] 2025-06-23 11:34:24.271 - [任务 22][sqlserver_ad] - build CT table for table RefreshToken failed 
[WARN ] 2025-06-23 11:34:24.271 - [任务 22][sqlserver_ad] - Cdc is not enabled for Table RefreshToken, cannot perform Cdc operation. 
[TRACE] 2025-06-23 11:34:26.237 - [任务 22][local_pg] - Process after table "refreshtoken" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-23 11:34:26.237 - [任务 22][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-23 11:46:53.246 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] running status set to false 
[TRACE] 2025-06-23 11:46:53.277 - [任务 22][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-23 11:46:56.261 - [任务 22][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_8f792f7e-3142-4784-a6ae-4f099bc64374_1750649663075 
[TRACE] 2025-06-23 11:46:56.261 - [任务 22][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_8f792f7e-3142-4784-a6ae-4f099bc64374_1750649663075 
[TRACE] 2025-06-23 11:46:56.262 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] schema data cleaned 
[TRACE] 2025-06-23 11:46:56.263 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] monitor closed 
[TRACE] 2025-06-23 11:46:56.263 - [任务 22][sqlserver_ad] - Node sqlserver_ad[8f792f7e-3142-4784-a6ae-4f099bc64374] close complete, cost 3017 ms 
[TRACE] 2025-06-23 11:46:56.263 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] running status set to false 
[TRACE] 2025-06-23 11:46:56.263 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] schema data cleaned 
[TRACE] 2025-06-23 11:46:56.264 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] monitor closed 
[TRACE] 2025-06-23 11:46:56.264 - [任务 22][表编辑] - Node 表编辑[f5ba094b-b94d-4d22-892f-1397dbd13132] close complete, cost 0 ms 
[TRACE] 2025-06-23 11:46:56.264 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] running status set to false 
[TRACE] 2025-06-23 11:46:56.264 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] schema data cleaned 
[TRACE] 2025-06-23 11:46:56.264 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] monitor closed 
[TRACE] 2025-06-23 11:46:56.265 - [任务 22][字段编辑] - Node 字段编辑[edd6ed75-56ef-4e1c-a5e9-ccc7b2d4fdea] close complete, cost 0 ms 
[TRACE] 2025-06-23 11:46:56.265 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] running status set to false 
[TRACE] 2025-06-23 11:46:56.278 - [任务 22][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_50b4c5a7-fe31-45a9-94fe-e26288067d2a_1750649663060 
[TRACE] 2025-06-23 11:46:56.278 - [任务 22][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_50b4c5a7-fe31-45a9-94fe-e26288067d2a_1750649663060 
[TRACE] 2025-06-23 11:46:56.278 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] schema data cleaned 
[TRACE] 2025-06-23 11:46:56.278 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] monitor closed 
[TRACE] 2025-06-23 11:46:56.481 - [任务 22][local_pg] - Node local_pg[50b4c5a7-fe31-45a9-94fe-e26288067d2a] close complete, cost 13 ms 
[TRACE] 2025-06-23 11:47:03.763 - [任务 22] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 11:47:04.620 - [任务 22] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@6efdc504 
[TRACE] 2025-06-23 11:47:04.620 - [任务 22] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@567f82bc 
[TRACE] 2025-06-23 11:47:04.737 - [任务 22] - Stop task milestones: 6858c75b783fb251d8b8da2e(任务 22)  
[TRACE] 2025-06-23 11:47:04.737 - [任务 22] - Stopped task aspect(s) 
[TRACE] 2025-06-23 11:47:04.737 - [任务 22] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 11:47:04.738 - [任务 22] - Task stopped. 
[TRACE] 2025-06-23 11:47:04.782 - [任务 22] - Remove memory task client succeed, task: 任务 22[6858c75b783fb251d8b8da2e] 
[TRACE] 2025-06-23 11:47:04.782 - [任务 22] - Destroy memory task client cache succeed, task: 任务 22[6858c75b783fb251d8b8da2e] 
