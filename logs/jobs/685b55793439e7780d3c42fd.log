[TRACE] 2025-06-25 09:49:13.434 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 09:49:13.434 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 09:49:14.053 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 09:49:14.237 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 09:49:14.283 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 09:49:14.488 - [任务 24] - Task started 
[TRACE] 2025-06-25 09:49:14.515 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 09:49:14.515 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 09:49:14.515 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 09:49:14.515 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 09:49:14.718 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 09:49:14.964 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 09:49:14.964 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 09:49:14.964 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 09:49:15.025 - [任务 24][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-25 09:49:15.025 - [任务 24][local_pg] - The table table_50 has already exist. 
[TRACE] 2025-06-25 09:49:15.072 - [任务 24][local_pg] - The table table_10 has already exist. 
[INFO ] 2025-06-25 09:49:15.072 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 09:49:15.073 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 09:49:15.073 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[TRACE] 2025-06-25 09:49:15.073 - [任务 24][local_pg - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-25 09:49:15.095 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 09:49:15.113 - [任务 24][local_pg - Copy] - new logical replication slot created, slotName:tapdata_cdc_7c82b195_c72d_4c2c_ba4b_c0b8a317286e 
[TRACE] 2025-06-25 09:49:15.114 - [任务 24][local_pg] - The table table_11 has already exist. 
[INFO ] 2025-06-25 09:49:15.149 - [任务 24][local_pg - Copy] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-06-25 09:49:15.149 - [任务 24][local_pg] - The table table_12 has already exist. 
[TRACE] 2025-06-25 09:49:15.219 - [任务 24][local_pg] - The table table_13 has already exist. 
[TRACE] 2025-06-25 09:49:15.219 - [任务 24][local_pg] - The table table_14 has already exist. 
[TRACE] 2025-06-25 09:49:15.266 - [任务 24][local_pg] - The table table_15 has already exist. 
[INFO ] 2025-06-25 09:49:15.266 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 09:49:15.277 - [任务 24][local_pg - Copy] - Initial sync started 
[INFO ] 2025-06-25 09:49:15.280 - [任务 24][local_pg - Copy] - Starting batch read from table: table_50 
[TRACE] 2025-06-25 09:49:15.280 - [任务 24][local_pg - Copy] - Table table_50 is going to be initial synced 
[TRACE] 2025-06-25 09:49:15.343 - [任务 24][local_pg] - The table table_16 has already exist. 
[TRACE] 2025-06-25 09:49:15.344 - [任务 24][local_pg] - The table table_17 has already exist. 
[TRACE] 2025-06-25 09:49:15.423 - [任务 24][local_pg] - The table table_19 has already exist. 
[TRACE] 2025-06-25 09:49:15.423 - [任务 24][local_pg] - The table table_40 has already exist. 
[TRACE] 2025-06-25 09:49:15.484 - [任务 24][local_pg] - The table table_41 has already exist. 
[TRACE] 2025-06-25 09:49:15.484 - [任务 24][local_pg] - The table table_42 has already exist. 
[TRACE] 2025-06-25 09:49:15.545 - [任务 24][local_pg] - The table table_43 has already exist. 
[TRACE] 2025-06-25 09:49:15.545 - [任务 24][local_pg] - The table table_44 has already exist. 
[TRACE] 2025-06-25 09:49:15.615 - [任务 24][local_pg] - The table table_45 has already exist. 
[TRACE] 2025-06-25 09:49:15.615 - [任务 24][local_pg] - The table table_46 has already exist. 
[TRACE] 2025-06-25 09:49:15.684 - [任务 24][local_pg] - The table table_47 has already exist. 
[TRACE] 2025-06-25 09:49:15.684 - [任务 24][local_pg] - The table table_48 has already exist. 
[TRACE] 2025-06-25 09:49:15.831 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 09:49:15.832 - [任务 24][local_pg] - The table table_49 has already exist. 
[TRACE] 2025-06-25 09:49:15.924 - [任务 24][local_pg] - The table table_30 has already exist. 
[TRACE] 2025-06-25 09:49:16.093 - [任务 24][local_pg] - The table table_31 has already exist. 
[TRACE] 2025-06-25 09:49:16.093 - [任务 24][local_pg] - The table table_32 has already exist. 
[TRACE] 2025-06-25 09:49:16.153 - [任务 24][local_pg] - The table table_33 has already exist. 
[TRACE] 2025-06-25 09:49:16.153 - [任务 24][local_pg] - The table table_34 has already exist. 
[TRACE] 2025-06-25 09:49:16.216 - [任务 24][local_pg] - The table table_35 has already exist. 
[TRACE] 2025-06-25 09:49:16.216 - [任务 24][local_pg] - The table table_36 has already exist. 
[TRACE] 2025-06-25 09:49:16.281 - [任务 24][local_pg] - The table table_37 has already exist. 
[TRACE] 2025-06-25 09:49:16.281 - [任务 24][local_pg] - The table table_38 has already exist. 
[TRACE] 2025-06-25 09:49:16.365 - [任务 24][local_pg] - The table table_39 has already exist. 
[TRACE] 2025-06-25 09:49:16.366 - [任务 24][local_pg] - The table table_20 has already exist. 
[TRACE] 2025-06-25 09:49:16.430 - [任务 24][local_pg] - The table table_21 has already exist. 
[TRACE] 2025-06-25 09:49:16.430 - [任务 24][local_pg] - The table table_1 has already exist. 
[TRACE] 2025-06-25 09:49:16.500 - [任务 24][local_pg] - The table table_22 has already exist. 
[TRACE] 2025-06-25 09:49:16.500 - [任务 24][local_pg] - The table table_2 has already exist. 
[TRACE] 2025-06-25 09:49:16.578 - [任务 24][local_pg] - The table table_3 has already exist. 
[TRACE] 2025-06-25 09:49:16.578 - [任务 24][local_pg] - The table table_23 has already exist. 
[TRACE] 2025-06-25 09:49:16.645 - [任务 24][local_pg] - The table table_4 has already exist. 
[TRACE] 2025-06-25 09:49:16.645 - [任务 24][local_pg] - The table table_24 has already exist. 
[TRACE] 2025-06-25 09:49:16.708 - [任务 24][local_pg] - The table table_25 has already exist. 
[TRACE] 2025-06-25 09:49:16.708 - [任务 24][local_pg] - The table table_5 has already exist. 
[TRACE] 2025-06-25 09:49:16.771 - [任务 24][local_pg] - The table table_26 has already exist. 
[TRACE] 2025-06-25 09:49:16.771 - [任务 24][local_pg] - The table table_6 has already exist. 
[TRACE] 2025-06-25 09:49:16.848 - [任务 24][local_pg] - The table table_27 has already exist. 
[TRACE] 2025-06-25 09:49:16.848 - [任务 24][local_pg] - The table table_7 has already exist. 
[TRACE] 2025-06-25 09:49:16.911 - [任务 24][local_pg] - The table table_28 has already exist. 
[TRACE] 2025-06-25 09:49:16.911 - [任务 24][local_pg] - The table table_8 has already exist. 
[TRACE] 2025-06-25 09:49:16.978 - [任务 24][local_pg] - The table table_29 has already exist. 
[TRACE] 2025-06-25 09:49:16.978 - [任务 24][local_pg] - The table table_9 has already exist. 
[INFO ] 2025-06-25 09:49:18.172 - [任务 24][local_pg - Copy] - Table table_50 has been completed batch read 
[INFO ] 2025-06-25 09:49:18.177 - [任务 24][local_pg - Copy] - Starting batch read from table: table_10 
[TRACE] 2025-06-25 09:49:18.178 - [任务 24][local_pg - Copy] - Table table_10 is going to be initial synced 
[INFO ] 2025-06-25 09:49:18.581 - [任务 24][local_pg - Copy] - Table table_10 has been completed batch read 
[INFO ] 2025-06-25 09:49:18.581 - [任务 24][local_pg - Copy] - Starting batch read from table: table_11 
[TRACE] 2025-06-25 09:49:18.582 - [任务 24][local_pg - Copy] - Table table_11 is going to be initial synced 
[INFO ] 2025-06-25 09:49:19.278 - [任务 24][local_pg - Copy] - Table table_11 has been completed batch read 
[INFO ] 2025-06-25 09:49:19.279 - [任务 24][local_pg - Copy] - Starting batch read from table: table_12 
[TRACE] 2025-06-25 09:49:19.279 - [任务 24][local_pg - Copy] - Table table_12 is going to be initial synced 
[INFO ] 2025-06-25 09:49:20.028 - [任务 24][local_pg - Copy] - Table table_12 has been completed batch read 
[INFO ] 2025-06-25 09:49:20.028 - [任务 24][local_pg - Copy] - Starting batch read from table: table_13 
[TRACE] 2025-06-25 09:49:20.028 - [任务 24][local_pg - Copy] - Table table_13 is going to be initial synced 
[INFO ] 2025-06-25 09:49:20.475 - [任务 24][local_pg - Copy] - Table table_13 has been completed batch read 
[INFO ] 2025-06-25 09:49:20.476 - [任务 24][local_pg - Copy] - Starting batch read from table: table_14 
[TRACE] 2025-06-25 09:49:20.476 - [任务 24][local_pg - Copy] - Table table_14 is going to be initial synced 
[INFO ] 2025-06-25 09:49:21.340 - [任务 24][local_pg - Copy] - Table table_14 has been completed batch read 
[INFO ] 2025-06-25 09:49:21.340 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 09:49:21.340 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[INFO ] 2025-06-25 09:49:22.268 - [任务 24][local_pg - Copy] - Table table_15 has been completed batch read 
[INFO ] 2025-06-25 09:49:22.269 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 09:49:22.269 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[INFO ] 2025-06-25 09:49:23.405 - [任务 24][local_pg - Copy] - Table table_16 has been completed batch read 
[INFO ] 2025-06-25 09:49:23.405 - [任务 24][local_pg - Copy] - Starting batch read from table: table_17 
[TRACE] 2025-06-25 09:49:23.405 - [任务 24][local_pg - Copy] - Table table_17 is going to be initial synced 
[INFO ] 2025-06-25 09:49:24.617 - [任务 24][local_pg - Copy] - Table table_17 has been completed batch read 
[INFO ] 2025-06-25 09:49:24.617 - [任务 24][local_pg - Copy] - Starting batch read from table: table_19 
[TRACE] 2025-06-25 09:49:24.617 - [任务 24][local_pg - Copy] - Table table_19 is going to be initial synced 
[INFO ] 2025-06-25 09:49:25.489 - [任务 24][local_pg - Copy] - Table table_19 has been completed batch read 
[INFO ] 2025-06-25 09:49:25.489 - [任务 24][local_pg - Copy] - Starting batch read from table: table_40 
[TRACE] 2025-06-25 09:49:25.489 - [任务 24][local_pg - Copy] - Table table_40 is going to be initial synced 
[INFO ] 2025-06-25 09:49:26.133 - [任务 24][local_pg - Copy] - Table table_40 has been completed batch read 
[INFO ] 2025-06-25 09:49:26.134 - [任务 24][local_pg - Copy] - Starting batch read from table: table_41 
[TRACE] 2025-06-25 09:49:26.134 - [任务 24][local_pg - Copy] - Table table_41 is going to be initial synced 
[INFO ] 2025-06-25 09:49:26.945 - [任务 24][local_pg - Copy] - Table table_41 has been completed batch read 
[INFO ] 2025-06-25 09:49:26.945 - [任务 24][local_pg - Copy] - Starting batch read from table: table_42 
[TRACE] 2025-06-25 09:49:27.149 - [任务 24][local_pg - Copy] - Table table_42 is going to be initial synced 
[INFO ] 2025-06-25 09:49:27.467 - [任务 24][local_pg - Copy] - Table table_42 has been completed batch read 
[INFO ] 2025-06-25 09:49:27.467 - [任务 24][local_pg - Copy] - Starting batch read from table: table_43 
[TRACE] 2025-06-25 09:49:27.468 - [任务 24][local_pg - Copy] - Table table_43 is going to be initial synced 
[INFO ] 2025-06-25 09:49:28.087 - [任务 24][local_pg - Copy] - Table table_43 has been completed batch read 
[INFO ] 2025-06-25 09:49:28.087 - [任务 24][local_pg - Copy] - Starting batch read from table: table_44 
[TRACE] 2025-06-25 09:49:28.087 - [任务 24][local_pg - Copy] - Table table_44 is going to be initial synced 
[INFO ] 2025-06-25 09:49:28.609 - [任务 24][local_pg - Copy] - Table table_44 has been completed batch read 
[INFO ] 2025-06-25 09:49:28.609 - [任务 24][local_pg - Copy] - Starting batch read from table: table_45 
[TRACE] 2025-06-25 09:49:28.609 - [任务 24][local_pg - Copy] - Table table_45 is going to be initial synced 
[INFO ] 2025-06-25 09:49:29.221 - [任务 24][local_pg - Copy] - Table table_45 has been completed batch read 
[INFO ] 2025-06-25 09:49:29.222 - [任务 24][local_pg - Copy] - Starting batch read from table: table_46 
[TRACE] 2025-06-25 09:49:29.222 - [任务 24][local_pg - Copy] - Table table_46 is going to be initial synced 
[INFO ] 2025-06-25 09:49:30.360 - [任务 24][local_pg - Copy] - Table table_46 has been completed batch read 
[INFO ] 2025-06-25 09:49:30.360 - [任务 24][local_pg - Copy] - Starting batch read from table: table_47 
[TRACE] 2025-06-25 09:49:30.360 - [任务 24][local_pg - Copy] - Table table_47 is going to be initial synced 
[INFO ] 2025-06-25 09:49:31.180 - [任务 24][local_pg - Copy] - Table table_47 has been completed batch read 
[INFO ] 2025-06-25 09:49:31.182 - [任务 24][local_pg - Copy] - Starting batch read from table: table_48 
[TRACE] 2025-06-25 09:49:31.182 - [任务 24][local_pg - Copy] - Table table_48 is going to be initial synced 
[INFO ] 2025-06-25 09:49:32.107 - [任务 24][local_pg - Copy] - Table table_48 has been completed batch read 
[INFO ] 2025-06-25 09:49:32.107 - [任务 24][local_pg - Copy] - Starting batch read from table: table_49 
[TRACE] 2025-06-25 09:49:32.107 - [任务 24][local_pg - Copy] - Table table_49 is going to be initial synced 
[INFO ] 2025-06-25 09:49:33.080 - [任务 24][local_pg - Copy] - Table table_49 has been completed batch read 
[INFO ] 2025-06-25 09:49:33.081 - [任务 24][local_pg - Copy] - Starting batch read from table: table_30 
[TRACE] 2025-06-25 09:49:33.081 - [任务 24][local_pg - Copy] - Table table_30 is going to be initial synced 
[INFO ] 2025-06-25 09:49:34.123 - [任务 24][local_pg - Copy] - Table table_30 has been completed batch read 
[INFO ] 2025-06-25 09:49:34.124 - [任务 24][local_pg - Copy] - Starting batch read from table: table_31 
[TRACE] 2025-06-25 09:49:34.124 - [任务 24][local_pg - Copy] - Table table_31 is going to be initial synced 
[INFO ] 2025-06-25 09:49:34.611 - [任务 24][local_pg - Copy] - Table table_31 has been completed batch read 
[INFO ] 2025-06-25 09:49:34.611 - [任务 24][local_pg - Copy] - Starting batch read from table: table_32 
[TRACE] 2025-06-25 09:49:34.611 - [任务 24][local_pg - Copy] - Table table_32 is going to be initial synced 
[INFO ] 2025-06-25 09:49:35.400 - [任务 24][local_pg - Copy] - Table table_32 has been completed batch read 
[INFO ] 2025-06-25 09:49:35.400 - [任务 24][local_pg - Copy] - Starting batch read from table: table_33 
[TRACE] 2025-06-25 09:49:35.400 - [任务 24][local_pg - Copy] - Table table_33 is going to be initial synced 
[INFO ] 2025-06-25 09:49:35.972 - [任务 24][local_pg - Copy] - Table table_33 has been completed batch read 
[INFO ] 2025-06-25 09:49:35.972 - [任务 24][local_pg - Copy] - Starting batch read from table: table_34 
[TRACE] 2025-06-25 09:49:35.972 - [任务 24][local_pg - Copy] - Table table_34 is going to be initial synced 
[INFO ] 2025-06-25 09:49:36.625 - [任务 24][local_pg - Copy] - Table table_34 has been completed batch read 
[INFO ] 2025-06-25 09:49:36.625 - [任务 24][local_pg - Copy] - Starting batch read from table: table_35 
[TRACE] 2025-06-25 09:49:36.625 - [任务 24][local_pg - Copy] - Table table_35 is going to be initial synced 
[INFO ] 2025-06-25 09:49:37.338 - [任务 24][local_pg - Copy] - Table table_35 has been completed batch read 
[INFO ] 2025-06-25 09:49:37.338 - [任务 24][local_pg - Copy] - Starting batch read from table: table_36 
[TRACE] 2025-06-25 09:49:37.338 - [任务 24][local_pg - Copy] - Table table_36 is going to be initial synced 
[INFO ] 2025-06-25 09:49:38.266 - [任务 24][local_pg - Copy] - Table table_36 has been completed batch read 
[INFO ] 2025-06-25 09:49:38.266 - [任务 24][local_pg - Copy] - Starting batch read from table: table_37 
[TRACE] 2025-06-25 09:49:38.266 - [任务 24][local_pg - Copy] - Table table_37 is going to be initial synced 
[INFO ] 2025-06-25 09:49:38.896 - [任务 24][local_pg - Copy] - Table table_37 has been completed batch read 
[INFO ] 2025-06-25 09:49:38.896 - [任务 24][local_pg - Copy] - Starting batch read from table: table_38 
[TRACE] 2025-06-25 09:49:38.897 - [任务 24][local_pg - Copy] - Table table_38 is going to be initial synced 
[INFO ] 2025-06-25 09:49:39.674 - [任务 24][local_pg - Copy] - Table table_38 has been completed batch read 
[INFO ] 2025-06-25 09:49:39.675 - [任务 24][local_pg - Copy] - Starting batch read from table: table_39 
[TRACE] 2025-06-25 09:49:39.675 - [任务 24][local_pg - Copy] - Table table_39 is going to be initial synced 
[INFO ] 2025-06-25 09:49:40.100 - [任务 24][local_pg - Copy] - Table table_39 has been completed batch read 
[INFO ] 2025-06-25 09:49:40.100 - [任务 24][local_pg - Copy] - Starting batch read from table: table_20 
[TRACE] 2025-06-25 09:49:40.302 - [任务 24][local_pg - Copy] - Table table_20 is going to be initial synced 
[INFO ] 2025-06-25 09:49:41.176 - [任务 24][local_pg - Copy] - Table table_20 has been completed batch read 
[INFO ] 2025-06-25 09:49:41.176 - [任务 24][local_pg - Copy] - Starting batch read from table: table_21 
[TRACE] 2025-06-25 09:49:41.176 - [任务 24][local_pg - Copy] - Table table_21 is going to be initial synced 
[INFO ] 2025-06-25 09:49:41.679 - [任务 24][local_pg - Copy] - Table table_21 has been completed batch read 
[INFO ] 2025-06-25 09:49:41.679 - [任务 24][local_pg - Copy] - Starting batch read from table: table_1 
[TRACE] 2025-06-25 09:49:41.679 - [任务 24][local_pg - Copy] - Table table_1 is going to be initial synced 
[INFO ] 2025-06-25 09:49:43.461 - [任务 24][local_pg - Copy] - Table table_1 has been completed batch read 
[INFO ] 2025-06-25 09:49:43.461 - [任务 24][local_pg - Copy] - Starting batch read from table: table_22 
[TRACE] 2025-06-25 09:49:43.461 - [任务 24][local_pg - Copy] - Table table_22 is going to be initial synced 
[INFO ] 2025-06-25 09:49:44.156 - [任务 24][local_pg - Copy] - Table table_22 has been completed batch read 
[INFO ] 2025-06-25 09:49:44.156 - [任务 24][local_pg - Copy] - Starting batch read from table: table_2 
[TRACE] 2025-06-25 09:49:44.157 - [任务 24][local_pg - Copy] - Table table_2 is going to be initial synced 
[INFO ] 2025-06-25 09:49:45.087 - [任务 24][local_pg - Copy] - Table table_2 has been completed batch read 
[INFO ] 2025-06-25 09:49:45.087 - [任务 24][local_pg - Copy] - Starting batch read from table: table_3 
[TRACE] 2025-06-25 09:49:45.087 - [任务 24][local_pg - Copy] - Table table_3 is going to be initial synced 
[INFO ] 2025-06-25 09:49:46.129 - [任务 24][local_pg - Copy] - Table table_3 has been completed batch read 
[INFO ] 2025-06-25 09:49:46.129 - [任务 24][local_pg - Copy] - Starting batch read from table: table_23 
[TRACE] 2025-06-25 09:49:46.130 - [任务 24][local_pg - Copy] - Table table_23 is going to be initial synced 
[INFO ] 2025-06-25 09:49:47.074 - [任务 24][local_pg - Copy] - Table table_23 has been completed batch read 
[INFO ] 2025-06-25 09:49:47.075 - [任务 24][local_pg - Copy] - Starting batch read from table: table_4 
[TRACE] 2025-06-25 09:49:47.075 - [任务 24][local_pg - Copy] - Table table_4 is going to be initial synced 
[INFO ] 2025-06-25 09:49:47.674 - [任务 24][local_pg - Copy] - Table table_4 has been completed batch read 
[INFO ] 2025-06-25 09:49:47.674 - [任务 24][local_pg - Copy] - Starting batch read from table: table_24 
[TRACE] 2025-06-25 09:49:47.674 - [任务 24][local_pg - Copy] - Table table_24 is going to be initial synced 
[INFO ] 2025-06-25 09:49:48.562 - [任务 24][local_pg - Copy] - Table table_24 has been completed batch read 
[INFO ] 2025-06-25 09:49:48.563 - [任务 24][local_pg - Copy] - Starting batch read from table: table_25 
[TRACE] 2025-06-25 09:49:48.563 - [任务 24][local_pg - Copy] - Table table_25 is going to be initial synced 
[INFO ] 2025-06-25 09:49:49.603 - [任务 24][local_pg - Copy] - Table table_25 has been completed batch read 
[INFO ] 2025-06-25 09:49:49.603 - [任务 24][local_pg - Copy] - Starting batch read from table: table_5 
[TRACE] 2025-06-25 09:49:49.603 - [任务 24][local_pg - Copy] - Table table_5 is going to be initial synced 
[INFO ] 2025-06-25 09:49:50.600 - [任务 24][local_pg - Copy] - Table table_5 has been completed batch read 
[INFO ] 2025-06-25 09:49:50.600 - [任务 24][local_pg - Copy] - Starting batch read from table: table_26 
[TRACE] 2025-06-25 09:49:50.600 - [任务 24][local_pg - Copy] - Table table_26 is going to be initial synced 
[INFO ] 2025-06-25 09:49:51.745 - [任务 24][local_pg - Copy] - Table table_26 has been completed batch read 
[INFO ] 2025-06-25 09:49:51.745 - [任务 24][local_pg - Copy] - Starting batch read from table: table_6 
[TRACE] 2025-06-25 09:49:51.745 - [任务 24][local_pg - Copy] - Table table_6 is going to be initial synced 
[INFO ] 2025-06-25 09:49:52.906 - [任务 24][local_pg - Copy] - Table table_6 has been completed batch read 
[INFO ] 2025-06-25 09:49:52.906 - [任务 24][local_pg - Copy] - Starting batch read from table: table_27 
[TRACE] 2025-06-25 09:49:52.906 - [任务 24][local_pg - Copy] - Table table_27 is going to be initial synced 
[INFO ] 2025-06-25 09:49:53.354 - [任务 24][local_pg - Copy] - Table table_27 has been completed batch read 
[INFO ] 2025-06-25 09:49:53.354 - [任务 24][local_pg - Copy] - Starting batch read from table: table_7 
[TRACE] 2025-06-25 09:49:53.354 - [任务 24][local_pg - Copy] - Table table_7 is going to be initial synced 
[INFO ] 2025-06-25 09:49:54.048 - [任务 24][local_pg - Copy] - Table table_7 has been completed batch read 
[INFO ] 2025-06-25 09:49:54.048 - [任务 24][local_pg - Copy] - Starting batch read from table: table_28 
[TRACE] 2025-06-25 09:49:54.048 - [任务 24][local_pg - Copy] - Table table_28 is going to be initial synced 
[INFO ] 2025-06-25 09:49:54.523 - [任务 24][local_pg - Copy] - Table table_28 has been completed batch read 
[INFO ] 2025-06-25 09:49:54.524 - [任务 24][local_pg - Copy] - Starting batch read from table: table_8 
[TRACE] 2025-06-25 09:49:54.524 - [任务 24][local_pg - Copy] - Table table_8 is going to be initial synced 
[INFO ] 2025-06-25 09:49:55.471 - [任务 24][local_pg - Copy] - Table table_8 has been completed batch read 
[INFO ] 2025-06-25 09:49:55.471 - [任务 24][local_pg - Copy] - Starting batch read from table: table_29 
[TRACE] 2025-06-25 09:49:55.471 - [任务 24][local_pg - Copy] - Table table_29 is going to be initial synced 
[INFO ] 2025-06-25 09:49:55.881 - [任务 24][local_pg - Copy] - Table table_29 has been completed batch read 
[INFO ] 2025-06-25 09:49:55.882 - [任务 24][local_pg - Copy] - Starting batch read from table: table_9 
[TRACE] 2025-06-25 09:49:55.882 - [任务 24][local_pg - Copy] - Table table_9 is going to be initial synced 
[INFO ] 2025-06-25 09:49:56.430 - [任务 24][local_pg - Copy] - Table table_9 has been completed batch read 
[TRACE] 2025-06-25 09:49:56.430 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 09:49:56.430 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 09:49:56.431 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 09:49:56.431 - [任务 24][local_pg - Copy] - Initial sync completed 
[TRACE] 2025-06-25 09:49:56.433 - [任务 24][local_pg - Copy] - Starting stream read, table list: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 09:49:56.433 - [任务 24][local_pg - Copy] - Starting incremental sync using database log parser 
[WARN ] 2025-06-25 09:49:56.437 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 09:49:56.612 - [任务 24][local_pg - Copy] - Using an existing logical replication slot, slotName:tapdata_cdc_7c82b195_c72d_4c2c_ba4b_c0b8a317286e 
[TRACE] 2025-06-25 09:49:56.612 - [任务 24][local_pg - Copy] - Connector PostgreSQL incremental start succeed, tables: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], data change syncing 
[TRACE] 2025-06-25 09:49:57.455 - [任务 24][local_pg] - Process after table "table_19" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_11" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_14" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_13" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_10" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_17" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_41" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_42" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_16" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_12" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_44" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_47" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_48" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_50" initial sync finished, cost: 1 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_46" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_33" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_43" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.456 - [任务 24][local_pg] - Process after table "table_45" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_40" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_15" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_30" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_31" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_35" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_49" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_34" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_36" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_38" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_37" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_21" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_39" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_5" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.457 - [任务 24][local_pg] - Process after table "table_3" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_32" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_24" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_1" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_26" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_4" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_7" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_6" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_8" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_20" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_9" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_29" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_23" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_28" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_25" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_22" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.458 - [任务 24][local_pg] - Process after table "table_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:49:57.459 - [任务 24][local_pg] - Process after table "table_27" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-25 09:49:57.662 - [任务 24][local_pg] - Process after all table(s) initial sync are finished，table number: 49 
[TRACE] 2025-06-25 09:50:28.809 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 09:50:28.810 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 09:50:28.820 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816154830 
[TRACE] 2025-06-25 09:50:28.820 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816154830 
[TRACE] 2025-06-25 09:50:28.821 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 09:50:28.821 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 09:50:28.823 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 193 ms 
[TRACE] 2025-06-25 09:50:28.823 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 09:50:28.830 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816154812 
[TRACE] 2025-06-25 09:50:28.830 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816154812 
[TRACE] 2025-06-25 09:50:28.830 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 09:50:28.830 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 09:50:28.831 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 7 ms 
[TRACE] 2025-06-25 09:50:37.752 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 09:50:38.583 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@29134f39 
[TRACE] 2025-06-25 09:50:38.583 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@cdfef26 
[TRACE] 2025-06-25 09:50:38.701 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 09:50:38.701 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 09:50:38.702 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 09:50:38.702 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 09:50:38.745 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 09:50:38.748 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 09:52:46.443 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 09:52:46.443 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 09:52:46.790 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 09:52:46.870 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 09:52:46.870 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 09:52:46.945 - [任务 24] - Task started 
[TRACE] 2025-06-25 09:52:46.945 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 09:52:46.945 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 09:52:46.945 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 09:52:46.946 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 09:52:46.946 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 09:52:47.361 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 09:52:47.361 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 09:52:47.361 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[TRACE] 2025-06-25 09:52:47.361 - [任务 24][local_pg - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-25 09:52:47.385 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 09:52:47.385 - [任务 24][local_pg - Copy] - new logical replication slot created, slotName:tapdata_cdc_b3c51800_6992_41e2_8b09_09ebd22a871f 
[INFO ] 2025-06-25 09:52:47.482 - [任务 24][local_pg - Copy] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 09:52:47.482 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 09:52:47.482 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 09:52:47.482 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 09:52:47.504 - [任务 24][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-25 09:52:47.504 - [任务 24][local_pg] - The table table_50 has already exist. 
[TRACE] 2025-06-25 09:52:47.550 - [任务 24][local_pg] - The table table_10 has already exist. 
[INFO ] 2025-06-25 09:52:47.550 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 09:52:47.554 - [任务 24][local_pg - Copy] - Initial sync started 
[INFO ] 2025-06-25 09:52:47.554 - [任务 24][local_pg - Copy] - Starting batch read from table: table_50 
[TRACE] 2025-06-25 09:52:47.554 - [任务 24][local_pg - Copy] - Table table_50 is going to be initial synced 
[TRACE] 2025-06-25 09:52:47.600 - [任务 24][local_pg] - The table table_11 has already exist. 
[TRACE] 2025-06-25 09:52:47.600 - [任务 24][local_pg] - The table table_12 has already exist. 
[TRACE] 2025-06-25 09:52:47.659 - [任务 24][local_pg] - The table table_13 has already exist. 
[TRACE] 2025-06-25 09:52:47.659 - [任务 24][local_pg] - The table table_14 has already exist. 
[TRACE] 2025-06-25 09:52:47.725 - [任务 24][local_pg] - The table table_15 has already exist. 
[TRACE] 2025-06-25 09:52:47.726 - [任务 24][local_pg] - The table table_16 has already exist. 
[TRACE] 2025-06-25 09:52:47.786 - [任务 24][local_pg] - The table table_17 has already exist. 
[TRACE] 2025-06-25 09:52:47.786 - [任务 24][local_pg] - The table table_19 has already exist. 
[TRACE] 2025-06-25 09:52:47.854 - [任务 24][local_pg] - The table table_40 has already exist. 
[TRACE] 2025-06-25 09:52:47.854 - [任务 24][local_pg] - The table table_41 has already exist. 
[TRACE] 2025-06-25 09:52:47.923 - [任务 24][local_pg] - The table table_42 has already exist. 
[TRACE] 2025-06-25 09:52:47.923 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 09:52:47.969 - [任务 24][local_pg] - The table table_43 has already exist. 
[TRACE] 2025-06-25 09:52:47.969 - [任务 24][local_pg] - The table table_44 has already exist. 
[TRACE] 2025-06-25 09:52:48.028 - [任务 24][local_pg] - The table table_45 has already exist. 
[TRACE] 2025-06-25 09:52:48.028 - [任务 24][local_pg] - The table table_46 has already exist. 
[TRACE] 2025-06-25 09:52:48.092 - [任务 24][local_pg] - The table table_47 has already exist. 
[TRACE] 2025-06-25 09:52:48.092 - [任务 24][local_pg] - The table table_48 has already exist. 
[TRACE] 2025-06-25 09:52:48.155 - [任务 24][local_pg] - The table table_49 has already exist. 
[TRACE] 2025-06-25 09:52:48.156 - [任务 24][local_pg] - The table table_30 has already exist. 
[TRACE] 2025-06-25 09:52:48.234 - [任务 24][local_pg] - The table table_31 has already exist. 
[TRACE] 2025-06-25 09:52:48.234 - [任务 24][local_pg] - The table table_32 has already exist. 
[TRACE] 2025-06-25 09:52:48.295 - [任务 24][local_pg] - The table table_33 has already exist. 
[TRACE] 2025-06-25 09:52:48.295 - [任务 24][local_pg] - The table table_34 has already exist. 
[TRACE] 2025-06-25 09:52:48.354 - [任务 24][local_pg] - The table table_35 has already exist. 
[TRACE] 2025-06-25 09:52:48.354 - [任务 24][local_pg] - The table table_36 has already exist. 
[TRACE] 2025-06-25 09:52:48.414 - [任务 24][local_pg] - The table table_37 has already exist. 
[TRACE] 2025-06-25 09:52:48.414 - [任务 24][local_pg] - The table table_38 has already exist. 
[TRACE] 2025-06-25 09:52:48.476 - [任务 24][local_pg] - The table table_39 has already exist. 
[TRACE] 2025-06-25 09:52:48.476 - [任务 24][local_pg] - The table table_20 has already exist. 
[TRACE] 2025-06-25 09:52:48.539 - [任务 24][local_pg] - The table table_21 has already exist. 
[TRACE] 2025-06-25 09:52:48.539 - [任务 24][local_pg] - The table table_1 has already exist. 
[TRACE] 2025-06-25 09:52:48.600 - [任务 24][local_pg] - The table table_22 has already exist. 
[TRACE] 2025-06-25 09:52:48.601 - [任务 24][local_pg] - The table table_2 has already exist. 
[TRACE] 2025-06-25 09:52:48.660 - [任务 24][local_pg] - The table table_3 has already exist. 
[TRACE] 2025-06-25 09:52:48.660 - [任务 24][local_pg] - The table table_23 has already exist. 
[TRACE] 2025-06-25 09:52:48.720 - [任务 24][local_pg] - The table table_4 has already exist. 
[TRACE] 2025-06-25 09:52:48.720 - [任务 24][local_pg] - The table table_24 has already exist. 
[TRACE] 2025-06-25 09:52:48.781 - [任务 24][local_pg] - The table table_25 has already exist. 
[TRACE] 2025-06-25 09:52:48.781 - [任务 24][local_pg] - The table table_5 has already exist. 
[TRACE] 2025-06-25 09:52:48.841 - [任务 24][local_pg] - The table table_26 has already exist. 
[TRACE] 2025-06-25 09:52:48.841 - [任务 24][local_pg] - The table table_6 has already exist. 
[TRACE] 2025-06-25 09:52:48.904 - [任务 24][local_pg] - The table table_27 has already exist. 
[TRACE] 2025-06-25 09:52:48.904 - [任务 24][local_pg] - The table table_7 has already exist. 
[TRACE] 2025-06-25 09:52:48.961 - [任务 24][local_pg] - The table table_28 has already exist. 
[TRACE] 2025-06-25 09:52:48.961 - [任务 24][local_pg] - The table table_8 has already exist. 
[TRACE] 2025-06-25 09:52:49.031 - [任务 24][local_pg] - The table table_29 has already exist. 
[TRACE] 2025-06-25 09:52:49.031 - [任务 24][local_pg] - The table table_9 has already exist. 
[INFO ] 2025-06-25 09:52:49.882 - [任务 24][local_pg - Copy] - Table table_50 has been completed batch read 
[INFO ] 2025-06-25 09:52:49.882 - [任务 24][local_pg - Copy] - Starting batch read from table: table_10 
[TRACE] 2025-06-25 09:52:49.882 - [任务 24][local_pg - Copy] - Table table_10 is going to be initial synced 
[INFO ] 2025-06-25 09:52:50.299 - [任务 24][local_pg - Copy] - Table table_10 has been completed batch read 
[INFO ] 2025-06-25 09:52:50.299 - [任务 24][local_pg - Copy] - Starting batch read from table: table_11 
[TRACE] 2025-06-25 09:52:50.501 - [任务 24][local_pg - Copy] - Table table_11 is going to be initial synced 
[INFO ] 2025-06-25 09:52:50.936 - [任务 24][local_pg - Copy] - Table table_11 has been completed batch read 
[INFO ] 2025-06-25 09:52:50.936 - [任务 24][local_pg - Copy] - Starting batch read from table: table_12 
[TRACE] 2025-06-25 09:52:50.936 - [任务 24][local_pg - Copy] - Table table_12 is going to be initial synced 
[INFO ] 2025-06-25 09:52:51.711 - [任务 24][local_pg - Copy] - Table table_12 has been completed batch read 
[INFO ] 2025-06-25 09:52:51.711 - [任务 24][local_pg - Copy] - Starting batch read from table: table_13 
[TRACE] 2025-06-25 09:52:51.711 - [任务 24][local_pg - Copy] - Table table_13 is going to be initial synced 
[INFO ] 2025-06-25 09:52:52.207 - [任务 24][local_pg - Copy] - Table table_13 has been completed batch read 
[INFO ] 2025-06-25 09:52:52.207 - [任务 24][local_pg - Copy] - Starting batch read from table: table_14 
[TRACE] 2025-06-25 09:52:52.207 - [任务 24][local_pg - Copy] - Table table_14 is going to be initial synced 
[INFO ] 2025-06-25 09:52:53.080 - [任务 24][local_pg - Copy] - Table table_14 has been completed batch read 
[INFO ] 2025-06-25 09:52:53.080 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 09:52:53.080 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[INFO ] 2025-06-25 09:52:53.911 - [任务 24][local_pg - Copy] - Table table_15 has been completed batch read 
[INFO ] 2025-06-25 09:52:53.911 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 09:52:53.912 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[INFO ] 2025-06-25 09:52:55.032 - [任务 24][local_pg - Copy] - Table table_16 has been completed batch read 
[INFO ] 2025-06-25 09:52:55.032 - [任务 24][local_pg - Copy] - Starting batch read from table: table_17 
[TRACE] 2025-06-25 09:52:55.032 - [任务 24][local_pg - Copy] - Table table_17 is going to be initial synced 
[INFO ] 2025-06-25 09:52:55.984 - [任务 24][local_pg - Copy] - Table table_17 has been completed batch read 
[INFO ] 2025-06-25 09:52:55.984 - [任务 24][local_pg - Copy] - Starting batch read from table: table_19 
[TRACE] 2025-06-25 09:52:55.984 - [任务 24][local_pg - Copy] - Table table_19 is going to be initial synced 
[INFO ] 2025-06-25 09:52:56.679 - [任务 24][local_pg - Copy] - Table table_19 has been completed batch read 
[INFO ] 2025-06-25 09:52:56.679 - [任务 24][local_pg - Copy] - Starting batch read from table: table_40 
[TRACE] 2025-06-25 09:52:56.679 - [任务 24][local_pg - Copy] - Table table_40 is going to be initial synced 
[INFO ] 2025-06-25 09:52:57.318 - [任务 24][local_pg - Copy] - Table table_40 has been completed batch read 
[INFO ] 2025-06-25 09:52:57.319 - [任务 24][local_pg - Copy] - Starting batch read from table: table_41 
[TRACE] 2025-06-25 09:52:57.319 - [任务 24][local_pg - Copy] - Table table_41 is going to be initial synced 
[INFO ] 2025-06-25 09:52:58.100 - [任务 24][local_pg - Copy] - Table table_41 has been completed batch read 
[INFO ] 2025-06-25 09:52:58.101 - [任务 24][local_pg - Copy] - Starting batch read from table: table_42 
[TRACE] 2025-06-25 09:52:58.101 - [任务 24][local_pg - Copy] - Table table_42 is going to be initial synced 
[INFO ] 2025-06-25 09:52:58.615 - [任务 24][local_pg - Copy] - Table table_42 has been completed batch read 
[INFO ] 2025-06-25 09:52:58.615 - [任务 24][local_pg - Copy] - Starting batch read from table: table_43 
[TRACE] 2025-06-25 09:52:58.816 - [任务 24][local_pg - Copy] - Table table_43 is going to be initial synced 
[INFO ] 2025-06-25 09:52:59.177 - [任务 24][local_pg - Copy] - Table table_43 has been completed batch read 
[INFO ] 2025-06-25 09:52:59.177 - [任务 24][local_pg - Copy] - Starting batch read from table: table_44 
[TRACE] 2025-06-25 09:52:59.177 - [任务 24][local_pg - Copy] - Table table_44 is going to be initial synced 
[INFO ] 2025-06-25 09:52:59.694 - [任务 24][local_pg - Copy] - Table table_44 has been completed batch read 
[INFO ] 2025-06-25 09:52:59.694 - [任务 24][local_pg - Copy] - Starting batch read from table: table_45 
[TRACE] 2025-06-25 09:52:59.694 - [任务 24][local_pg - Copy] - Table table_45 is going to be initial synced 
[INFO ] 2025-06-25 09:53:00.316 - [任务 24][local_pg - Copy] - Table table_45 has been completed batch read 
[INFO ] 2025-06-25 09:53:00.316 - [任务 24][local_pg - Copy] - Starting batch read from table: table_46 
[TRACE] 2025-06-25 09:53:00.316 - [任务 24][local_pg - Copy] - Table table_46 is going to be initial synced 
[INFO ] 2025-06-25 09:53:01.460 - [任务 24][local_pg - Copy] - Table table_46 has been completed batch read 
[INFO ] 2025-06-25 09:53:01.460 - [任务 24][local_pg - Copy] - Starting batch read from table: table_47 
[TRACE] 2025-06-25 09:53:01.460 - [任务 24][local_pg - Copy] - Table table_47 is going to be initial synced 
[INFO ] 2025-06-25 09:53:02.135 - [任务 24][local_pg - Copy] - Table table_47 has been completed batch read 
[INFO ] 2025-06-25 09:53:02.136 - [任务 24][local_pg - Copy] - Starting batch read from table: table_48 
[TRACE] 2025-06-25 09:53:02.136 - [任务 24][local_pg - Copy] - Table table_48 is going to be initial synced 
[INFO ] 2025-06-25 09:53:03.221 - [任务 24][local_pg - Copy] - Table table_48 has been completed batch read 
[INFO ] 2025-06-25 09:53:03.221 - [任务 24][local_pg - Copy] - Starting batch read from table: table_49 
[TRACE] 2025-06-25 09:53:03.221 - [任务 24][local_pg - Copy] - Table table_49 is going to be initial synced 
[INFO ] 2025-06-25 09:53:04.201 - [任务 24][local_pg - Copy] - Table table_49 has been completed batch read 
[INFO ] 2025-06-25 09:53:04.201 - [任务 24][local_pg - Copy] - Starting batch read from table: table_30 
[TRACE] 2025-06-25 09:53:04.201 - [任务 24][local_pg - Copy] - Table table_30 is going to be initial synced 
[INFO ] 2025-06-25 09:53:05.253 - [任务 24][local_pg - Copy] - Table table_30 has been completed batch read 
[INFO ] 2025-06-25 09:53:05.253 - [任务 24][local_pg - Copy] - Starting batch read from table: table_31 
[TRACE] 2025-06-25 09:53:05.253 - [任务 24][local_pg - Copy] - Table table_31 is going to be initial synced 
[INFO ] 2025-06-25 09:53:05.744 - [任务 24][local_pg - Copy] - Table table_31 has been completed batch read 
[INFO ] 2025-06-25 09:53:05.744 - [任务 24][local_pg - Copy] - Starting batch read from table: table_32 
[TRACE] 2025-06-25 09:53:05.744 - [任务 24][local_pg - Copy] - Table table_32 is going to be initial synced 
[INFO ] 2025-06-25 09:53:06.540 - [任务 24][local_pg - Copy] - Table table_32 has been completed batch read 
[INFO ] 2025-06-25 09:53:06.540 - [任务 24][local_pg - Copy] - Starting batch read from table: table_33 
[TRACE] 2025-06-25 09:53:06.540 - [任务 24][local_pg - Copy] - Table table_33 is going to be initial synced 
[INFO ] 2025-06-25 09:53:07.130 - [任务 24][local_pg - Copy] - Table table_33 has been completed batch read 
[INFO ] 2025-06-25 09:53:07.131 - [任务 24][local_pg - Copy] - Starting batch read from table: table_34 
[TRACE] 2025-06-25 09:53:07.131 - [任务 24][local_pg - Copy] - Table table_34 is going to be initial synced 
[INFO ] 2025-06-25 09:53:07.840 - [任务 24][local_pg - Copy] - Table table_34 has been completed batch read 
[INFO ] 2025-06-25 09:53:07.840 - [任务 24][local_pg - Copy] - Starting batch read from table: table_35 
[TRACE] 2025-06-25 09:53:07.840 - [任务 24][local_pg - Copy] - Table table_35 is going to be initial synced 
[INFO ] 2025-06-25 09:53:08.568 - [任务 24][local_pg - Copy] - Table table_35 has been completed batch read 
[INFO ] 2025-06-25 09:53:08.568 - [任务 24][local_pg - Copy] - Starting batch read from table: table_36 
[TRACE] 2025-06-25 09:53:08.568 - [任务 24][local_pg - Copy] - Table table_36 is going to be initial synced 
[INFO ] 2025-06-25 09:53:09.488 - [任务 24][local_pg - Copy] - Table table_36 has been completed batch read 
[INFO ] 2025-06-25 09:53:09.488 - [任务 24][local_pg - Copy] - Starting batch read from table: table_37 
[TRACE] 2025-06-25 09:53:09.488 - [任务 24][local_pg - Copy] - Table table_37 is going to be initial synced 
[INFO ] 2025-06-25 09:53:10.131 - [任务 24][local_pg - Copy] - Table table_37 has been completed batch read 
[INFO ] 2025-06-25 09:53:10.131 - [任务 24][local_pg - Copy] - Starting batch read from table: table_38 
[TRACE] 2025-06-25 09:53:10.131 - [任务 24][local_pg - Copy] - Table table_38 is going to be initial synced 
[INFO ] 2025-06-25 09:53:10.939 - [任务 24][local_pg - Copy] - Table table_38 has been completed batch read 
[INFO ] 2025-06-25 09:53:10.940 - [任务 24][local_pg - Copy] - Starting batch read from table: table_39 
[TRACE] 2025-06-25 09:53:10.940 - [任务 24][local_pg - Copy] - Table table_39 is going to be initial synced 
[INFO ] 2025-06-25 09:53:11.384 - [任务 24][local_pg - Copy] - Table table_39 has been completed batch read 
[INFO ] 2025-06-25 09:53:11.384 - [任务 24][local_pg - Copy] - Starting batch read from table: table_20 
[TRACE] 2025-06-25 09:53:11.384 - [任务 24][local_pg - Copy] - Table table_20 is going to be initial synced 
[INFO ] 2025-06-25 09:53:12.455 - [任务 24][local_pg - Copy] - Table table_20 has been completed batch read 
[INFO ] 2025-06-25 09:53:12.456 - [任务 24][local_pg - Copy] - Starting batch read from table: table_21 
[TRACE] 2025-06-25 09:53:12.456 - [任务 24][local_pg - Copy] - Table table_21 is going to be initial synced 
[INFO ] 2025-06-25 09:53:12.964 - [任务 24][local_pg - Copy] - Table table_21 has been completed batch read 
[INFO ] 2025-06-25 09:53:12.964 - [任务 24][local_pg - Copy] - Starting batch read from table: table_1 
[TRACE] 2025-06-25 09:53:12.964 - [任务 24][local_pg - Copy] - Table table_1 is going to be initial synced 
[INFO ] 2025-06-25 09:53:13.931 - [任务 24][local_pg - Copy] - Table table_1 has been completed batch read 
[INFO ] 2025-06-25 09:53:13.932 - [任务 24][local_pg - Copy] - Starting batch read from table: table_22 
[TRACE] 2025-06-25 09:53:13.932 - [任务 24][local_pg - Copy] - Table table_22 is going to be initial synced 
[INFO ] 2025-06-25 09:53:14.621 - [任务 24][local_pg - Copy] - Table table_22 has been completed batch read 
[INFO ] 2025-06-25 09:53:14.621 - [任务 24][local_pg - Copy] - Starting batch read from table: table_2 
[TRACE] 2025-06-25 09:53:14.622 - [任务 24][local_pg - Copy] - Table table_2 is going to be initial synced 
[INFO ] 2025-06-25 09:53:15.522 - [任务 24][local_pg - Copy] - Table table_2 has been completed batch read 
[INFO ] 2025-06-25 09:53:15.522 - [任务 24][local_pg - Copy] - Starting batch read from table: table_3 
[TRACE] 2025-06-25 09:53:15.522 - [任务 24][local_pg - Copy] - Table table_3 is going to be initial synced 
[INFO ] 2025-06-25 09:53:16.559 - [任务 24][local_pg - Copy] - Table table_3 has been completed batch read 
[INFO ] 2025-06-25 09:53:16.560 - [任务 24][local_pg - Copy] - Starting batch read from table: table_23 
[TRACE] 2025-06-25 09:53:16.764 - [任务 24][local_pg - Copy] - Table table_23 is going to be initial synced 
[INFO ] 2025-06-25 09:53:17.529 - [任务 24][local_pg - Copy] - Table table_23 has been completed batch read 
[INFO ] 2025-06-25 09:53:17.529 - [任务 24][local_pg - Copy] - Starting batch read from table: table_4 
[TRACE] 2025-06-25 09:53:17.529 - [任务 24][local_pg - Copy] - Table table_4 is going to be initial synced 
[INFO ] 2025-06-25 09:53:18.080 - [任务 24][local_pg - Copy] - Table table_4 has been completed batch read 
[INFO ] 2025-06-25 09:53:18.080 - [任务 24][local_pg - Copy] - Starting batch read from table: table_24 
[TRACE] 2025-06-25 09:53:18.080 - [任务 24][local_pg - Copy] - Table table_24 is going to be initial synced 
[INFO ] 2025-06-25 09:53:18.960 - [任务 24][local_pg - Copy] - Table table_24 has been completed batch read 
[INFO ] 2025-06-25 09:53:18.960 - [任务 24][local_pg - Copy] - Starting batch read from table: table_25 
[TRACE] 2025-06-25 09:53:18.960 - [任务 24][local_pg - Copy] - Table table_25 is going to be initial synced 
[INFO ] 2025-06-25 09:53:19.972 - [任务 24][local_pg - Copy] - Table table_25 has been completed batch read 
[INFO ] 2025-06-25 09:53:19.974 - [任务 24][local_pg - Copy] - Starting batch read from table: table_5 
[TRACE] 2025-06-25 09:53:19.974 - [任务 24][local_pg - Copy] - Table table_5 is going to be initial synced 
[INFO ] 2025-06-25 09:53:20.947 - [任务 24][local_pg - Copy] - Table table_5 has been completed batch read 
[INFO ] 2025-06-25 09:53:20.947 - [任务 24][local_pg - Copy] - Starting batch read from table: table_26 
[TRACE] 2025-06-25 09:53:20.947 - [任务 24][local_pg - Copy] - Table table_26 is going to be initial synced 
[INFO ] 2025-06-25 09:53:22.063 - [任务 24][local_pg - Copy] - Table table_26 has been completed batch read 
[INFO ] 2025-06-25 09:53:22.063 - [任务 24][local_pg - Copy] - Starting batch read from table: table_6 
[TRACE] 2025-06-25 09:53:22.063 - [任务 24][local_pg - Copy] - Table table_6 is going to be initial synced 
[INFO ] 2025-06-25 09:53:23.153 - [任务 24][local_pg - Copy] - Table table_6 has been completed batch read 
[INFO ] 2025-06-25 09:53:23.154 - [任务 24][local_pg - Copy] - Starting batch read from table: table_27 
[TRACE] 2025-06-25 09:53:23.154 - [任务 24][local_pg - Copy] - Table table_27 is going to be initial synced 
[INFO ] 2025-06-25 09:53:23.587 - [任务 24][local_pg - Copy] - Table table_27 has been completed batch read 
[INFO ] 2025-06-25 09:53:23.587 - [任务 24][local_pg - Copy] - Starting batch read from table: table_7 
[TRACE] 2025-06-25 09:53:23.587 - [任务 24][local_pg - Copy] - Table table_7 is going to be initial synced 
[INFO ] 2025-06-25 09:53:24.381 - [任务 24][local_pg - Copy] - Table table_7 has been completed batch read 
[INFO ] 2025-06-25 09:53:24.381 - [任务 24][local_pg - Copy] - Starting batch read from table: table_28 
[TRACE] 2025-06-25 09:53:24.381 - [任务 24][local_pg - Copy] - Table table_28 is going to be initial synced 
[INFO ] 2025-06-25 09:53:24.843 - [任务 24][local_pg - Copy] - Table table_28 has been completed batch read 
[INFO ] 2025-06-25 09:53:24.843 - [任务 24][local_pg - Copy] - Starting batch read from table: table_8 
[TRACE] 2025-06-25 09:53:24.843 - [任务 24][local_pg - Copy] - Table table_8 is going to be initial synced 
[INFO ] 2025-06-25 09:53:25.776 - [任务 24][local_pg - Copy] - Table table_8 has been completed batch read 
[INFO ] 2025-06-25 09:53:25.776 - [任务 24][local_pg - Copy] - Starting batch read from table: table_29 
[TRACE] 2025-06-25 09:53:25.776 - [任务 24][local_pg - Copy] - Table table_29 is going to be initial synced 
[INFO ] 2025-06-25 09:53:26.168 - [任务 24][local_pg - Copy] - Table table_29 has been completed batch read 
[INFO ] 2025-06-25 09:53:26.168 - [任务 24][local_pg - Copy] - Starting batch read from table: table_9 
[TRACE] 2025-06-25 09:53:26.168 - [任务 24][local_pg - Copy] - Table table_9 is going to be initial synced 
[INFO ] 2025-06-25 09:53:26.719 - [任务 24][local_pg - Copy] - Table table_9 has been completed batch read 
[TRACE] 2025-06-25 09:53:26.719 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 09:53:26.719 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 09:53:26.719 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 09:53:26.719 - [任务 24][local_pg - Copy] - Initial sync completed 
[TRACE] 2025-06-25 09:53:26.719 - [任务 24][local_pg - Copy] - Starting stream read, table list: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 09:53:26.719 - [任务 24][local_pg - Copy] - Starting incremental sync using database log parser 
[WARN ] 2025-06-25 09:53:26.719 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 09:53:26.756 - [任务 24][local_pg - Copy] - Using an existing logical replication slot, slotName:tapdata_cdc_b3c51800_6992_41e2_8b09_09ebd22a871f 
[TRACE] 2025-06-25 09:53:26.756 - [任务 24][local_pg - Copy] - Connector PostgreSQL incremental start succeed, tables: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], data change syncing 
[TRACE] 2025-06-25 09:53:27.234 - [任务 24][local_pg] - Process after table "table_10" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_14" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_12" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_50" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_13" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_16" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_42" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_11" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_43" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_41" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_15" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_45" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_44" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_47" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_48" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.235 - [任务 24][local_pg] - Process after table "table_40" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_49" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_31" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_32" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_33" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_46" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_19" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_35" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_34" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_30" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_21" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_37" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_36" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_22" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_17" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_39" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_38" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_23" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_5" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_20" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_24" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_25" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_1" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_3" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_26" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_28" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_8" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_4" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_7" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.236 - [任务 24][local_pg] - Process after table "table_9" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.237 - [任务 24][local_pg] - Process after table "table_27" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.237 - [任务 24][local_pg] - Process after table "table_29" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 09:53:27.237 - [任务 24][local_pg] - Process after table "table_6" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-25 09:53:27.237 - [任务 24][local_pg] - Process after all table(s) initial sync are finished，table number: 49 
[TRACE] 2025-06-25 09:53:42.585 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 09:53:42.638 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 09:53:42.641 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816367216 
[TRACE] 2025-06-25 09:53:42.641 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816367216 
[TRACE] 2025-06-25 09:53:42.641 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 09:53:42.641 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 09:53:42.642 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 57 ms 
[TRACE] 2025-06-25 09:53:42.642 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 09:53:42.650 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816367248 
[TRACE] 2025-06-25 09:53:42.650 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816367248 
[TRACE] 2025-06-25 09:53:42.650 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 09:53:42.651 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 09:53:42.651 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 8 ms 
[TRACE] 2025-06-25 09:53:48.950 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 09:53:49.896 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@504fe883 
[TRACE] 2025-06-25 09:53:49.896 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@44c82349 
[TRACE] 2025-06-25 09:53:50.013 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 09:53:50.014 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 09:53:50.014 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 09:53:50.014 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 09:53:50.049 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 09:53:50.053 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 09:56:13.159 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 09:56:13.160 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 09:56:13.513 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 09:56:13.583 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 09:56:13.677 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 09:56:13.678 - [任务 24] - Task started 
[TRACE] 2025-06-25 09:56:13.727 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 09:56:13.728 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 09:56:13.728 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 09:56:13.728 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 09:56:13.729 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 09:56:14.160 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 09:56:14.161 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 09:56:14.161 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[TRACE] 2025-06-25 09:56:14.161 - [任务 24][local_pg - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-25 09:56:14.188 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 09:56:14.207 - [任务 24][local_pg - Copy] - new logical replication slot created, slotName:tapdata_cdc_ca14b0fd_4e02_4da9_9704_cf2e92e7b1c0 
[INFO ] 2025-06-25 09:56:14.208 - [任务 24][local_pg - Copy] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 09:56:14.300 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 09:56:14.301 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 09:56:14.302 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 09:56:14.323 - [任务 24][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-25 09:56:14.323 - [任务 24][local_pg] - The table table_50 has already exist. 
[TRACE] 2025-06-25 09:56:14.368 - [任务 24][local_pg] - The table table_10 has already exist. 
[INFO ] 2025-06-25 09:56:14.368 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 09:56:14.376 - [任务 24][local_pg - Copy] - Initial sync started 
[INFO ] 2025-06-25 09:56:14.380 - [任务 24][local_pg - Copy] - Starting batch read from table: table_50 
[TRACE] 2025-06-25 09:56:14.380 - [任务 24][local_pg - Copy] - Table table_50 is going to be initial synced 
[TRACE] 2025-06-25 09:56:14.446 - [任务 24][local_pg] - The table table_11 has already exist. 
[TRACE] 2025-06-25 09:56:14.446 - [任务 24][local_pg] - The table table_12 has already exist. 
[TRACE] 2025-06-25 09:56:14.523 - [任务 24][local_pg] - The table table_13 has already exist. 
[TRACE] 2025-06-25 09:56:14.523 - [任务 24][local_pg] - The table table_14 has already exist. 
[TRACE] 2025-06-25 09:56:14.598 - [任务 24][local_pg] - The table table_15 has already exist. 
[TRACE] 2025-06-25 09:56:14.599 - [任务 24][local_pg] - The table table_16 has already exist. 
[TRACE] 2025-06-25 09:56:14.687 - [任务 24][local_pg] - The table table_17 has already exist. 
[TRACE] 2025-06-25 09:56:14.687 - [任务 24][local_pg] - The table table_19 has already exist. 
[TRACE] 2025-06-25 09:56:14.826 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 09:56:14.826 - [任务 24][local_pg] - The table table_40 has already exist. 
[TRACE] 2025-06-25 09:56:14.892 - [任务 24][local_pg] - The table table_41 has already exist. 
[TRACE] 2025-06-25 09:56:14.892 - [任务 24][local_pg] - The table table_42 has already exist. 
[TRACE] 2025-06-25 09:56:14.958 - [任务 24][local_pg] - The table table_43 has already exist. 
[TRACE] 2025-06-25 09:56:14.959 - [任务 24][local_pg] - The table table_44 has already exist. 
[TRACE] 2025-06-25 09:56:15.025 - [任务 24][local_pg] - The table table_45 has already exist. 
[TRACE] 2025-06-25 09:56:15.025 - [任务 24][local_pg] - The table table_46 has already exist. 
[TRACE] 2025-06-25 09:56:15.092 - [任务 24][local_pg] - The table table_47 has already exist. 
[TRACE] 2025-06-25 09:56:15.092 - [任务 24][local_pg] - The table table_48 has already exist. 
[TRACE] 2025-06-25 09:56:15.258 - [任务 24][local_pg] - The table table_49 has already exist. 
[TRACE] 2025-06-25 09:56:15.258 - [任务 24][local_pg] - The table table_30 has already exist. 
[TRACE] 2025-06-25 09:56:15.336 - [任务 24][local_pg] - The table table_31 has already exist. 
[TRACE] 2025-06-25 09:56:15.336 - [任务 24][local_pg] - The table table_32 has already exist. 
[TRACE] 2025-06-25 09:56:15.429 - [任务 24][local_pg] - The table table_33 has already exist. 
[TRACE] 2025-06-25 09:56:15.429 - [任务 24][local_pg] - The table table_34 has already exist. 
[TRACE] 2025-06-25 09:56:15.495 - [任务 24][local_pg] - The table table_35 has already exist. 
[TRACE] 2025-06-25 09:56:15.496 - [任务 24][local_pg] - The table table_36 has already exist. 
[TRACE] 2025-06-25 09:56:15.558 - [任务 24][local_pg] - The table table_37 has already exist. 
[TRACE] 2025-06-25 09:56:15.558 - [任务 24][local_pg] - The table table_38 has already exist. 
[TRACE] 2025-06-25 09:56:15.622 - [任务 24][local_pg] - The table table_39 has already exist. 
[TRACE] 2025-06-25 09:56:15.622 - [任务 24][local_pg] - The table table_20 has already exist. 
[TRACE] 2025-06-25 09:56:15.689 - [任务 24][local_pg] - The table table_21 has already exist. 
[TRACE] 2025-06-25 09:56:15.689 - [任务 24][local_pg] - The table table_1 has already exist. 
[TRACE] 2025-06-25 09:56:15.758 - [任务 24][local_pg] - The table table_22 has already exist. 
[TRACE] 2025-06-25 09:56:15.758 - [任务 24][local_pg] - The table table_2 has already exist. 
[TRACE] 2025-06-25 09:56:15.826 - [任务 24][local_pg] - The table table_3 has already exist. 
[TRACE] 2025-06-25 09:56:15.827 - [任务 24][local_pg] - The table table_23 has already exist. 
[TRACE] 2025-06-25 09:56:15.890 - [任务 24][local_pg] - The table table_4 has already exist. 
[TRACE] 2025-06-25 09:56:15.890 - [任务 24][local_pg] - The table table_24 has already exist. 
[TRACE] 2025-06-25 09:56:15.957 - [任务 24][local_pg] - The table table_25 has already exist. 
[TRACE] 2025-06-25 09:56:15.957 - [任务 24][local_pg] - The table table_5 has already exist. 
[TRACE] 2025-06-25 09:56:16.022 - [任务 24][local_pg] - The table table_26 has already exist. 
[TRACE] 2025-06-25 09:56:16.022 - [任务 24][local_pg] - The table table_6 has already exist. 
[TRACE] 2025-06-25 09:56:16.089 - [任务 24][local_pg] - The table table_27 has already exist. 
[TRACE] 2025-06-25 09:56:16.090 - [任务 24][local_pg] - The table table_7 has already exist. 
[TRACE] 2025-06-25 09:56:16.157 - [任务 24][local_pg] - The table table_28 has already exist. 
[TRACE] 2025-06-25 09:56:16.157 - [任务 24][local_pg] - The table table_8 has already exist. 
[TRACE] 2025-06-25 09:56:16.220 - [任务 24][local_pg] - The table table_29 has already exist. 
[TRACE] 2025-06-25 09:56:16.220 - [任务 24][local_pg] - The table table_9 has already exist. 
[INFO ] 2025-06-25 09:56:17.591 - [任务 24][local_pg - Copy] - Table table_50 has been completed batch read 
[INFO ] 2025-06-25 09:56:17.591 - [任务 24][local_pg - Copy] - Starting batch read from table: table_10 
[TRACE] 2025-06-25 09:56:17.591 - [任务 24][local_pg - Copy] - Table table_10 is going to be initial synced 
[INFO ] 2025-06-25 09:56:18.004 - [任务 24][local_pg - Copy] - Table table_10 has been completed batch read 
[INFO ] 2025-06-25 09:56:18.004 - [任务 24][local_pg - Copy] - Starting batch read from table: table_11 
[TRACE] 2025-06-25 09:56:18.004 - [任务 24][local_pg - Copy] - Table table_11 is going to be initial synced 
[INFO ] 2025-06-25 09:56:18.687 - [任务 24][local_pg - Copy] - Table table_11 has been completed batch read 
[INFO ] 2025-06-25 09:56:18.688 - [任务 24][local_pg - Copy] - Starting batch read from table: table_12 
[TRACE] 2025-06-25 09:56:18.688 - [任务 24][local_pg - Copy] - Table table_12 is going to be initial synced 
[INFO ] 2025-06-25 09:56:19.462 - [任务 24][local_pg - Copy] - Table table_12 has been completed batch read 
[INFO ] 2025-06-25 09:56:19.463 - [任务 24][local_pg - Copy] - Starting batch read from table: table_13 
[TRACE] 2025-06-25 09:56:19.463 - [任务 24][local_pg - Copy] - Table table_13 is going to be initial synced 
[INFO ] 2025-06-25 09:56:19.914 - [任务 24][local_pg - Copy] - Table table_13 has been completed batch read 
[INFO ] 2025-06-25 09:56:19.914 - [任务 24][local_pg - Copy] - Starting batch read from table: table_14 
[TRACE] 2025-06-25 09:56:19.914 - [任务 24][local_pg - Copy] - Table table_14 is going to be initial synced 
[INFO ] 2025-06-25 09:56:20.754 - [任务 24][local_pg - Copy] - Table table_14 has been completed batch read 
[INFO ] 2025-06-25 09:56:20.754 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 09:56:20.754 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[TRACE] 2025-06-25 09:56:20.782 - [任务 24][local_pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  
[ERROR] 2025-06-25 09:56:20.796 - [任务 24][local_pg] - Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  <-- Error Message -->
Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type: 

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectWriteType(PostgresExceptionCollector.java:66)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:165)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:503)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:848)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:808)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:701)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 98 more

[TRACE] 2025-06-25 09:56:20.797 - [任务 24][local_pg] - Job suspend in error handle 
[TRACE] 2025-06-25 09:56:21.767 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 09:56:21.768 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 09:56:21.768 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 09:56:21.768 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 09:56:21.768 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 09:56:21.770 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816574014 
[TRACE] 2025-06-25 09:56:21.771 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816574014 
[TRACE] 2025-06-25 09:56:21.771 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 09:56:21.771 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 09:56:21.771 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 4 ms 
[TRACE] 2025-06-25 09:56:21.771 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 09:56:21.784 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816574029 
[TRACE] 2025-06-25 09:56:21.784 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816574029 
[TRACE] 2025-06-25 09:56:21.784 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 09:56:21.784 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 09:56:21.784 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 13 ms 
[INFO ] 2025-06-25 09:56:25.246 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-25 09:56:30.170 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 09:56:30.170 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 09:56:31.176 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@2c8ee768 
[TRACE] 2025-06-25 09:56:31.179 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@421449cc 
[TRACE] 2025-06-25 09:56:31.179 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 09:56:31.295 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 09:56:31.295 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 09:56:31.295 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 09:56:31.318 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 09:56:31.523 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 09:56:57.993 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 09:56:57.993 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 09:56:58.391 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 09:56:58.438 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 09:56:58.501 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 09:56:58.501 - [任务 24] - Task started 
[TRACE] 2025-06-25 09:56:58.541 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 09:56:58.541 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 09:56:58.541 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 09:56:58.541 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 09:56:58.541 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 09:56:58.700 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 09:56:58.700 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 09:56:58.700 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[INFO ] 2025-06-25 09:56:58.701 - [任务 24][local_pg - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-25 09:56:58.818 - [任务 24][local_pg - Copy] - Use existing batch read offset: {"table_10":{"batch_read_connector_status":"OVER"},"table_11":{"batch_read_connector_status":"OVER"},"table_12":{"batch_read_connector_status":"OVER"},"table_13":{"batch_read_connector_status":"OVER"},"table_14":{"batch_read_connector_status":"OVER"},"table_50":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 09:56:58.818 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 09:56:58.818 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 09:56:58.818 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 09:56:58.859 - [任务 24][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-25 09:56:58.859 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 09:56:58.865 - [任务 24][local_pg - Copy] - Initial sync started 
[TRACE] 2025-06-25 09:56:58.865 - [任务 24][local_pg - Copy] - Skip table [table_50] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 09:56:58.865 - [任务 24][local_pg - Copy] - Skip table [table_10] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 09:56:58.865 - [任务 24][local_pg - Copy] - Skip table [table_11] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 09:56:58.865 - [任务 24][local_pg - Copy] - Skip table [table_12] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 09:56:58.865 - [任务 24][local_pg - Copy] - Skip table [table_13] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 09:56:58.866 - [任务 24][local_pg - Copy] - Skip table [table_14] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2025-06-25 09:56:58.866 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 09:56:58.866 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[TRACE] 2025-06-25 09:56:58.905 - [任务 24][local_pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  
[ERROR] 2025-06-25 09:56:58.906 - [任务 24][local_pg] - Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  <-- Error Message -->
Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type: 

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectWriteType(PostgresExceptionCollector.java:66)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:165)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:503)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:848)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:808)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:701)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 98 more

[TRACE] 2025-06-25 09:56:58.906 - [任务 24][local_pg] - Job suspend in error handle 
[TRACE] 2025-06-25 09:56:59.363 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 09:56:59.890 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 09:56:59.890 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 09:56:59.891 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 09:56:59.891 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 09:56:59.891 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 09:56:59.893 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816618566 
[TRACE] 2025-06-25 09:56:59.893 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816618566 
[TRACE] 2025-06-25 09:56:59.893 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 09:56:59.893 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 09:56:59.893 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 4 ms 
[TRACE] 2025-06-25 09:56:59.894 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 09:56:59.897 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816618571 
[TRACE] 2025-06-25 09:56:59.897 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816618571 
[TRACE] 2025-06-25 09:56:59.897 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 09:56:59.897 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 09:57:00.102 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 3 ms 
[INFO ] 2025-06-25 09:57:01.373 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-25 09:57:06.387 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 09:57:06.387 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 09:57:07.392 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@493fd402 
[TRACE] 2025-06-25 09:57:07.392 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@655d1696 
[TRACE] 2025-06-25 09:57:07.508 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 09:57:07.508 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 09:57:07.508 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 09:57:07.508 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 09:57:07.526 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 09:57:07.526 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:00:01.522 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:00:01.730 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:00:01.903 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:00:01.903 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:00:01.966 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:00:01.966 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:00:02.010 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:00:02.011 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:00:02.011 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[TRACE] 2025-06-25 10:00:02.011 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:00:02.182 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:00:02.182 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:00:02.182 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:00:02.182 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[INFO ] 2025-06-25 10:00:02.182 - [任务 24][local_pg - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-25 10:00:02.301 - [任务 24][local_pg - Copy] - Use existing batch read offset: {"table_10":{"batch_read_connector_status":"OVER"},"table_11":{"batch_read_connector_status":"OVER"},"table_12":{"batch_read_connector_status":"OVER"},"table_13":{"batch_read_connector_status":"OVER"},"table_14":{"batch_read_connector_status":"OVER"},"table_50":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:00:02.301 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:00:02.301 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:00:02.301 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:00:02.313 - [任务 24][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-25 10:00:02.336 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:00:02.336 - [任务 24][local_pg - Copy] - Initial sync started 
[TRACE] 2025-06-25 10:00:02.336 - [任务 24][local_pg - Copy] - Skip table [table_50] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:00:02.336 - [任务 24][local_pg - Copy] - Skip table [table_10] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:00:02.336 - [任务 24][local_pg - Copy] - Skip table [table_11] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:00:02.336 - [任务 24][local_pg - Copy] - Skip table [table_12] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:00:02.337 - [任务 24][local_pg - Copy] - Skip table [table_13] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:00:02.337 - [任务 24][local_pg - Copy] - Skip table [table_14] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2025-06-25 10:00:02.337 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 10:00:02.541 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[TRACE] 2025-06-25 10:00:02.766 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[INFO ] 2025-06-25 10:00:03.257 - [任务 24][local_pg - Copy] - Table table_15 has been completed batch read 
[INFO ] 2025-06-25 10:00:03.257 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 10:00:03.257 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[INFO ] 2025-06-25 10:00:04.355 - [任务 24][local_pg - Copy] - Table table_16 has been completed batch read 
[INFO ] 2025-06-25 10:00:04.355 - [任务 24][local_pg - Copy] - Starting batch read from table: table_17 
[TRACE] 2025-06-25 10:00:04.355 - [任务 24][local_pg - Copy] - Table table_17 is going to be initial synced 
[INFO ] 2025-06-25 10:00:05.290 - [任务 24][local_pg - Copy] - Table table_17 has been completed batch read 
[INFO ] 2025-06-25 10:00:05.291 - [任务 24][local_pg - Copy] - Starting batch read from table: table_19 
[TRACE] 2025-06-25 10:00:05.291 - [任务 24][local_pg - Copy] - Table table_19 is going to be initial synced 
[INFO ] 2025-06-25 10:00:05.940 - [任务 24][local_pg - Copy] - Table table_19 has been completed batch read 
[INFO ] 2025-06-25 10:00:05.941 - [任务 24][local_pg - Copy] - Starting batch read from table: table_40 
[TRACE] 2025-06-25 10:00:05.941 - [任务 24][local_pg - Copy] - Table table_40 is going to be initial synced 
[INFO ] 2025-06-25 10:00:06.565 - [任务 24][local_pg - Copy] - Table table_40 has been completed batch read 
[INFO ] 2025-06-25 10:00:06.565 - [任务 24][local_pg - Copy] - Starting batch read from table: table_41 
[TRACE] 2025-06-25 10:00:06.566 - [任务 24][local_pg - Copy] - Table table_41 is going to be initial synced 
[INFO ] 2025-06-25 10:00:07.528 - [任务 24][local_pg - Copy] - Table table_41 has been completed batch read 
[INFO ] 2025-06-25 10:00:07.528 - [任务 24][local_pg - Copy] - Starting batch read from table: table_42 
[TRACE] 2025-06-25 10:00:07.528 - [任务 24][local_pg - Copy] - Table table_42 is going to be initial synced 
[INFO ] 2025-06-25 10:00:08.025 - [任务 24][local_pg - Copy] - Table table_42 has been completed batch read 
[INFO ] 2025-06-25 10:00:08.026 - [任务 24][local_pg - Copy] - Starting batch read from table: table_43 
[TRACE] 2025-06-25 10:00:08.026 - [任务 24][local_pg - Copy] - Table table_43 is going to be initial synced 
[INFO ] 2025-06-25 10:00:08.608 - [任务 24][local_pg - Copy] - Table table_43 has been completed batch read 
[INFO ] 2025-06-25 10:00:08.608 - [任务 24][local_pg - Copy] - Starting batch read from table: table_44 
[TRACE] 2025-06-25 10:00:08.608 - [任务 24][local_pg - Copy] - Table table_44 is going to be initial synced 
[INFO ] 2025-06-25 10:00:09.115 - [任务 24][local_pg - Copy] - Table table_44 has been completed batch read 
[INFO ] 2025-06-25 10:00:09.115 - [任务 24][local_pg - Copy] - Starting batch read from table: table_45 
[TRACE] 2025-06-25 10:00:09.115 - [任务 24][local_pg - Copy] - Table table_45 is going to be initial synced 
[INFO ] 2025-06-25 10:00:09.685 - [任务 24][local_pg - Copy] - Table table_45 has been completed batch read 
[INFO ] 2025-06-25 10:00:09.686 - [任务 24][local_pg - Copy] - Starting batch read from table: table_46 
[TRACE] 2025-06-25 10:00:09.686 - [任务 24][local_pg - Copy] - Table table_46 is going to be initial synced 
[INFO ] 2025-06-25 10:00:10.828 - [任务 24][local_pg - Copy] - Table table_46 has been completed batch read 
[INFO ] 2025-06-25 10:00:10.829 - [任务 24][local_pg - Copy] - Starting batch read from table: table_47 
[TRACE] 2025-06-25 10:00:10.829 - [任务 24][local_pg - Copy] - Table table_47 is going to be initial synced 
[INFO ] 2025-06-25 10:00:11.455 - [任务 24][local_pg - Copy] - Table table_47 has been completed batch read 
[INFO ] 2025-06-25 10:00:11.455 - [任务 24][local_pg - Copy] - Starting batch read from table: table_48 
[TRACE] 2025-06-25 10:00:11.455 - [任务 24][local_pg - Copy] - Table table_48 is going to be initial synced 
[INFO ] 2025-06-25 10:00:12.395 - [任务 24][local_pg - Copy] - Table table_48 has been completed batch read 
[INFO ] 2025-06-25 10:00:12.395 - [任务 24][local_pg - Copy] - Starting batch read from table: table_49 
[TRACE] 2025-06-25 10:00:12.395 - [任务 24][local_pg - Copy] - Table table_49 is going to be initial synced 
[INFO ] 2025-06-25 10:00:13.313 - [任务 24][local_pg - Copy] - Table table_49 has been completed batch read 
[INFO ] 2025-06-25 10:00:13.313 - [任务 24][local_pg - Copy] - Starting batch read from table: table_30 
[TRACE] 2025-06-25 10:00:13.313 - [任务 24][local_pg - Copy] - Table table_30 is going to be initial synced 
[INFO ] 2025-06-25 10:00:14.292 - [任务 24][local_pg - Copy] - Table table_30 has been completed batch read 
[INFO ] 2025-06-25 10:00:14.292 - [任务 24][local_pg - Copy] - Starting batch read from table: table_31 
[TRACE] 2025-06-25 10:00:14.292 - [任务 24][local_pg - Copy] - Table table_31 is going to be initial synced 
[INFO ] 2025-06-25 10:00:14.800 - [任务 24][local_pg - Copy] - Table table_31 has been completed batch read 
[INFO ] 2025-06-25 10:00:14.800 - [任务 24][local_pg - Copy] - Starting batch read from table: table_32 
[TRACE] 2025-06-25 10:00:14.800 - [任务 24][local_pg - Copy] - Table table_32 is going to be initial synced 
[INFO ] 2025-06-25 10:00:15.598 - [任务 24][local_pg - Copy] - Table table_32 has been completed batch read 
[INFO ] 2025-06-25 10:00:15.598 - [任务 24][local_pg - Copy] - Starting batch read from table: table_33 
[TRACE] 2025-06-25 10:00:15.598 - [任务 24][local_pg - Copy] - Table table_33 is going to be initial synced 
[INFO ] 2025-06-25 10:00:16.155 - [任务 24][local_pg - Copy] - Table table_33 has been completed batch read 
[INFO ] 2025-06-25 10:00:16.155 - [任务 24][local_pg - Copy] - Starting batch read from table: table_34 
[TRACE] 2025-06-25 10:00:16.155 - [任务 24][local_pg - Copy] - Table table_34 is going to be initial synced 
[INFO ] 2025-06-25 10:00:16.842 - [任务 24][local_pg - Copy] - Table table_34 has been completed batch read 
[INFO ] 2025-06-25 10:00:16.842 - [任务 24][local_pg - Copy] - Starting batch read from table: table_35 
[TRACE] 2025-06-25 10:00:16.842 - [任务 24][local_pg - Copy] - Table table_35 is going to be initial synced 
[INFO ] 2025-06-25 10:00:17.575 - [任务 24][local_pg - Copy] - Table table_35 has been completed batch read 
[INFO ] 2025-06-25 10:00:17.575 - [任务 24][local_pg - Copy] - Starting batch read from table: table_36 
[TRACE] 2025-06-25 10:00:17.575 - [任务 24][local_pg - Copy] - Table table_36 is going to be initial synced 
[INFO ] 2025-06-25 10:00:18.431 - [任务 24][local_pg - Copy] - Table table_36 has been completed batch read 
[INFO ] 2025-06-25 10:00:18.432 - [任务 24][local_pg - Copy] - Starting batch read from table: table_37 
[TRACE] 2025-06-25 10:00:18.432 - [任务 24][local_pg - Copy] - Table table_37 is going to be initial synced 
[INFO ] 2025-06-25 10:00:19.015 - [任务 24][local_pg - Copy] - Table table_37 has been completed batch read 
[INFO ] 2025-06-25 10:00:19.015 - [任务 24][local_pg - Copy] - Starting batch read from table: table_38 
[TRACE] 2025-06-25 10:00:19.015 - [任务 24][local_pg - Copy] - Table table_38 is going to be initial synced 
[INFO ] 2025-06-25 10:00:19.745 - [任务 24][local_pg - Copy] - Table table_38 has been completed batch read 
[INFO ] 2025-06-25 10:00:19.746 - [任务 24][local_pg - Copy] - Starting batch read from table: table_39 
[TRACE] 2025-06-25 10:00:19.746 - [任务 24][local_pg - Copy] - Table table_39 is going to be initial synced 
[INFO ] 2025-06-25 10:00:20.170 - [任务 24][local_pg - Copy] - Table table_39 has been completed batch read 
[INFO ] 2025-06-25 10:00:20.170 - [任务 24][local_pg - Copy] - Starting batch read from table: table_20 
[TRACE] 2025-06-25 10:00:20.170 - [任务 24][local_pg - Copy] - Table table_20 is going to be initial synced 
[INFO ] 2025-06-25 10:00:21.132 - [任务 24][local_pg - Copy] - Table table_20 has been completed batch read 
[INFO ] 2025-06-25 10:00:21.132 - [任务 24][local_pg - Copy] - Starting batch read from table: table_21 
[TRACE] 2025-06-25 10:00:21.132 - [任务 24][local_pg - Copy] - Table table_21 is going to be initial synced 
[INFO ] 2025-06-25 10:00:21.590 - [任务 24][local_pg - Copy] - Table table_21 has been completed batch read 
[INFO ] 2025-06-25 10:00:21.591 - [任务 24][local_pg - Copy] - Starting batch read from table: table_1 
[TRACE] 2025-06-25 10:00:21.591 - [任务 24][local_pg - Copy] - Table table_1 is going to be initial synced 
[INFO ] 2025-06-25 10:00:22.529 - [任务 24][local_pg - Copy] - Table table_1 has been completed batch read 
[INFO ] 2025-06-25 10:00:22.529 - [任务 24][local_pg - Copy] - Starting batch read from table: table_22 
[TRACE] 2025-06-25 10:00:22.529 - [任务 24][local_pg - Copy] - Table table_22 is going to be initial synced 
[INFO ] 2025-06-25 10:00:23.200 - [任务 24][local_pg - Copy] - Table table_22 has been completed batch read 
[INFO ] 2025-06-25 10:00:23.200 - [任务 24][local_pg - Copy] - Starting batch read from table: table_2 
[TRACE] 2025-06-25 10:00:23.200 - [任务 24][local_pg - Copy] - Table table_2 is going to be initial synced 
[INFO ] 2025-06-25 10:00:24.060 - [任务 24][local_pg - Copy] - Table table_2 has been completed batch read 
[INFO ] 2025-06-25 10:00:24.060 - [任务 24][local_pg - Copy] - Starting batch read from table: table_3 
[TRACE] 2025-06-25 10:00:24.060 - [任务 24][local_pg - Copy] - Table table_3 is going to be initial synced 
[INFO ] 2025-06-25 10:00:25.079 - [任务 24][local_pg - Copy] - Table table_3 has been completed batch read 
[INFO ] 2025-06-25 10:00:25.079 - [任务 24][local_pg - Copy] - Starting batch read from table: table_23 
[TRACE] 2025-06-25 10:00:25.079 - [任务 24][local_pg - Copy] - Table table_23 is going to be initial synced 
[INFO ] 2025-06-25 10:00:25.971 - [任务 24][local_pg - Copy] - Table table_23 has been completed batch read 
[INFO ] 2025-06-25 10:00:25.971 - [任务 24][local_pg - Copy] - Starting batch read from table: table_4 
[TRACE] 2025-06-25 10:00:25.972 - [任务 24][local_pg - Copy] - Table table_4 is going to be initial synced 
[INFO ] 2025-06-25 10:00:26.492 - [任务 24][local_pg - Copy] - Table table_4 has been completed batch read 
[INFO ] 2025-06-25 10:00:26.492 - [任务 24][local_pg - Copy] - Starting batch read from table: table_24 
[TRACE] 2025-06-25 10:00:26.493 - [任务 24][local_pg - Copy] - Table table_24 is going to be initial synced 
[INFO ] 2025-06-25 10:00:27.410 - [任务 24][local_pg - Copy] - Table table_24 has been completed batch read 
[INFO ] 2025-06-25 10:00:27.415 - [任务 24][local_pg - Copy] - Starting batch read from table: table_25 
[TRACE] 2025-06-25 10:00:27.415 - [任务 24][local_pg - Copy] - Table table_25 is going to be initial synced 
[INFO ] 2025-06-25 10:00:28.419 - [任务 24][local_pg - Copy] - Table table_25 has been completed batch read 
[INFO ] 2025-06-25 10:00:28.419 - [任务 24][local_pg - Copy] - Starting batch read from table: table_5 
[TRACE] 2025-06-25 10:00:28.419 - [任务 24][local_pg - Copy] - Table table_5 is going to be initial synced 
[INFO ] 2025-06-25 10:00:29.406 - [任务 24][local_pg - Copy] - Table table_5 has been completed batch read 
[INFO ] 2025-06-25 10:00:29.406 - [任务 24][local_pg - Copy] - Starting batch read from table: table_26 
[TRACE] 2025-06-25 10:00:29.406 - [任务 24][local_pg - Copy] - Table table_26 is going to be initial synced 
[INFO ] 2025-06-25 10:00:30.522 - [任务 24][local_pg - Copy] - Table table_26 has been completed batch read 
[INFO ] 2025-06-25 10:00:30.522 - [任务 24][local_pg - Copy] - Starting batch read from table: table_6 
[TRACE] 2025-06-25 10:00:30.522 - [任务 24][local_pg - Copy] - Table table_6 is going to be initial synced 
[INFO ] 2025-06-25 10:00:31.679 - [任务 24][local_pg - Copy] - Table table_6 has been completed batch read 
[INFO ] 2025-06-25 10:00:31.679 - [任务 24][local_pg - Copy] - Starting batch read from table: table_27 
[TRACE] 2025-06-25 10:00:31.679 - [任务 24][local_pg - Copy] - Table table_27 is going to be initial synced 
[INFO ] 2025-06-25 10:00:32.127 - [任务 24][local_pg - Copy] - Table table_27 has been completed batch read 
[INFO ] 2025-06-25 10:00:32.128 - [任务 24][local_pg - Copy] - Starting batch read from table: table_7 
[TRACE] 2025-06-25 10:00:32.128 - [任务 24][local_pg - Copy] - Table table_7 is going to be initial synced 
[INFO ] 2025-06-25 10:00:32.819 - [任务 24][local_pg - Copy] - Table table_7 has been completed batch read 
[INFO ] 2025-06-25 10:00:32.820 - [任务 24][local_pg - Copy] - Starting batch read from table: table_28 
[TRACE] 2025-06-25 10:00:32.820 - [任务 24][local_pg - Copy] - Table table_28 is going to be initial synced 
[INFO ] 2025-06-25 10:00:33.308 - [任务 24][local_pg - Copy] - Table table_28 has been completed batch read 
[INFO ] 2025-06-25 10:00:33.308 - [任务 24][local_pg - Copy] - Starting batch read from table: table_8 
[TRACE] 2025-06-25 10:00:33.308 - [任务 24][local_pg - Copy] - Table table_8 is going to be initial synced 
[INFO ] 2025-06-25 10:00:34.289 - [任务 24][local_pg - Copy] - Table table_8 has been completed batch read 
[INFO ] 2025-06-25 10:00:34.290 - [任务 24][local_pg - Copy] - Starting batch read from table: table_29 
[TRACE] 2025-06-25 10:00:34.290 - [任务 24][local_pg - Copy] - Table table_29 is going to be initial synced 
[INFO ] 2025-06-25 10:00:34.696 - [任务 24][local_pg - Copy] - Table table_29 has been completed batch read 
[INFO ] 2025-06-25 10:00:34.696 - [任务 24][local_pg - Copy] - Starting batch read from table: table_9 
[TRACE] 2025-06-25 10:00:34.696 - [任务 24][local_pg - Copy] - Table table_9 is going to be initial synced 
[INFO ] 2025-06-25 10:00:35.269 - [任务 24][local_pg - Copy] - Table table_9 has been completed batch read 
[TRACE] 2025-06-25 10:00:35.269 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:00:35.269 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:00:35.269 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:00:35.269 - [任务 24][local_pg - Copy] - Initial sync completed 
[TRACE] 2025-06-25 10:00:35.270 - [任务 24][local_pg - Copy] - Starting stream read, table list: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:00:35.270 - [任务 24][local_pg - Copy] - Starting incremental sync using database log parser 
[WARN ] 2025-06-25 10:00:35.280 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[TRACE] 2025-06-25 10:00:35.281 - [任务 24][local_pg] - Process after table "table_16" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.281 - [任务 24][local_pg] - Process after table "table_15" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.281 - [任务 24][local_pg] - Process after table "table_19" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.281 - [任务 24][local_pg] - Process after table "table_41" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.281 - [任务 24][local_pg] - Process after table "table_17" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.281 - [任务 24][local_pg] - Process after table "table_40" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.281 - [任务 24][local_pg] - Process after table "table_42" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_45" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_44" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_43" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_47" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_46" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_48" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-25 10:00:35.282 - [任务 24][local_pg - Copy] - Using an existing logical replication slot, slotName:tapdata_cdc_ca14b0fd_4e02_4da9_9704_cf2e92e7b1c0 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_30" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_49" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_31" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_34" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.282 - [任务 24][local_pg] - Process after table "table_35" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.283 - [任务 24][local_pg] - Process after table "table_33" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.283 - [任务 24][local_pg] - Process after table "table_36" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.283 - [任务 24][local_pg] - Process after table "table_32" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.283 - [任务 24][local_pg] - Process after table "table_37" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.283 - [任务 24][local_pg] - Process after table "table_21" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.283 - [任务 24][local_pg] - Process after table "table_11" initial sync finished, cost: 2 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_20" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_38" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_39" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_1" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_24" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_5" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_3" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_25" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_4" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_23" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_26" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_6" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_22" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.284 - [任务 24][local_pg] - Process after table "table_27" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.285 - [任务 24][local_pg] - Process after table "table_28" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.285 - [任务 24][local_pg] - Process after table "table_8" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.285 - [任务 24][local_pg] - Process after table "table_7" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.285 - [任务 24][local_pg] - Process after table "table_29" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.285 - [任务 24][local_pg] - Process after table "table_9" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:00:35.288 - [任务 24][local_pg] - Process after table "table_14" initial sync finished, cost: 6 ms 
[TRACE] 2025-06-25 10:00:35.288 - [任务 24][local_pg] - Process after table "table_13" initial sync finished, cost: 8 ms 
[TRACE] 2025-06-25 10:00:35.303 - [任务 24][local_pg] - Process after table "table_50" initial sync finished, cost: 10 ms 
[TRACE] 2025-06-25 10:00:35.303 - [任务 24][local_pg] - Process after table "table_10" initial sync finished, cost: 22 ms 
[TRACE] 2025-06-25 10:00:35.303 - [任务 24][local_pg] - Process after table "table_12" initial sync finished, cost: 22 ms 
[INFO ] 2025-06-25 10:00:35.313 - [任务 24][local_pg] - Process after all table(s) initial sync are finished，table number: 49 
[TRACE] 2025-06-25 10:00:35.313 - [任务 24][local_pg - Copy] - Connector PostgreSQL incremental start succeed, tables: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], data change syncing 
[TRACE] 2025-06-25 10:03:29.721 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:03:29.981 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:03:29.981 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816802028 
[TRACE] 2025-06-25 10:03:29.981 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750816802028 
[TRACE] 2025-06-25 10:03:29.981 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:03:29.981 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:03:29.981 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 314 ms 
[TRACE] 2025-06-25 10:03:29.981 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:03:29.988 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816802037 
[TRACE] 2025-06-25 10:03:29.988 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750816802037 
[TRACE] 2025-06-25 10:03:29.988 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:03:29.988 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:03:30.193 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 6 ms 
[TRACE] 2025-06-25 10:03:37.793 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 10:03:38.799 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@15cf1fae 
[TRACE] 2025-06-25 10:03:38.799 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@35b5aec7 
[TRACE] 2025-06-25 10:03:38.913 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 10:03:38.913 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 10:03:38.913 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 10:03:38.913 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 10:03:38.949 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:03:38.953 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:06:45.663 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:06:45.869 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:06:46.275 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:06:46.368 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:06:46.368 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:06:46.571 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:06:46.579 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:06:46.579 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:06:46.580 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:06:46.580 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:06:46.782 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:06:46.969 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:06:46.969 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:06:46.985 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:06:46.985 - [任务 24][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-25 10:06:47.073 - [任务 24][local_pg] - The table table_50 has already exist. 
[TRACE] 2025-06-25 10:06:47.074 - [任务 24][local_pg] - The table table_10 has already exist. 
[INFO ] 2025-06-25 10:06:47.102 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:06:47.102 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:06:47.102 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[TRACE] 2025-06-25 10:06:47.102 - [任务 24][local_pg - Copy] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-06-25 10:06:47.123 - [任务 24][local_pg] - The table table_11 has already exist. 
[WARN ] 2025-06-25 10:06:47.123 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 10:06:47.137 - [任务 24][local_pg - Copy] - new logical replication slot created, slotName:tapdata_cdc_cb2d0822_9eb1_410c_aab7_4a33b559761d 
[INFO ] 2025-06-25 10:06:47.137 - [任务 24][local_pg - Copy] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-06-25 10:06:47.181 - [任务 24][local_pg] - The table table_12 has already exist. 
[TRACE] 2025-06-25 10:06:47.181 - [任务 24][local_pg] - The table table_13 has already exist. 
[TRACE] 2025-06-25 10:06:47.243 - [任务 24][local_pg] - The table table_14 has already exist. 
[TRACE] 2025-06-25 10:06:47.243 - [任务 24][local_pg] - The table table_15 has already exist. 
[TRACE] 2025-06-25 10:06:47.287 - [任务 24][local_pg] - The table table_16 has already exist. 
[INFO ] 2025-06-25 10:06:47.287 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:06:47.300 - [任务 24][local_pg - Copy] - Initial sync started 
[INFO ] 2025-06-25 10:06:47.300 - [任务 24][local_pg - Copy] - Starting batch read from table: table_50 
[TRACE] 2025-06-25 10:06:47.303 - [任务 24][local_pg - Copy] - Table table_50 is going to be initial synced 
[TRACE] 2025-06-25 10:06:47.303 - [任务 24][local_pg] - The table table_17 has already exist. 
[TRACE] 2025-06-25 10:06:47.381 - [任务 24][local_pg] - The table table_19 has already exist. 
[TRACE] 2025-06-25 10:06:47.381 - [任务 24][local_pg] - The table table_40 has already exist. 
[TRACE] 2025-06-25 10:06:47.483 - [任务 24][local_pg] - The table table_41 has already exist. 
[TRACE] 2025-06-25 10:06:47.483 - [任务 24][local_pg] - The table table_42 has already exist. 
[TRACE] 2025-06-25 10:06:47.555 - [任务 24][local_pg] - The table table_43 has already exist. 
[TRACE] 2025-06-25 10:06:47.555 - [任务 24][local_pg] - The table table_44 has already exist. 
[TRACE] 2025-06-25 10:06:47.722 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 10:06:47.888 - [任务 24][local_pg] - The table table_45 has already exist. 
[TRACE] 2025-06-25 10:06:47.888 - [任务 24][local_pg] - The table table_46 has already exist. 
[TRACE] 2025-06-25 10:06:47.957 - [任务 24][local_pg] - The table table_47 has already exist. 
[TRACE] 2025-06-25 10:06:47.957 - [任务 24][local_pg] - The table table_48 has already exist. 
[TRACE] 2025-06-25 10:06:48.021 - [任务 24][local_pg] - The table table_49 has already exist. 
[TRACE] 2025-06-25 10:06:48.021 - [任务 24][local_pg] - The table table_30 has already exist. 
[TRACE] 2025-06-25 10:06:48.085 - [任务 24][local_pg] - The table table_31 has already exist. 
[TRACE] 2025-06-25 10:06:48.085 - [任务 24][local_pg] - The table table_32 has already exist. 
[TRACE] 2025-06-25 10:06:48.149 - [任务 24][local_pg] - The table table_33 has already exist. 
[TRACE] 2025-06-25 10:06:48.150 - [任务 24][local_pg] - The table table_34 has already exist. 
[TRACE] 2025-06-25 10:06:48.289 - [任务 24][local_pg] - The table table_35 has already exist. 
[TRACE] 2025-06-25 10:06:48.289 - [任务 24][local_pg] - The table table_36 has already exist. 
[TRACE] 2025-06-25 10:06:48.347 - [任务 24][local_pg] - The table table_37 has already exist. 
[TRACE] 2025-06-25 10:06:48.347 - [任务 24][local_pg] - The table table_38 has already exist. 
[TRACE] 2025-06-25 10:06:48.403 - [任务 24][local_pg] - The table table_39 has already exist. 
[TRACE] 2025-06-25 10:06:48.404 - [任务 24][local_pg] - The table table_20 has already exist. 
[TRACE] 2025-06-25 10:06:48.465 - [任务 24][local_pg] - The table table_21 has already exist. 
[TRACE] 2025-06-25 10:06:48.465 - [任务 24][local_pg] - The table table_1 has already exist. 
[TRACE] 2025-06-25 10:06:48.526 - [任务 24][local_pg] - The table table_22 has already exist. 
[TRACE] 2025-06-25 10:06:48.526 - [任务 24][local_pg] - The table table_2 has already exist. 
[TRACE] 2025-06-25 10:06:48.586 - [任务 24][local_pg] - The table table_3 has already exist. 
[TRACE] 2025-06-25 10:06:48.587 - [任务 24][local_pg] - The table table_23 has already exist. 
[TRACE] 2025-06-25 10:06:48.645 - [任务 24][local_pg] - The table table_4 has already exist. 
[TRACE] 2025-06-25 10:06:48.646 - [任务 24][local_pg] - The table table_24 has already exist. 
[TRACE] 2025-06-25 10:06:48.703 - [任务 24][local_pg] - The table table_25 has already exist. 
[TRACE] 2025-06-25 10:06:48.703 - [任务 24][local_pg] - The table table_5 has already exist. 
[TRACE] 2025-06-25 10:06:48.763 - [任务 24][local_pg] - The table table_26 has already exist. 
[TRACE] 2025-06-25 10:06:48.763 - [任务 24][local_pg] - The table table_6 has already exist. 
[TRACE] 2025-06-25 10:06:48.823 - [任务 24][local_pg] - The table table_27 has already exist. 
[TRACE] 2025-06-25 10:06:48.823 - [任务 24][local_pg] - The table table_7 has already exist. 
[TRACE] 2025-06-25 10:06:48.884 - [任务 24][local_pg] - The table table_28 has already exist. 
[TRACE] 2025-06-25 10:06:48.884 - [任务 24][local_pg] - The table table_8 has already exist. 
[TRACE] 2025-06-25 10:06:48.944 - [任务 24][local_pg] - The table table_29 has already exist. 
[TRACE] 2025-06-25 10:06:48.944 - [任务 24][local_pg] - The table table_9 has already exist. 
[INFO ] 2025-06-25 10:06:49.996 - [任务 24][local_pg - Copy] - Table table_50 has been completed batch read 
[INFO ] 2025-06-25 10:06:49.996 - [任务 24][local_pg - Copy] - Starting batch read from table: table_10 
[TRACE] 2025-06-25 10:06:49.996 - [任务 24][local_pg - Copy] - Table table_10 is going to be initial synced 
[INFO ] 2025-06-25 10:06:50.379 - [任务 24][local_pg - Copy] - Table table_10 has been completed batch read 
[INFO ] 2025-06-25 10:06:50.380 - [任务 24][local_pg - Copy] - Starting batch read from table: table_11 
[TRACE] 2025-06-25 10:06:50.380 - [任务 24][local_pg - Copy] - Table table_11 is going to be initial synced 
[INFO ] 2025-06-25 10:06:51.053 - [任务 24][local_pg - Copy] - Table table_11 has been completed batch read 
[INFO ] 2025-06-25 10:06:51.054 - [任务 24][local_pg - Copy] - Starting batch read from table: table_12 
[TRACE] 2025-06-25 10:06:51.054 - [任务 24][local_pg - Copy] - Table table_12 is going to be initial synced 
[INFO ] 2025-06-25 10:06:51.802 - [任务 24][local_pg - Copy] - Table table_12 has been completed batch read 
[INFO ] 2025-06-25 10:06:51.802 - [任务 24][local_pg - Copy] - Starting batch read from table: table_13 
[TRACE] 2025-06-25 10:06:51.803 - [任务 24][local_pg - Copy] - Table table_13 is going to be initial synced 
[INFO ] 2025-06-25 10:06:52.248 - [任务 24][local_pg - Copy] - Table table_13 has been completed batch read 
[INFO ] 2025-06-25 10:06:52.248 - [任务 24][local_pg - Copy] - Starting batch read from table: table_14 
[TRACE] 2025-06-25 10:06:52.249 - [任务 24][local_pg - Copy] - Table table_14 is going to be initial synced 
[INFO ] 2025-06-25 10:06:53.539 - [任务 24][local_pg - Copy] - Table table_14 has been completed batch read 
[INFO ] 2025-06-25 10:06:53.539 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 10:06:53.540 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[TRACE] 2025-06-25 10:06:53.575 - [任务 24][local_pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  
[ERROR] 2025-06-25 10:06:53.576 - [任务 24][local_pg] - Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  <-- Error Message -->
Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type: 

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectWriteType(PostgresExceptionCollector.java:66)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:165)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:503)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:848)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:808)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:701)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 98 more

[TRACE] 2025-06-25 10:06:53.576 - [任务 24][local_pg] - Job suspend in error handle 
[TRACE] 2025-06-25 10:06:54.555 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:06:54.558 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:06:54.558 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:06:54.563 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:06:54.563 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:06:54.563 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817206862 
[TRACE] 2025-06-25 10:06:54.564 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817206862 
[TRACE] 2025-06-25 10:06:54.564 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:06:54.566 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:06:54.566 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 10 ms 
[TRACE] 2025-06-25 10:06:54.566 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:06:54.602 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817206843 
[TRACE] 2025-06-25 10:06:54.602 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817206843 
[TRACE] 2025-06-25 10:06:54.602 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:06:54.602 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:06:54.804 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 35 ms 
[INFO ] 2025-06-25 10:06:58.056 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-25 10:07:03.068 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 10:07:03.272 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 10:07:04.077 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@55310d50 
[TRACE] 2025-06-25 10:07:04.078 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1312fa21 
[TRACE] 2025-06-25 10:07:04.197 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 10:07:04.197 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 10:07:04.198 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 10:07:04.198 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 10:07:04.219 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:07:04.222 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:07:30.503 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:07:30.710 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:07:30.864 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:07:30.864 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:07:30.928 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:07:30.928 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:07:30.969 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:07:30.970 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:07:30.970 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:07:30.970 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:07:30.970 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:07:31.128 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:07:31.129 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:07:31.129 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:07:31.278 - [任务 24][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-25 10:07:31.278 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:07:31.278 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:07:31.278 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[INFO ] 2025-06-25 10:07:31.279 - [任务 24][local_pg - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-25 10:07:31.279 - [任务 24][local_pg - Copy] - Use existing batch read offset: {"table_10":{"batch_read_connector_status":"OVER"},"table_11":{"batch_read_connector_status":"OVER"},"table_12":{"batch_read_connector_status":"OVER"},"table_13":{"batch_read_connector_status":"OVER"},"table_14":{"batch_read_connector_status":"OVER"},"table_50":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:07:31.445 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:07:31.445 - [任务 24][local_pg - Copy] - Initial sync started 
[TRACE] 2025-06-25 10:07:31.445 - [任务 24][local_pg - Copy] - Skip table [table_50] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:07:31.445 - [任务 24][local_pg - Copy] - Skip table [table_10] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:07:31.445 - [任务 24][local_pg - Copy] - Skip table [table_11] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:07:31.445 - [任务 24][local_pg - Copy] - Skip table [table_12] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:07:31.445 - [任务 24][local_pg - Copy] - Skip table [table_13] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:07:31.445 - [任务 24][local_pg - Copy] - Skip table [table_14] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2025-06-25 10:07:31.445 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 10:07:31.446 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[TRACE] 2025-06-25 10:07:31.483 - [任务 24][local_pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  
[ERROR] 2025-06-25 10:07:31.483 - [任务 24][local_pg] - Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  <-- Error Message -->
Target type in postgres does not match the incoming data when write record.
 - Table name: table_15
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type: 

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectWriteType(PostgresExceptionCollector.java:66)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:165)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:503)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:848)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:808)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:701)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 98 more

[TRACE] 2025-06-25 10:07:31.484 - [任务 24][local_pg] - Job suspend in error handle 
[TRACE] 2025-06-25 10:07:31.953 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 10:07:32.473 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:07:32.474 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:07:32.474 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:07:32.474 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:07:32.476 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:07:32.476 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817250996 
[TRACE] 2025-06-25 10:07:32.476 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817250996 
[TRACE] 2025-06-25 10:07:32.477 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:07:32.477 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:07:32.477 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 4 ms 
[TRACE] 2025-06-25 10:07:32.477 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:07:32.480 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817250991 
[TRACE] 2025-06-25 10:07:32.480 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817250991 
[TRACE] 2025-06-25 10:07:32.480 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:07:32.480 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:07:32.685 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 3 ms 
[INFO ] 2025-06-25 10:07:34.251 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-25 10:07:39.261 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 10:07:39.466 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 10:07:40.265 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@574d655 
[TRACE] 2025-06-25 10:07:40.266 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d158de9 
[TRACE] 2025-06-25 10:07:40.381 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 10:07:40.382 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 10:07:40.382 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 10:07:40.403 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 10:07:40.403 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:07:40.403 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:08:27.958 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:08:27.961 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:08:28.276 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:08:28.357 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:08:28.357 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:08:28.422 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:08:28.423 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:08:28.423 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:08:28.423 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:08:28.423 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:08:28.424 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:08:28.573 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:08:28.573 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:08:28.573 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[INFO ] 2025-06-25 10:08:28.573 - [任务 24][local_pg - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-25 10:08:28.688 - [任务 24][local_pg - Copy] - Use existing batch read offset: {"table_10":{"batch_read_connector_status":"OVER"},"table_11":{"batch_read_connector_status":"OVER"},"table_12":{"batch_read_connector_status":"OVER"},"table_13":{"batch_read_connector_status":"OVER"},"table_14":{"batch_read_connector_status":"OVER"},"table_50":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:08:28.688 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:08:28.688 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:08:28.688 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:08:28.723 - [任务 24][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-25 10:08:28.723 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:08:28.727 - [任务 24][local_pg - Copy] - Initial sync started 
[TRACE] 2025-06-25 10:08:28.727 - [任务 24][local_pg - Copy] - Skip table [table_50] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:08:28.727 - [任务 24][local_pg - Copy] - Skip table [table_10] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:08:28.727 - [任务 24][local_pg - Copy] - Skip table [table_11] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:08:28.727 - [任务 24][local_pg - Copy] - Skip table [table_12] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:08:28.727 - [任务 24][local_pg - Copy] - Skip table [table_13] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:08:28.727 - [任务 24][local_pg - Copy] - Skip table [table_14] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2025-06-25 10:08:28.727 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 10:08:28.727 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[TRACE] 2025-06-25 10:08:29.338 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[INFO ] 2025-06-25 10:08:29.711 - [任务 24][local_pg - Copy] - Table table_15 has been completed batch read 
[INFO ] 2025-06-25 10:08:29.711 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 10:08:29.711 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[INFO ] 2025-06-25 10:08:30.816 - [任务 24][local_pg - Copy] - Table table_16 has been completed batch read 
[INFO ] 2025-06-25 10:08:30.816 - [任务 24][local_pg - Copy] - Starting batch read from table: table_17 
[TRACE] 2025-06-25 10:08:31.021 - [任务 24][local_pg - Copy] - Table table_17 is going to be initial synced 
[INFO ] 2025-06-25 10:08:31.765 - [任务 24][local_pg - Copy] - Table table_17 has been completed batch read 
[INFO ] 2025-06-25 10:08:31.765 - [任务 24][local_pg - Copy] - Starting batch read from table: table_19 
[TRACE] 2025-06-25 10:08:31.965 - [任务 24][local_pg - Copy] - Table table_19 is going to be initial synced 
[INFO ] 2025-06-25 10:08:32.445 - [任务 24][local_pg - Copy] - Table table_19 has been completed batch read 
[INFO ] 2025-06-25 10:08:32.445 - [任务 24][local_pg - Copy] - Starting batch read from table: table_40 
[TRACE] 2025-06-25 10:08:32.445 - [任务 24][local_pg - Copy] - Table table_40 is going to be initial synced 
[INFO ] 2025-06-25 10:08:33.051 - [任务 24][local_pg - Copy] - Table table_40 has been completed batch read 
[INFO ] 2025-06-25 10:08:33.051 - [任务 24][local_pg - Copy] - Starting batch read from table: table_41 
[TRACE] 2025-06-25 10:08:33.051 - [任务 24][local_pg - Copy] - Table table_41 is going to be initial synced 
[INFO ] 2025-06-25 10:08:33.872 - [任务 24][local_pg - Copy] - Table table_41 has been completed batch read 
[INFO ] 2025-06-25 10:08:33.873 - [任务 24][local_pg - Copy] - Starting batch read from table: table_42 
[TRACE] 2025-06-25 10:08:33.873 - [任务 24][local_pg - Copy] - Table table_42 is going to be initial synced 
[INFO ] 2025-06-25 10:08:34.367 - [任务 24][local_pg - Copy] - Table table_42 has been completed batch read 
[INFO ] 2025-06-25 10:08:34.367 - [任务 24][local_pg - Copy] - Starting batch read from table: table_43 
[TRACE] 2025-06-25 10:08:34.367 - [任务 24][local_pg - Copy] - Table table_43 is going to be initial synced 
[INFO ] 2025-06-25 10:08:34.948 - [任务 24][local_pg - Copy] - Table table_43 has been completed batch read 
[INFO ] 2025-06-25 10:08:34.948 - [任务 24][local_pg - Copy] - Starting batch read from table: table_44 
[TRACE] 2025-06-25 10:08:34.948 - [任务 24][local_pg - Copy] - Table table_44 is going to be initial synced 
[INFO ] 2025-06-25 10:08:35.433 - [任务 24][local_pg - Copy] - Table table_44 has been completed batch read 
[INFO ] 2025-06-25 10:08:35.433 - [任务 24][local_pg - Copy] - Starting batch read from table: table_45 
[TRACE] 2025-06-25 10:08:35.433 - [任务 24][local_pg - Copy] - Table table_45 is going to be initial synced 
[INFO ] 2025-06-25 10:08:36.029 - [任务 24][local_pg - Copy] - Table table_45 has been completed batch read 
[INFO ] 2025-06-25 10:08:36.029 - [任务 24][local_pg - Copy] - Starting batch read from table: table_46 
[TRACE] 2025-06-25 10:08:36.230 - [任务 24][local_pg - Copy] - Table table_46 is going to be initial synced 
[INFO ] 2025-06-25 10:08:37.110 - [任务 24][local_pg - Copy] - Table table_46 has been completed batch read 
[INFO ] 2025-06-25 10:08:37.110 - [任务 24][local_pg - Copy] - Starting batch read from table: table_47 
[TRACE] 2025-06-25 10:08:37.111 - [任务 24][local_pg - Copy] - Table table_47 is going to be initial synced 
[INFO ] 2025-06-25 10:08:37.750 - [任务 24][local_pg - Copy] - Table table_47 has been completed batch read 
[INFO ] 2025-06-25 10:08:37.750 - [任务 24][local_pg - Copy] - Starting batch read from table: table_48 
[TRACE] 2025-06-25 10:08:37.750 - [任务 24][local_pg - Copy] - Table table_48 is going to be initial synced 
[INFO ] 2025-06-25 10:08:38.728 - [任务 24][local_pg - Copy] - Table table_48 has been completed batch read 
[INFO ] 2025-06-25 10:08:38.728 - [任务 24][local_pg - Copy] - Starting batch read from table: table_49 
[TRACE] 2025-06-25 10:08:38.728 - [任务 24][local_pg - Copy] - Table table_49 is going to be initial synced 
[INFO ] 2025-06-25 10:08:39.680 - [任务 24][local_pg - Copy] - Table table_49 has been completed batch read 
[INFO ] 2025-06-25 10:08:39.683 - [任务 24][local_pg - Copy] - Starting batch read from table: table_30 
[TRACE] 2025-06-25 10:08:39.684 - [任务 24][local_pg - Copy] - Table table_30 is going to be initial synced 
[INFO ] 2025-06-25 10:08:40.686 - [任务 24][local_pg - Copy] - Table table_30 has been completed batch read 
[INFO ] 2025-06-25 10:08:40.686 - [任务 24][local_pg - Copy] - Starting batch read from table: table_31 
[TRACE] 2025-06-25 10:08:40.686 - [任务 24][local_pg - Copy] - Table table_31 is going to be initial synced 
[INFO ] 2025-06-25 10:08:41.201 - [任务 24][local_pg - Copy] - Table table_31 has been completed batch read 
[INFO ] 2025-06-25 10:08:41.202 - [任务 24][local_pg - Copy] - Starting batch read from table: table_32 
[TRACE] 2025-06-25 10:08:41.402 - [任务 24][local_pg - Copy] - Table table_32 is going to be initial synced 
[INFO ] 2025-06-25 10:08:41.987 - [任务 24][local_pg - Copy] - Table table_32 has been completed batch read 
[INFO ] 2025-06-25 10:08:41.987 - [任务 24][local_pg - Copy] - Starting batch read from table: table_33 
[TRACE] 2025-06-25 10:08:41.987 - [任务 24][local_pg - Copy] - Table table_33 is going to be initial synced 
[INFO ] 2025-06-25 10:08:42.561 - [任务 24][local_pg - Copy] - Table table_33 has been completed batch read 
[INFO ] 2025-06-25 10:08:42.561 - [任务 24][local_pg - Copy] - Starting batch read from table: table_34 
[TRACE] 2025-06-25 10:08:42.561 - [任务 24][local_pg - Copy] - Table table_34 is going to be initial synced 
[INFO ] 2025-06-25 10:08:43.258 - [任务 24][local_pg - Copy] - Table table_34 has been completed batch read 
[INFO ] 2025-06-25 10:08:43.258 - [任务 24][local_pg - Copy] - Starting batch read from table: table_35 
[TRACE] 2025-06-25 10:08:43.459 - [任务 24][local_pg - Copy] - Table table_35 is going to be initial synced 
[INFO ] 2025-06-25 10:08:44.048 - [任务 24][local_pg - Copy] - Table table_35 has been completed batch read 
[INFO ] 2025-06-25 10:08:44.048 - [任务 24][local_pg - Copy] - Starting batch read from table: table_36 
[TRACE] 2025-06-25 10:08:44.048 - [任务 24][local_pg - Copy] - Table table_36 is going to be initial synced 
[INFO ] 2025-06-25 10:08:44.890 - [任务 24][local_pg - Copy] - Table table_36 has been completed batch read 
[INFO ] 2025-06-25 10:08:44.890 - [任务 24][local_pg - Copy] - Starting batch read from table: table_37 
[TRACE] 2025-06-25 10:08:44.890 - [任务 24][local_pg - Copy] - Table table_37 is going to be initial synced 
[INFO ] 2025-06-25 10:08:45.514 - [任务 24][local_pg - Copy] - Table table_37 has been completed batch read 
[INFO ] 2025-06-25 10:08:45.518 - [任务 24][local_pg - Copy] - Starting batch read from table: table_38 
[TRACE] 2025-06-25 10:08:45.518 - [任务 24][local_pg - Copy] - Table table_38 is going to be initial synced 
[INFO ] 2025-06-25 10:08:46.246 - [任务 24][local_pg - Copy] - Table table_38 has been completed batch read 
[INFO ] 2025-06-25 10:08:46.246 - [任务 24][local_pg - Copy] - Starting batch read from table: table_39 
[TRACE] 2025-06-25 10:08:46.246 - [任务 24][local_pg - Copy] - Table table_39 is going to be initial synced 
[INFO ] 2025-06-25 10:08:46.655 - [任务 24][local_pg - Copy] - Table table_39 has been completed batch read 
[INFO ] 2025-06-25 10:08:46.655 - [任务 24][local_pg - Copy] - Starting batch read from table: table_20 
[TRACE] 2025-06-25 10:08:46.655 - [任务 24][local_pg - Copy] - Table table_20 is going to be initial synced 
[INFO ] 2025-06-25 10:08:47.651 - [任务 24][local_pg - Copy] - Table table_20 has been completed batch read 
[INFO ] 2025-06-25 10:08:47.652 - [任务 24][local_pg - Copy] - Starting batch read from table: table_21 
[TRACE] 2025-06-25 10:08:47.652 - [任务 24][local_pg - Copy] - Table table_21 is going to be initial synced 
[INFO ] 2025-06-25 10:08:48.321 - [任务 24][local_pg - Copy] - Table table_21 has been completed batch read 
[INFO ] 2025-06-25 10:08:48.322 - [任务 24][local_pg - Copy] - Starting batch read from table: table_1 
[TRACE] 2025-06-25 10:08:48.322 - [任务 24][local_pg - Copy] - Table table_1 is going to be initial synced 
[INFO ] 2025-06-25 10:08:49.309 - [任务 24][local_pg - Copy] - Table table_1 has been completed batch read 
[INFO ] 2025-06-25 10:08:49.309 - [任务 24][local_pg - Copy] - Starting batch read from table: table_22 
[TRACE] 2025-06-25 10:08:49.309 - [任务 24][local_pg - Copy] - Table table_22 is going to be initial synced 
[INFO ] 2025-06-25 10:08:49.999 - [任务 24][local_pg - Copy] - Table table_22 has been completed batch read 
[INFO ] 2025-06-25 10:08:50.000 - [任务 24][local_pg - Copy] - Starting batch read from table: table_2 
[TRACE] 2025-06-25 10:08:50.000 - [任务 24][local_pg - Copy] - Table table_2 is going to be initial synced 
[INFO ] 2025-06-25 10:08:50.906 - [任务 24][local_pg - Copy] - Table table_2 has been completed batch read 
[INFO ] 2025-06-25 10:08:50.906 - [任务 24][local_pg - Copy] - Starting batch read from table: table_3 
[TRACE] 2025-06-25 10:08:50.906 - [任务 24][local_pg - Copy] - Table table_3 is going to be initial synced 
[INFO ] 2025-06-25 10:08:51.893 - [任务 24][local_pg - Copy] - Table table_3 has been completed batch read 
[INFO ] 2025-06-25 10:08:51.893 - [任务 24][local_pg - Copy] - Starting batch read from table: table_23 
[TRACE] 2025-06-25 10:08:51.893 - [任务 24][local_pg - Copy] - Table table_23 is going to be initial synced 
[INFO ] 2025-06-25 10:08:52.777 - [任务 24][local_pg - Copy] - Table table_23 has been completed batch read 
[INFO ] 2025-06-25 10:08:52.777 - [任务 24][local_pg - Copy] - Starting batch read from table: table_4 
[TRACE] 2025-06-25 10:08:52.777 - [任务 24][local_pg - Copy] - Table table_4 is going to be initial synced 
[INFO ] 2025-06-25 10:08:53.313 - [任务 24][local_pg - Copy] - Table table_4 has been completed batch read 
[INFO ] 2025-06-25 10:08:53.314 - [任务 24][local_pg - Copy] - Starting batch read from table: table_24 
[TRACE] 2025-06-25 10:08:53.314 - [任务 24][local_pg - Copy] - Table table_24 is going to be initial synced 
[INFO ] 2025-06-25 10:08:54.486 - [任务 24][local_pg - Copy] - Table table_24 has been completed batch read 
[INFO ] 2025-06-25 10:08:54.487 - [任务 24][local_pg - Copy] - Starting batch read from table: table_25 
[TRACE] 2025-06-25 10:08:54.487 - [任务 24][local_pg - Copy] - Table table_25 is going to be initial synced 
[INFO ] 2025-06-25 10:08:55.475 - [任务 24][local_pg - Copy] - Table table_25 has been completed batch read 
[INFO ] 2025-06-25 10:08:55.475 - [任务 24][local_pg - Copy] - Starting batch read from table: table_5 
[TRACE] 2025-06-25 10:08:55.475 - [任务 24][local_pg - Copy] - Table table_5 is going to be initial synced 
[INFO ] 2025-06-25 10:08:56.403 - [任务 24][local_pg - Copy] - Table table_5 has been completed batch read 
[INFO ] 2025-06-25 10:08:56.404 - [任务 24][local_pg - Copy] - Starting batch read from table: table_26 
[TRACE] 2025-06-25 10:08:56.404 - [任务 24][local_pg - Copy] - Table table_26 is going to be initial synced 
[INFO ] 2025-06-25 10:08:57.503 - [任务 24][local_pg - Copy] - Table table_26 has been completed batch read 
[INFO ] 2025-06-25 10:08:57.505 - [任务 24][local_pg - Copy] - Starting batch read from table: table_6 
[TRACE] 2025-06-25 10:08:57.505 - [任务 24][local_pg - Copy] - Table table_6 is going to be initial synced 
[INFO ] 2025-06-25 10:08:58.580 - [任务 24][local_pg - Copy] - Table table_6 has been completed batch read 
[INFO ] 2025-06-25 10:08:58.580 - [任务 24][local_pg - Copy] - Starting batch read from table: table_27 
[TRACE] 2025-06-25 10:08:58.580 - [任务 24][local_pg - Copy] - Table table_27 is going to be initial synced 
[INFO ] 2025-06-25 10:08:59.007 - [任务 24][local_pg - Copy] - Table table_27 has been completed batch read 
[INFO ] 2025-06-25 10:08:59.007 - [任务 24][local_pg - Copy] - Starting batch read from table: table_7 
[TRACE] 2025-06-25 10:08:59.007 - [任务 24][local_pg - Copy] - Table table_7 is going to be initial synced 
[INFO ] 2025-06-25 10:08:59.659 - [任务 24][local_pg - Copy] - Table table_7 has been completed batch read 
[INFO ] 2025-06-25 10:08:59.660 - [任务 24][local_pg - Copy] - Starting batch read from table: table_28 
[TRACE] 2025-06-25 10:08:59.660 - [任务 24][local_pg - Copy] - Table table_28 is going to be initial synced 
[INFO ] 2025-06-25 10:09:00.112 - [任务 24][local_pg - Copy] - Table table_28 has been completed batch read 
[INFO ] 2025-06-25 10:09:00.112 - [任务 24][local_pg - Copy] - Starting batch read from table: table_8 
[TRACE] 2025-06-25 10:09:00.112 - [任务 24][local_pg - Copy] - Table table_8 is going to be initial synced 
[INFO ] 2025-06-25 10:09:01.106 - [任务 24][local_pg - Copy] - Table table_8 has been completed batch read 
[INFO ] 2025-06-25 10:09:01.106 - [任务 24][local_pg - Copy] - Starting batch read from table: table_29 
[TRACE] 2025-06-25 10:09:01.106 - [任务 24][local_pg - Copy] - Table table_29 is going to be initial synced 
[INFO ] 2025-06-25 10:09:01.503 - [任务 24][local_pg - Copy] - Table table_29 has been completed batch read 
[INFO ] 2025-06-25 10:09:01.503 - [任务 24][local_pg - Copy] - Starting batch read from table: table_9 
[TRACE] 2025-06-25 10:09:01.503 - [任务 24][local_pg - Copy] - Table table_9 is going to be initial synced 
[INFO ] 2025-06-25 10:09:02.065 - [任务 24][local_pg - Copy] - Table table_9 has been completed batch read 
[TRACE] 2025-06-25 10:09:02.065 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:09:02.066 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:09:02.066 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:09:02.066 - [任务 24][local_pg - Copy] - Initial sync completed 
[TRACE] 2025-06-25 10:09:02.067 - [任务 24][local_pg - Copy] - Starting stream read, table list: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:09:02.067 - [任务 24][local_pg - Copy] - Starting incremental sync using database log parser 
[WARN ] 2025-06-25 10:09:02.076 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[TRACE] 2025-06-25 10:09:02.076 - [任务 24][local_pg] - Process after table "table_16" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.076 - [任务 24][local_pg] - Process after table "table_40" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.076 - [任务 24][local_pg] - Process after table "table_17" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.076 - [任务 24][local_pg] - Process after table "table_41" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.076 - [任务 24][local_pg] - Process after table "table_15" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.077 - [任务 24][local_pg] - Process after table "table_42" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.077 - [任务 24][local_pg] - Process after table "table_19" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.077 - [任务 24][local_pg] - Process after table "table_43" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.077 - [任务 24][local_pg] - Process after table "table_45" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.077 - [任务 24][local_pg] - Process after table "table_46" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.078 - [任务 24][local_pg] - Process after table "table_44" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.078 - [任务 24][local_pg] - Process after table "table_30" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.078 - [任务 24][local_pg] - Process after table "table_48" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.078 - [任务 24][local_pg] - Process after table "table_47" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.078 - [任务 24][local_pg] - Process after table "table_31" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.078 - [任务 24][local_pg] - Process after table "table_50" initial sync finished, cost: 2 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_49" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_34" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_35" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_33" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_32" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_39" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_37" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_21" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_20" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.079 - [任务 24][local_pg] - Process after table "table_38" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.080 - [任务 24][local_pg] - Process after table "table_22" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.080 - [任务 24][local_pg] - Process after table "table_3" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.080 - [任务 24][local_pg] - Process after table "table_23" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.080 - [任务 24][local_pg] - Process after table "table_1" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.080 - [任务 24][local_pg] - Process after table "table_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.080 - [任务 24][local_pg] - Process after table "table_36" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.080 - [任务 24][local_pg] - Process after table "table_4" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_5" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_26" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_25" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_24" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_6" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-25 10:09:02.081 - [任务 24][local_pg - Copy] - Using an existing logical replication slot, slotName:tapdata_cdc_cb2d0822_9eb1_410c_aab7_4a33b559761d 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_27" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_9" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_28" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_29" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_8" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.081 - [任务 24][local_pg] - Process after table "table_7" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:09:02.083 - [任务 24][local_pg] - Process after table "table_12" initial sync finished, cost: 6 ms 
[TRACE] 2025-06-25 10:09:02.084 - [任务 24][local_pg] - Process after table "table_13" initial sync finished, cost: 8 ms 
[TRACE] 2025-06-25 10:09:02.089 - [任务 24][local_pg] - Process after table "table_14" initial sync finished, cost: 10 ms 
[TRACE] 2025-06-25 10:09:02.089 - [任务 24][local_pg] - Process after table "table_10" initial sync finished, cost: 14 ms 
[TRACE] 2025-06-25 10:09:02.089 - [任务 24][local_pg] - Process after table "table_11" initial sync finished, cost: 14 ms 
[INFO ] 2025-06-25 10:09:02.089 - [任务 24][local_pg] - Process after all table(s) initial sync are finished，table number: 49 
[TRACE] 2025-06-25 10:09:02.290 - [任务 24][local_pg - Copy] - Connector PostgreSQL incremental start succeed, tables: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], data change syncing 
[TRACE] 2025-06-25 10:09:27.258 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:09:27.323 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:09:27.324 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817308440 
[TRACE] 2025-06-25 10:09:27.324 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817308440 
[TRACE] 2025-06-25 10:09:27.324 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:09:27.324 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:09:27.324 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 66 ms 
[TRACE] 2025-06-25 10:09:27.324 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:09:27.333 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817308447 
[TRACE] 2025-06-25 10:09:27.333 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817308447 
[TRACE] 2025-06-25 10:09:27.333 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:09:27.333 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:09:27.334 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 9 ms 
[TRACE] 2025-06-25 10:14:35.981 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:14:36.187 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:14:36.611 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:14:36.704 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:14:36.705 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:14:36.905 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:14:36.913 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:14:36.913 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:14:36.913 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:14:36.913 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:14:37.119 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:14:37.298 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:14:37.298 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:14:37.298 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:14:37.312 - [任务 24][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-25 10:14:37.397 - [任务 24][local_pg] - The table table_50 has already exist. 
[TRACE] 2025-06-25 10:14:37.397 - [任务 24][local_pg] - The table table_10 has already exist. 
[INFO ] 2025-06-25 10:14:37.416 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:14:37.416 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:14:37.416 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[TRACE] 2025-06-25 10:14:37.416 - [任务 24][local_pg - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-25 10:14:37.436 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[TRACE] 2025-06-25 10:14:37.436 - [任务 24][local_pg] - The table table_11 has already exist. 
[INFO ] 2025-06-25 10:14:37.452 - [任务 24][local_pg - Copy] - new logical replication slot created, slotName:tapdata_cdc_e60c30ba_437f_4834_856b_3be8c90e8ea6 
[INFO ] 2025-06-25 10:14:37.453 - [任务 24][local_pg - Copy] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-06-25 10:14:37.500 - [任务 24][local_pg] - The table table_12 has already exist. 
[TRACE] 2025-06-25 10:14:37.500 - [任务 24][local_pg] - The table table_13 has already exist. 
[TRACE] 2025-06-25 10:14:37.573 - [任务 24][local_pg] - The table table_14 has already exist. 
[TRACE] 2025-06-25 10:14:37.573 - [任务 24][local_pg] - The table table_15 has already exist. 
[TRACE] 2025-06-25 10:14:37.606 - [任务 24][local_pg] - The table table_16 has already exist. 
[INFO ] 2025-06-25 10:14:37.606 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:14:37.617 - [任务 24][local_pg - Copy] - Initial sync started 
[INFO ] 2025-06-25 10:14:37.618 - [任务 24][local_pg - Copy] - Starting batch read from table: table_50 
[TRACE] 2025-06-25 10:14:37.641 - [任务 24][local_pg - Copy] - Table table_50 is going to be initial synced 
[TRACE] 2025-06-25 10:14:37.642 - [任务 24][local_pg] - The table table_17 has already exist. 
[TRACE] 2025-06-25 10:14:37.742 - [任务 24][local_pg] - The table table_19 has already exist. 
[TRACE] 2025-06-25 10:14:37.742 - [任务 24][local_pg] - The table table_40 has already exist. 
[TRACE] 2025-06-25 10:14:37.816 - [任务 24][local_pg] - The table table_41 has already exist. 
[TRACE] 2025-06-25 10:14:37.816 - [任务 24][local_pg] - The table table_42 has already exist. 
[TRACE] 2025-06-25 10:14:37.887 - [任务 24][local_pg] - The table table_43 has already exist. 
[TRACE] 2025-06-25 10:14:37.887 - [任务 24][local_pg] - The table table_44 has already exist. 
[TRACE] 2025-06-25 10:14:38.025 - [任务 24][local_pg] - The table table_45 has already exist. 
[TRACE] 2025-06-25 10:14:38.025 - [任务 24][local_pg] - The table table_46 has already exist. 
[TRACE] 2025-06-25 10:14:38.060 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 10:14:38.061 - [任务 24][local_pg] - The table table_47 has already exist. 
[TRACE] 2025-06-25 10:14:38.135 - [任务 24][local_pg] - The table table_48 has already exist. 
[TRACE] 2025-06-25 10:14:38.135 - [任务 24][local_pg] - The table table_49 has already exist. 
[TRACE] 2025-06-25 10:14:38.193 - [任务 24][local_pg] - The table table_30 has already exist. 
[TRACE] 2025-06-25 10:14:38.193 - [任务 24][local_pg] - The table table_31 has already exist. 
[TRACE] 2025-06-25 10:14:38.255 - [任务 24][local_pg] - The table table_32 has already exist. 
[TRACE] 2025-06-25 10:14:38.256 - [任务 24][local_pg] - The table table_33 has already exist. 
[TRACE] 2025-06-25 10:14:38.315 - [任务 24][local_pg] - The table table_34 has already exist. 
[TRACE] 2025-06-25 10:14:38.315 - [任务 24][local_pg] - The table table_35 has already exist. 
[TRACE] 2025-06-25 10:14:38.372 - [任务 24][local_pg] - The table table_36 has already exist. 
[TRACE] 2025-06-25 10:14:38.373 - [任务 24][local_pg] - The table table_37 has already exist. 
[TRACE] 2025-06-25 10:14:38.429 - [任务 24][local_pg] - The table table_38 has already exist. 
[TRACE] 2025-06-25 10:14:38.429 - [任务 24][local_pg] - The table table_39 has already exist. 
[TRACE] 2025-06-25 10:14:38.487 - [任务 24][local_pg] - The table table_20 has already exist. 
[TRACE] 2025-06-25 10:14:38.487 - [任务 24][local_pg] - The table table_21 has already exist. 
[TRACE] 2025-06-25 10:14:38.546 - [任务 24][local_pg] - The table table_1 has already exist. 
[TRACE] 2025-06-25 10:14:38.546 - [任务 24][local_pg] - The table table_22 has already exist. 
[TRACE] 2025-06-25 10:14:38.605 - [任务 24][local_pg] - The table table_2 has already exist. 
[TRACE] 2025-06-25 10:14:38.605 - [任务 24][local_pg] - The table table_3 has already exist. 
[TRACE] 2025-06-25 10:14:38.675 - [任务 24][local_pg] - The table table_23 has already exist. 
[TRACE] 2025-06-25 10:14:38.675 - [任务 24][local_pg] - The table table_4 has already exist. 
[TRACE] 2025-06-25 10:14:38.731 - [任务 24][local_pg] - The table table_24 has already exist. 
[TRACE] 2025-06-25 10:14:38.731 - [任务 24][local_pg] - The table table_25 has already exist. 
[TRACE] 2025-06-25 10:14:38.790 - [任务 24][local_pg] - The table table_5 has already exist. 
[TRACE] 2025-06-25 10:14:38.790 - [任务 24][local_pg] - The table table_26 has already exist. 
[TRACE] 2025-06-25 10:14:38.845 - [任务 24][local_pg] - The table table_6 has already exist. 
[TRACE] 2025-06-25 10:14:38.845 - [任务 24][local_pg] - The table table_27 has already exist. 
[TRACE] 2025-06-25 10:14:38.902 - [任务 24][local_pg] - The table table_7 has already exist. 
[TRACE] 2025-06-25 10:14:38.902 - [任务 24][local_pg] - The table table_28 has already exist. 
[TRACE] 2025-06-25 10:14:39.021 - [任务 24][local_pg] - The table table_8 has already exist. 
[TRACE] 2025-06-25 10:14:39.021 - [任务 24][local_pg] - The table table_29 has already exist. 
[TRACE] 2025-06-25 10:14:39.226 - [任务 24][local_pg] - The table table_9 has already exist. 
[INFO ] 2025-06-25 10:14:40.174 - [任务 24][local_pg - Copy] - Table table_50 has been completed batch read 
[INFO ] 2025-06-25 10:14:40.177 - [任务 24][local_pg - Copy] - Starting batch read from table: table_10 
[TRACE] 2025-06-25 10:14:40.177 - [任务 24][local_pg - Copy] - Table table_10 is going to be initial synced 
[INFO ] 2025-06-25 10:14:40.569 - [任务 24][local_pg - Copy] - Table table_10 has been completed batch read 
[INFO ] 2025-06-25 10:14:40.569 - [任务 24][local_pg - Copy] - Starting batch read from table: table_11 
[TRACE] 2025-06-25 10:14:40.569 - [任务 24][local_pg - Copy] - Table table_11 is going to be initial synced 
[INFO ] 2025-06-25 10:14:41.282 - [任务 24][local_pg - Copy] - Table table_11 has been completed batch read 
[INFO ] 2025-06-25 10:14:41.283 - [任务 24][local_pg - Copy] - Starting batch read from table: table_12 
[TRACE] 2025-06-25 10:14:41.283 - [任务 24][local_pg - Copy] - Table table_12 is going to be initial synced 
[INFO ] 2025-06-25 10:14:42.112 - [任务 24][local_pg - Copy] - Table table_12 has been completed batch read 
[INFO ] 2025-06-25 10:14:42.112 - [任务 24][local_pg - Copy] - Starting batch read from table: table_13 
[TRACE] 2025-06-25 10:14:42.113 - [任务 24][local_pg - Copy] - Table table_13 is going to be initial synced 
[INFO ] 2025-06-25 10:14:42.564 - [任务 24][local_pg - Copy] - Table table_13 has been completed batch read 
[INFO ] 2025-06-25 10:14:42.564 - [任务 24][local_pg - Copy] - Starting batch read from table: table_14 
[TRACE] 2025-06-25 10:14:42.564 - [任务 24][local_pg - Copy] - Table table_14 is going to be initial synced 
[INFO ] 2025-06-25 10:14:43.435 - [任务 24][local_pg - Copy] - Table table_14 has been completed batch read 
[INFO ] 2025-06-25 10:14:43.435 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 10:14:43.435 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[INFO ] 2025-06-25 10:14:44.243 - [任务 24][local_pg - Copy] - Table table_15 has been completed batch read 
[INFO ] 2025-06-25 10:14:44.243 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 10:14:44.243 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[TRACE] 2025-06-25 10:14:44.279 - [任务 24][local_pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  
[ERROR] 2025-06-25 10:14:44.280 - [任务 24][local_pg] - Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  <-- Error Message -->
Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type: 

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectWriteType(PostgresExceptionCollector.java:66)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:165)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:503)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:848)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:808)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:701)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 98 more

[TRACE] 2025-06-25 10:14:44.280 - [任务 24][local_pg] - Job suspend in error handle 
[INFO ] 2025-06-25 10:14:44.689 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 10:14:45.255 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:14:45.257 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:14:45.257 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:14:45.258 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:14:45.258 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:14:45.261 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817677169 
[TRACE] 2025-06-25 10:14:45.261 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817677169 
[TRACE] 2025-06-25 10:14:45.262 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:14:45.262 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:14:45.263 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 8 ms 
[TRACE] 2025-06-25 10:14:45.263 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:14:45.279 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817677156 
[TRACE] 2025-06-25 10:14:45.279 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817677156 
[TRACE] 2025-06-25 10:14:45.279 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:14:45.279 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:14:45.480 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 16 ms 
[INFO ] 2025-06-25 10:14:49.735 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-25 10:14:54.576 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 10:14:54.781 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 10:14:55.584 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@38a3a335 
[TRACE] 2025-06-25 10:14:55.587 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@26c92687 
[TRACE] 2025-06-25 10:14:55.588 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 10:14:55.720 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 10:14:55.723 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 10:14:55.730 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 10:14:55.789 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:14:55.793 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:15:25.468 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:15:25.674 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:15:25.878 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:15:25.933 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:15:25.934 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:15:26.006 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:15:26.006 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:15:26.006 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:15:26.007 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:15:26.007 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:15:26.007 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:15:26.160 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:15:26.160 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:15:26.160 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[INFO ] 2025-06-25 10:15:26.161 - [任务 24][local_pg - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-25 10:15:26.274 - [任务 24][local_pg - Copy] - Use existing batch read offset: {"table_10":{"batch_read_connector_status":"OVER"},"table_11":{"batch_read_connector_status":"OVER"},"table_12":{"batch_read_connector_status":"OVER"},"table_13":{"batch_read_connector_status":"OVER"},"table_14":{"batch_read_connector_status":"OVER"},"table_15":{"batch_read_connector_status":"OVER"},"table_50":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:15:26.274 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:15:26.274 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:15:26.274 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:15:26.311 - [任务 24][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-25 10:15:26.311 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:16:28.108 - [任务 24][local_pg - Copy] - Initial sync started 
[TRACE] 2025-06-25 10:16:28.109 - [任务 24][local_pg - Copy] - Skip table [table_50] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:16:28.109 - [任务 24][local_pg - Copy] - Skip table [table_10] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:16:28.109 - [任务 24][local_pg - Copy] - Skip table [table_11] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:16:28.109 - [任务 24][local_pg - Copy] - Skip table [table_12] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:16:28.109 - [任务 24][local_pg - Copy] - Skip table [table_13] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:16:28.109 - [任务 24][local_pg - Copy] - Skip table [table_14] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:16:28.109 - [任务 24][local_pg - Copy] - Skip table [table_15] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2025-06-25 10:16:28.110 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 10:16:28.312 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[TRACE] 2025-06-25 10:16:28.735 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[INFO ] 2025-06-25 10:16:29.406 - [任务 24][local_pg - Copy] - Table table_16 has been completed batch read 
[INFO ] 2025-06-25 10:16:29.406 - [任务 24][local_pg - Copy] - Starting batch read from table: table_17 
[TRACE] 2025-06-25 10:16:29.406 - [任务 24][local_pg - Copy] - Table table_17 is going to be initial synced 
[INFO ] 2025-06-25 10:16:30.393 - [任务 24][local_pg - Copy] - Table table_17 has been completed batch read 
[INFO ] 2025-06-25 10:16:30.394 - [任务 24][local_pg - Copy] - Starting batch read from table: table_19 
[TRACE] 2025-06-25 10:16:30.394 - [任务 24][local_pg - Copy] - Table table_19 is going to be initial synced 
[INFO ] 2025-06-25 10:16:31.070 - [任务 24][local_pg - Copy] - Table table_19 has been completed batch read 
[INFO ] 2025-06-25 10:16:31.070 - [任务 24][local_pg - Copy] - Starting batch read from table: table_40 
[TRACE] 2025-06-25 10:16:31.070 - [任务 24][local_pg - Copy] - Table table_40 is going to be initial synced 
[INFO ] 2025-06-25 10:16:31.708 - [任务 24][local_pg - Copy] - Table table_40 has been completed batch read 
[INFO ] 2025-06-25 10:16:31.709 - [任务 24][local_pg - Copy] - Starting batch read from table: table_41 
[TRACE] 2025-06-25 10:16:31.709 - [任务 24][local_pg - Copy] - Table table_41 is going to be initial synced 
[INFO ] 2025-06-25 10:16:32.506 - [任务 24][local_pg - Copy] - Table table_41 has been completed batch read 
[INFO ] 2025-06-25 10:16:32.507 - [任务 24][local_pg - Copy] - Starting batch read from table: table_42 
[TRACE] 2025-06-25 10:16:32.507 - [任务 24][local_pg - Copy] - Table table_42 is going to be initial synced 
[INFO ] 2025-06-25 10:16:32.996 - [任务 24][local_pg - Copy] - Table table_42 has been completed batch read 
[INFO ] 2025-06-25 10:16:32.997 - [任务 24][local_pg - Copy] - Starting batch read from table: table_43 
[TRACE] 2025-06-25 10:16:32.997 - [任务 24][local_pg - Copy] - Table table_43 is going to be initial synced 
[INFO ] 2025-06-25 10:16:33.589 - [任务 24][local_pg - Copy] - Table table_43 has been completed batch read 
[INFO ] 2025-06-25 10:16:33.590 - [任务 24][local_pg - Copy] - Starting batch read from table: table_44 
[TRACE] 2025-06-25 10:16:33.590 - [任务 24][local_pg - Copy] - Table table_44 is going to be initial synced 
[INFO ] 2025-06-25 10:16:34.105 - [任务 24][local_pg - Copy] - Table table_44 has been completed batch read 
[INFO ] 2025-06-25 10:16:34.105 - [任务 24][local_pg - Copy] - Starting batch read from table: table_45 
[TRACE] 2025-06-25 10:16:34.105 - [任务 24][local_pg - Copy] - Table table_45 is going to be initial synced 
[INFO ] 2025-06-25 10:16:34.691 - [任务 24][local_pg - Copy] - Table table_45 has been completed batch read 
[INFO ] 2025-06-25 10:16:34.691 - [任务 24][local_pg - Copy] - Starting batch read from table: table_46 
[TRACE] 2025-06-25 10:16:34.691 - [任务 24][local_pg - Copy] - Table table_46 is going to be initial synced 
[INFO ] 2025-06-25 10:16:35.780 - [任务 24][local_pg - Copy] - Table table_46 has been completed batch read 
[INFO ] 2025-06-25 10:16:35.781 - [任务 24][local_pg - Copy] - Starting batch read from table: table_47 
[TRACE] 2025-06-25 10:16:35.981 - [任务 24][local_pg - Copy] - Table table_47 is going to be initial synced 
[INFO ] 2025-06-25 10:16:36.505 - [任务 24][local_pg - Copy] - Table table_47 has been completed batch read 
[INFO ] 2025-06-25 10:16:36.505 - [任务 24][local_pg - Copy] - Starting batch read from table: table_48 
[TRACE] 2025-06-25 10:16:36.706 - [任务 24][local_pg - Copy] - Table table_48 is going to be initial synced 
[INFO ] 2025-06-25 10:16:37.421 - [任务 24][local_pg - Copy] - Table table_48 has been completed batch read 
[INFO ] 2025-06-25 10:16:37.421 - [任务 24][local_pg - Copy] - Starting batch read from table: table_49 
[TRACE] 2025-06-25 10:16:37.421 - [任务 24][local_pg - Copy] - Table table_49 is going to be initial synced 
[INFO ] 2025-06-25 10:16:38.363 - [任务 24][local_pg - Copy] - Table table_49 has been completed batch read 
[INFO ] 2025-06-25 10:16:38.363 - [任务 24][local_pg - Copy] - Starting batch read from table: table_30 
[TRACE] 2025-06-25 10:16:38.363 - [任务 24][local_pg - Copy] - Table table_30 is going to be initial synced 
[INFO ] 2025-06-25 10:16:39.342 - [任务 24][local_pg - Copy] - Table table_30 has been completed batch read 
[INFO ] 2025-06-25 10:16:39.342 - [任务 24][local_pg - Copy] - Starting batch read from table: table_31 
[TRACE] 2025-06-25 10:16:39.342 - [任务 24][local_pg - Copy] - Table table_31 is going to be initial synced 
[INFO ] 2025-06-25 10:16:39.906 - [任务 24][local_pg - Copy] - Table table_31 has been completed batch read 
[INFO ] 2025-06-25 10:16:39.907 - [任务 24][local_pg - Copy] - Starting batch read from table: table_32 
[TRACE] 2025-06-25 10:16:39.907 - [任务 24][local_pg - Copy] - Table table_32 is going to be initial synced 
[INFO ] 2025-06-25 10:16:40.738 - [任务 24][local_pg - Copy] - Table table_32 has been completed batch read 
[INFO ] 2025-06-25 10:16:40.738 - [任务 24][local_pg - Copy] - Starting batch read from table: table_33 
[TRACE] 2025-06-25 10:16:40.738 - [任务 24][local_pg - Copy] - Table table_33 is going to be initial synced 
[INFO ] 2025-06-25 10:16:41.724 - [任务 24][local_pg - Copy] - Table table_33 has been completed batch read 
[INFO ] 2025-06-25 10:16:41.724 - [任务 24][local_pg - Copy] - Starting batch read from table: table_34 
[TRACE] 2025-06-25 10:16:41.724 - [任务 24][local_pg - Copy] - Table table_34 is going to be initial synced 
[INFO ] 2025-06-25 10:16:42.434 - [任务 24][local_pg - Copy] - Table table_34 has been completed batch read 
[INFO ] 2025-06-25 10:16:42.434 - [任务 24][local_pg - Copy] - Starting batch read from table: table_35 
[TRACE] 2025-06-25 10:16:42.435 - [任务 24][local_pg - Copy] - Table table_35 is going to be initial synced 
[INFO ] 2025-06-25 10:16:43.160 - [任务 24][local_pg - Copy] - Table table_35 has been completed batch read 
[INFO ] 2025-06-25 10:16:43.160 - [任务 24][local_pg - Copy] - Starting batch read from table: table_36 
[TRACE] 2025-06-25 10:16:43.160 - [任务 24][local_pg - Copy] - Table table_36 is going to be initial synced 
[INFO ] 2025-06-25 10:16:44.043 - [任务 24][local_pg - Copy] - Table table_36 has been completed batch read 
[INFO ] 2025-06-25 10:16:44.043 - [任务 24][local_pg - Copy] - Starting batch read from table: table_37 
[TRACE] 2025-06-25 10:16:44.043 - [任务 24][local_pg - Copy] - Table table_37 is going to be initial synced 
[INFO ] 2025-06-25 10:16:44.648 - [任务 24][local_pg - Copy] - Table table_37 has been completed batch read 
[INFO ] 2025-06-25 10:16:44.648 - [任务 24][local_pg - Copy] - Starting batch read from table: table_38 
[TRACE] 2025-06-25 10:16:44.648 - [任务 24][local_pg - Copy] - Table table_38 is going to be initial synced 
[INFO ] 2025-06-25 10:16:45.363 - [任务 24][local_pg - Copy] - Table table_38 has been completed batch read 
[INFO ] 2025-06-25 10:16:45.363 - [任务 24][local_pg - Copy] - Starting batch read from table: table_39 
[TRACE] 2025-06-25 10:16:45.363 - [任务 24][local_pg - Copy] - Table table_39 is going to be initial synced 
[INFO ] 2025-06-25 10:16:45.781 - [任务 24][local_pg - Copy] - Table table_39 has been completed batch read 
[INFO ] 2025-06-25 10:16:45.781 - [任务 24][local_pg - Copy] - Starting batch read from table: table_20 
[TRACE] 2025-06-25 10:16:45.782 - [任务 24][local_pg - Copy] - Table table_20 is going to be initial synced 
[INFO ] 2025-06-25 10:16:46.790 - [任务 24][local_pg - Copy] - Table table_20 has been completed batch read 
[INFO ] 2025-06-25 10:16:46.992 - [任务 24][local_pg - Copy] - Starting batch read from table: table_21 
[TRACE] 2025-06-25 10:16:46.994 - [任务 24][local_pg - Copy] - Table table_21 is going to be initial synced 
[INFO ] 2025-06-25 10:16:47.284 - [任务 24][local_pg - Copy] - Table table_21 has been completed batch read 
[INFO ] 2025-06-25 10:16:47.285 - [任务 24][local_pg - Copy] - Starting batch read from table: table_1 
[TRACE] 2025-06-25 10:16:47.285 - [任务 24][local_pg - Copy] - Table table_1 is going to be initial synced 
[INFO ] 2025-06-25 10:16:48.226 - [任务 24][local_pg - Copy] - Table table_1 has been completed batch read 
[INFO ] 2025-06-25 10:16:48.226 - [任务 24][local_pg - Copy] - Starting batch read from table: table_22 
[TRACE] 2025-06-25 10:16:48.226 - [任务 24][local_pg - Copy] - Table table_22 is going to be initial synced 
[INFO ] 2025-06-25 10:16:48.895 - [任务 24][local_pg - Copy] - Table table_22 has been completed batch read 
[INFO ] 2025-06-25 10:16:48.895 - [任务 24][local_pg - Copy] - Starting batch read from table: table_2 
[TRACE] 2025-06-25 10:16:48.895 - [任务 24][local_pg - Copy] - Table table_2 is going to be initial synced 
[INFO ] 2025-06-25 10:16:49.823 - [任务 24][local_pg - Copy] - Table table_2 has been completed batch read 
[INFO ] 2025-06-25 10:16:49.823 - [任务 24][local_pg - Copy] - Starting batch read from table: table_3 
[TRACE] 2025-06-25 10:16:49.823 - [任务 24][local_pg - Copy] - Table table_3 is going to be initial synced 
[INFO ] 2025-06-25 10:16:50.893 - [任务 24][local_pg - Copy] - Table table_3 has been completed batch read 
[INFO ] 2025-06-25 10:16:50.893 - [任务 24][local_pg - Copy] - Starting batch read from table: table_23 
[TRACE] 2025-06-25 10:16:50.893 - [任务 24][local_pg - Copy] - Table table_23 is going to be initial synced 
[INFO ] 2025-06-25 10:16:51.904 - [任务 24][local_pg - Copy] - Table table_23 has been completed batch read 
[INFO ] 2025-06-25 10:16:51.905 - [任务 24][local_pg - Copy] - Starting batch read from table: table_4 
[TRACE] 2025-06-25 10:16:51.905 - [任务 24][local_pg - Copy] - Table table_4 is going to be initial synced 
[INFO ] 2025-06-25 10:16:52.450 - [任务 24][local_pg - Copy] - Table table_4 has been completed batch read 
[INFO ] 2025-06-25 10:16:52.450 - [任务 24][local_pg - Copy] - Starting batch read from table: table_24 
[TRACE] 2025-06-25 10:16:52.450 - [任务 24][local_pg - Copy] - Table table_24 is going to be initial synced 
[INFO ] 2025-06-25 10:16:53.311 - [任务 24][local_pg - Copy] - Table table_24 has been completed batch read 
[INFO ] 2025-06-25 10:16:53.311 - [任务 24][local_pg - Copy] - Starting batch read from table: table_25 
[TRACE] 2025-06-25 10:16:53.311 - [任务 24][local_pg - Copy] - Table table_25 is going to be initial synced 
[INFO ] 2025-06-25 10:16:54.304 - [任务 24][local_pg - Copy] - Table table_25 has been completed batch read 
[INFO ] 2025-06-25 10:16:54.304 - [任务 24][local_pg - Copy] - Starting batch read from table: table_5 
[TRACE] 2025-06-25 10:16:54.304 - [任务 24][local_pg - Copy] - Table table_5 is going to be initial synced 
[INFO ] 2025-06-25 10:16:55.240 - [任务 24][local_pg - Copy] - Table table_5 has been completed batch read 
[INFO ] 2025-06-25 10:16:55.240 - [任务 24][local_pg - Copy] - Starting batch read from table: table_26 
[TRACE] 2025-06-25 10:16:55.240 - [任务 24][local_pg - Copy] - Table table_26 is going to be initial synced 
[INFO ] 2025-06-25 10:16:56.542 - [任务 24][local_pg - Copy] - Table table_26 has been completed batch read 
[INFO ] 2025-06-25 10:16:56.542 - [任务 24][local_pg - Copy] - Starting batch read from table: table_6 
[TRACE] 2025-06-25 10:16:56.542 - [任务 24][local_pg - Copy] - Table table_6 is going to be initial synced 
[INFO ] 2025-06-25 10:16:57.633 - [任务 24][local_pg - Copy] - Table table_6 has been completed batch read 
[INFO ] 2025-06-25 10:16:57.635 - [任务 24][local_pg - Copy] - Starting batch read from table: table_27 
[TRACE] 2025-06-25 10:16:57.635 - [任务 24][local_pg - Copy] - Table table_27 is going to be initial synced 
[INFO ] 2025-06-25 10:16:58.066 - [任务 24][local_pg - Copy] - Table table_27 has been completed batch read 
[INFO ] 2025-06-25 10:16:58.066 - [任务 24][local_pg - Copy] - Starting batch read from table: table_7 
[TRACE] 2025-06-25 10:16:58.066 - [任务 24][local_pg - Copy] - Table table_7 is going to be initial synced 
[INFO ] 2025-06-25 10:16:58.717 - [任务 24][local_pg - Copy] - Table table_7 has been completed batch read 
[INFO ] 2025-06-25 10:16:58.718 - [任务 24][local_pg - Copy] - Starting batch read from table: table_28 
[TRACE] 2025-06-25 10:16:58.718 - [任务 24][local_pg - Copy] - Table table_28 is going to be initial synced 
[INFO ] 2025-06-25 10:16:59.436 - [任务 24][local_pg - Copy] - Table table_28 has been completed batch read 
[INFO ] 2025-06-25 10:16:59.436 - [任务 24][local_pg - Copy] - Starting batch read from table: table_8 
[TRACE] 2025-06-25 10:16:59.436 - [任务 24][local_pg - Copy] - Table table_8 is going to be initial synced 
[INFO ] 2025-06-25 10:17:00.437 - [任务 24][local_pg - Copy] - Table table_8 has been completed batch read 
[INFO ] 2025-06-25 10:17:00.437 - [任务 24][local_pg - Copy] - Starting batch read from table: table_29 
[TRACE] 2025-06-25 10:17:00.437 - [任务 24][local_pg - Copy] - Table table_29 is going to be initial synced 
[INFO ] 2025-06-25 10:17:00.857 - [任务 24][local_pg - Copy] - Table table_29 has been completed batch read 
[INFO ] 2025-06-25 10:17:00.857 - [任务 24][local_pg - Copy] - Starting batch read from table: table_9 
[TRACE] 2025-06-25 10:17:00.857 - [任务 24][local_pg - Copy] - Table table_9 is going to be initial synced 
[INFO ] 2025-06-25 10:17:01.425 - [任务 24][local_pg - Copy] - Table table_9 has been completed batch read 
[TRACE] 2025-06-25 10:17:01.427 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:17:01.427 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:17:01.427 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:17:01.427 - [任务 24][local_pg - Copy] - Initial sync completed 
[TRACE] 2025-06-25 10:17:01.428 - [任务 24][local_pg - Copy] - Starting stream read, table list: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:17:01.429 - [任务 24][local_pg - Copy] - Starting incremental sync using database log parser 
[WARN ] 2025-06-25 10:17:01.440 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 10:17:01.440 - [任务 24][local_pg - Copy] - Using an existing logical replication slot, slotName:tapdata_cdc_e60c30ba_437f_4834_856b_3be8c90e8ea6 
[TRACE] 2025-06-25 10:17:01.644 - [任务 24][local_pg - Copy] - Connector PostgreSQL incremental start succeed, tables: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], data change syncing 
[TRACE] 2025-06-25 10:17:02.441 - [任务 24][local_pg] - Process after table "table_19" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.442 - [任务 24][local_pg] - Process after table "table_16" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.442 - [任务 24][local_pg] - Process after table "table_40" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.442 - [任务 24][local_pg] - Process after table "table_17" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.442 - [任务 24][local_pg] - Process after table "table_43" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.442 - [任务 24][local_pg] - Process after table "table_41" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.442 - [任务 24][local_pg] - Process after table "table_42" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.442 - [任务 24][local_pg] - Process after table "table_45" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.442 - [任务 24][local_pg] - Process after table "table_44" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.443 - [任务 24][local_pg] - Process after table "table_46" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.443 - [任务 24][local_pg] - Process after table "table_49" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.443 - [任务 24][local_pg] - Process after table "table_47" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.443 - [任务 24][local_pg] - Process after table "table_48" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.443 - [任务 24][local_pg] - Process after table "table_30" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.443 - [任务 24][local_pg] - Process after table "table_31" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_32" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_33" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_35" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_37" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_34" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_36" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_38" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_20" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_21" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_1" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.444 - [任务 24][local_pg] - Process after table "table_22" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.445 - [任务 24][local_pg] - Process after table "table_3" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.445 - [任务 24][local_pg] - Process after table "table_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.445 - [任务 24][local_pg] - Process after table "table_39" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.445 - [任务 24][local_pg] - Process after table "table_24" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.445 - [任务 24][local_pg] - Process after table "table_4" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.445 - [任务 24][local_pg] - Process after table "table_23" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.445 - [任务 24][local_pg] - Process after table "table_5" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.445 - [任务 24][local_pg] - Process after table "table_25" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.446 - [任务 24][local_pg] - Process after table "table_26" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.446 - [任务 24][local_pg] - Process after table "table_6" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.446 - [任务 24][local_pg] - Process after table "table_28" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.446 - [任务 24][local_pg] - Process after table "table_8" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.446 - [任务 24][local_pg] - Process after table "table_29" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.446 - [任务 24][local_pg] - Process after table "table_27" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.446 - [任务 24][local_pg] - Process after table "table_7" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.446 - [任务 24][local_pg] - Process after table "table_9" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:17:02.447 - [任务 24][local_pg] - Process after table "table_50" initial sync finished, cost: 6 ms 
[TRACE] 2025-06-25 10:17:02.451 - [任务 24][local_pg] - Process after table "table_12" initial sync finished, cost: 8 ms 
[TRACE] 2025-06-25 10:17:02.451 - [任务 24][local_pg] - Process after table "table_13" initial sync finished, cost: 10 ms 
[TRACE] 2025-06-25 10:17:02.452 - [任务 24][local_pg] - Process after table "table_10" initial sync finished, cost: 12 ms 
[TRACE] 2025-06-25 10:17:02.452 - [任务 24][local_pg] - Process after table "table_15" initial sync finished, cost: 12 ms 
[TRACE] 2025-06-25 10:17:02.454 - [任务 24][local_pg] - Process after table "table_14" initial sync finished, cost: 13 ms 
[TRACE] 2025-06-25 10:17:02.454 - [任务 24][local_pg] - Process after table "table_11" initial sync finished, cost: 14 ms 
[INFO ] 2025-06-25 10:17:02.654 - [任务 24][local_pg] - Process after all table(s) initial sync are finished，table number: 49 
[TRACE] 2025-06-25 10:17:37.753 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:17:37.894 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:17:37.894 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817726027 
[TRACE] 2025-06-25 10:17:37.894 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817726027 
[TRACE] 2025-06-25 10:17:37.895 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:17:37.895 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:17:37.895 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 143 ms 
[TRACE] 2025-06-25 10:17:37.895 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:17:37.906 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817726031 
[TRACE] 2025-06-25 10:17:37.906 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817726031 
[TRACE] 2025-06-25 10:17:37.906 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:17:37.906 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:17:37.906 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 11 ms 
[TRACE] 2025-06-25 10:17:46.031 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 10:17:46.866 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@9b75b8e 
[TRACE] 2025-06-25 10:17:46.866 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@586a3e53 
[TRACE] 2025-06-25 10:17:46.984 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 10:17:46.984 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 10:17:46.984 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 10:17:46.985 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 10:17:47.037 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:17:47.040 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:18:28.844 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:18:29.050 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:18:29.253 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:18:29.253 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:18:29.311 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:18:29.311 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:18:29.349 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:18:29.349 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:18:29.349 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:18:29.350 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:18:29.350 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:18:29.744 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:18:29.745 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:18:29.745 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[TRACE] 2025-06-25 10:18:29.745 - [任务 24][local_pg - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-25 10:18:29.767 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 10:18:29.767 - [任务 24][local_pg - Copy] - new logical replication slot created, slotName:tapdata_cdc_4bae6a8b_d8b0_4a04_b887_5cc0c0a605f7 
[INFO ] 2025-06-25 10:18:29.857 - [任务 24][local_pg - Copy] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:18:29.857 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:18:29.857 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:18:29.857 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:18:29.874 - [任务 24][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-25 10:18:29.874 - [任务 24][local_pg] - The table table_50 has already exist. 
[TRACE] 2025-06-25 10:18:29.918 - [任务 24][local_pg] - The table table_10 has already exist. 
[INFO ] 2025-06-25 10:18:29.918 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:18:29.922 - [任务 24][local_pg - Copy] - Initial sync started 
[INFO ] 2025-06-25 10:18:29.922 - [任务 24][local_pg - Copy] - Starting batch read from table: table_50 
[TRACE] 2025-06-25 10:18:29.922 - [任务 24][local_pg - Copy] - Table table_50 is going to be initial synced 
[TRACE] 2025-06-25 10:18:30.018 - [任务 24][local_pg] - The table table_11 has already exist. 
[TRACE] 2025-06-25 10:18:30.023 - [任务 24][local_pg] - The table table_12 has already exist. 
[TRACE] 2025-06-25 10:18:30.093 - [任务 24][local_pg] - The table table_13 has already exist. 
[TRACE] 2025-06-25 10:18:30.093 - [任务 24][local_pg] - The table table_14 has already exist. 
[TRACE] 2025-06-25 10:18:30.178 - [任务 24][local_pg] - The table table_15 has already exist. 
[TRACE] 2025-06-25 10:18:30.178 - [任务 24][local_pg] - The table table_16 has already exist. 
[TRACE] 2025-06-25 10:18:30.252 - [任务 24][local_pg] - The table table_17 has already exist. 
[TRACE] 2025-06-25 10:18:30.252 - [任务 24][local_pg] - The table table_19 has already exist. 
[TRACE] 2025-06-25 10:18:30.322 - [任务 24][local_pg] - The table table_40 has already exist. 
[TRACE] 2025-06-25 10:18:30.322 - [任务 24][local_pg] - The table table_41 has already exist. 
[TRACE] 2025-06-25 10:18:30.350 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 10:18:30.350 - [任务 24][local_pg] - The table table_42 has already exist. 
[TRACE] 2025-06-25 10:18:30.404 - [任务 24][local_pg] - The table table_43 has already exist. 
[TRACE] 2025-06-25 10:18:30.404 - [任务 24][local_pg] - The table table_44 has already exist. 
[TRACE] 2025-06-25 10:18:30.459 - [任务 24][local_pg] - The table table_45 has already exist. 
[TRACE] 2025-06-25 10:18:30.459 - [任务 24][local_pg] - The table table_46 has already exist. 
[TRACE] 2025-06-25 10:18:30.515 - [任务 24][local_pg] - The table table_47 has already exist. 
[TRACE] 2025-06-25 10:18:30.515 - [任务 24][local_pg] - The table table_48 has already exist. 
[TRACE] 2025-06-25 10:18:30.574 - [任务 24][local_pg] - The table table_49 has already exist. 
[TRACE] 2025-06-25 10:18:30.574 - [任务 24][local_pg] - The table table_30 has already exist. 
[TRACE] 2025-06-25 10:18:30.632 - [任务 24][local_pg] - The table table_31 has already exist. 
[TRACE] 2025-06-25 10:18:30.632 - [任务 24][local_pg] - The table table_32 has already exist. 
[TRACE] 2025-06-25 10:18:30.691 - [任务 24][local_pg] - The table table_33 has already exist. 
[TRACE] 2025-06-25 10:18:30.691 - [任务 24][local_pg] - The table table_34 has already exist. 
[TRACE] 2025-06-25 10:18:30.751 - [任务 24][local_pg] - The table table_35 has already exist. 
[TRACE] 2025-06-25 10:18:30.751 - [任务 24][local_pg] - The table table_36 has already exist. 
[TRACE] 2025-06-25 10:18:30.824 - [任务 24][local_pg] - The table table_37 has already exist. 
[TRACE] 2025-06-25 10:18:30.824 - [任务 24][local_pg] - The table table_38 has already exist. 
[TRACE] 2025-06-25 10:18:30.880 - [任务 24][local_pg] - The table table_39 has already exist. 
[TRACE] 2025-06-25 10:18:30.881 - [任务 24][local_pg] - The table table_20 has already exist. 
[TRACE] 2025-06-25 10:18:30.942 - [任务 24][local_pg] - The table table_21 has already exist. 
[TRACE] 2025-06-25 10:18:30.942 - [任务 24][local_pg] - The table table_1 has already exist. 
[TRACE] 2025-06-25 10:18:31.004 - [任务 24][local_pg] - The table table_22 has already exist. 
[TRACE] 2025-06-25 10:18:31.004 - [任务 24][local_pg] - The table table_2 has already exist. 
[TRACE] 2025-06-25 10:18:31.062 - [任务 24][local_pg] - The table table_3 has already exist. 
[TRACE] 2025-06-25 10:18:31.062 - [任务 24][local_pg] - The table table_23 has already exist. 
[TRACE] 2025-06-25 10:18:31.122 - [任务 24][local_pg] - The table table_4 has already exist. 
[TRACE] 2025-06-25 10:18:31.122 - [任务 24][local_pg] - The table table_24 has already exist. 
[TRACE] 2025-06-25 10:18:31.179 - [任务 24][local_pg] - The table table_25 has already exist. 
[TRACE] 2025-06-25 10:18:31.179 - [任务 24][local_pg] - The table table_5 has already exist. 
[TRACE] 2025-06-25 10:18:31.235 - [任务 24][local_pg] - The table table_26 has already exist. 
[TRACE] 2025-06-25 10:18:31.235 - [任务 24][local_pg] - The table table_6 has already exist. 
[TRACE] 2025-06-25 10:18:31.292 - [任务 24][local_pg] - The table table_27 has already exist. 
[TRACE] 2025-06-25 10:18:31.293 - [任务 24][local_pg] - The table table_7 has already exist. 
[TRACE] 2025-06-25 10:18:31.349 - [任务 24][local_pg] - The table table_28 has already exist. 
[TRACE] 2025-06-25 10:18:31.349 - [任务 24][local_pg] - The table table_8 has already exist. 
[TRACE] 2025-06-25 10:18:31.405 - [任务 24][local_pg] - The table table_29 has already exist. 
[TRACE] 2025-06-25 10:18:31.405 - [任务 24][local_pg] - The table table_9 has already exist. 
[INFO ] 2025-06-25 10:18:32.375 - [任务 24][local_pg - Copy] - Table table_50 has been completed batch read 
[INFO ] 2025-06-25 10:18:32.375 - [任务 24][local_pg - Copy] - Starting batch read from table: table_10 
[TRACE] 2025-06-25 10:18:32.375 - [任务 24][local_pg - Copy] - Table table_10 is going to be initial synced 
[INFO ] 2025-06-25 10:18:32.769 - [任务 24][local_pg - Copy] - Table table_10 has been completed batch read 
[INFO ] 2025-06-25 10:18:32.769 - [任务 24][local_pg - Copy] - Starting batch read from table: table_11 
[TRACE] 2025-06-25 10:18:32.769 - [任务 24][local_pg - Copy] - Table table_11 is going to be initial synced 
[INFO ] 2025-06-25 10:18:33.410 - [任务 24][local_pg - Copy] - Table table_11 has been completed batch read 
[INFO ] 2025-06-25 10:18:33.410 - [任务 24][local_pg - Copy] - Starting batch read from table: table_12 
[TRACE] 2025-06-25 10:18:33.410 - [任务 24][local_pg - Copy] - Table table_12 is going to be initial synced 
[INFO ] 2025-06-25 10:18:34.134 - [任务 24][local_pg - Copy] - Table table_12 has been completed batch read 
[INFO ] 2025-06-25 10:18:34.135 - [任务 24][local_pg - Copy] - Starting batch read from table: table_13 
[TRACE] 2025-06-25 10:18:34.335 - [任务 24][local_pg - Copy] - Table table_13 is going to be initial synced 
[INFO ] 2025-06-25 10:18:34.635 - [任务 24][local_pg - Copy] - Table table_13 has been completed batch read 
[INFO ] 2025-06-25 10:18:34.635 - [任务 24][local_pg - Copy] - Starting batch read from table: table_14 
[TRACE] 2025-06-25 10:18:34.635 - [任务 24][local_pg - Copy] - Table table_14 is going to be initial synced 
[INFO ] 2025-06-25 10:18:35.556 - [任务 24][local_pg - Copy] - Table table_14 has been completed batch read 
[INFO ] 2025-06-25 10:18:35.556 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 10:18:35.556 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[INFO ] 2025-06-25 10:18:36.355 - [任务 24][local_pg - Copy] - Table table_15 has been completed batch read 
[INFO ] 2025-06-25 10:18:36.355 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 10:18:36.355 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[TRACE] 2025-06-25 10:18:36.379 - [任务 24][local_pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  
[ERROR] 2025-06-25 10:18:36.380 - [任务 24][local_pg] - Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  <-- Error Message -->
Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type: 

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectWriteType(PostgresExceptionCollector.java:66)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:165)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:503)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:848)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:808)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:701)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 98 more

[TRACE] 2025-06-25 10:18:36.380 - [任务 24][local_pg] - Job suspend in error handle 
[INFO ] 2025-06-25 10:18:37.194 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 10:18:37.369 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:18:37.369 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:18:37.369 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:18:37.369 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:18:37.369 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:18:37.373 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817909608 
[TRACE] 2025-06-25 10:18:37.373 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750817909608 
[TRACE] 2025-06-25 10:18:37.373 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:18:37.373 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:18:37.373 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 4 ms 
[TRACE] 2025-06-25 10:18:37.373 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:18:37.384 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817909621 
[TRACE] 2025-06-25 10:18:37.384 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750817909621 
[TRACE] 2025-06-25 10:18:37.384 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:18:37.384 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:18:37.384 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 10 ms 
[INFO ] 2025-06-25 10:18:42.267 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-25 10:18:47.100 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 10:18:47.100 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 10:18:48.104 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@7e502325 
[TRACE] 2025-06-25 10:18:48.104 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6b2ca239 
[TRACE] 2025-06-25 10:18:48.217 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 10:18:48.217 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 10:18:48.217 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 10:18:48.217 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 10:18:48.238 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:18:48.238 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:20:14.933 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:20:14.934 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:20:15.405 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:20:15.405 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:20:15.468 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:20:15.468 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:20:15.518 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:20:15.518 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:20:15.519 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:20:15.519 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:20:15.676 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:20:15.676 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:20:15.676 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:20:15.677 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:20:15.804 - [任务 24][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-25 10:20:15.804 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:20:15.804 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:20:15.805 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[INFO ] 2025-06-25 10:20:15.805 - [任务 24][local_pg - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-25 10:20:15.805 - [任务 24][local_pg - Copy] - Use existing batch read offset: {"table_10":{"batch_read_connector_status":"OVER"},"table_11":{"batch_read_connector_status":"OVER"},"table_12":{"batch_read_connector_status":"OVER"},"table_13":{"batch_read_connector_status":"OVER"},"table_14":{"batch_read_connector_status":"OVER"},"table_15":{"batch_read_connector_status":"OVER"},"table_50":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:20:43.731 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:21:53.665 - [任务 24][local_pg - Copy] - Initial sync started 
[TRACE] 2025-06-25 10:21:53.666 - [任务 24][local_pg - Copy] - Skip table [table_50] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:21:53.666 - [任务 24][local_pg - Copy] - Skip table [table_10] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:21:53.666 - [任务 24][local_pg - Copy] - Skip table [table_11] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:21:53.666 - [任务 24][local_pg - Copy] - Skip table [table_12] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:21:53.666 - [任务 24][local_pg - Copy] - Skip table [table_13] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:21:53.666 - [任务 24][local_pg - Copy] - Skip table [table_14] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:21:53.666 - [任务 24][local_pg - Copy] - Skip table [table_15] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2025-06-25 10:21:53.666 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 10:21:53.666 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[TRACE] 2025-06-25 10:21:53.708 - [任务 24][local_pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  
[ERROR] 2025-06-25 10:21:53.709 - [任务 24][local_pg] - Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  <-- Error Message -->
Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type: 

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectWriteType(PostgresExceptionCollector.java:66)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:165)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:503)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:848)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:808)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:701)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 98 more

[TRACE] 2025-06-25 10:21:53.709 - [任务 24][local_pg] - Job suspend in error handle 
[TRACE] 2025-06-25 10:21:54.177 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 10:21:54.686 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:21:54.686 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:21:54.686 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:21:54.686 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:21:54.688 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:21:54.689 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750818015544 
[TRACE] 2025-06-25 10:21:54.689 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750818015544 
[TRACE] 2025-06-25 10:21:54.689 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:21:54.689 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:21:54.689 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 4 ms 
[TRACE] 2025-06-25 10:21:54.689 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:21:54.693 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750818015540 
[TRACE] 2025-06-25 10:21:54.693 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750818015540 
[TRACE] 2025-06-25 10:21:54.693 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:21:54.693 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:21:54.694 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 4 ms 
[INFO ] 2025-06-25 10:21:58.655 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-25 10:22:03.663 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 10:22:03.868 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 10:22:04.666 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@272e1640 
[TRACE] 2025-06-25 10:22:04.668 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@171e9d98 
[TRACE] 2025-06-25 10:22:04.668 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 10:22:04.784 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 10:22:04.784 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 10:22:04.784 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 10:22:04.805 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:22:04.805 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:48:49.881 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:48:50.084 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:48:50.574 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:48:50.961 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:48:51.140 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:48:51.140 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:48:51.274 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:48:51.275 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:48:51.275 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:48:51.275 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:48:51.277 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:48:51.720 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:48:51.722 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:48:51.723 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[INFO ] 2025-06-25 10:48:51.730 - [任务 24][local_pg - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-25 10:48:51.730 - [任务 24][local_pg - Copy] - Use existing batch read offset: {"table_10":{"batch_read_connector_status":"OVER"},"table_11":{"batch_read_connector_status":"OVER"},"table_12":{"batch_read_connector_status":"OVER"},"table_13":{"batch_read_connector_status":"OVER"},"table_14":{"batch_read_connector_status":"OVER"},"table_15":{"batch_read_connector_status":"OVER"},"table_50":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:48:51.741 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:48:51.744 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:48:51.744 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:48:51.893 - [任务 24][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-25 10:48:51.893 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:48:59.639 - [任务 24][local_pg - Copy] - Initial sync started 
[TRACE] 2025-06-25 10:48:59.640 - [任务 24][local_pg - Copy] - Skip table [table_50] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:49:04.317 - [任务 24][local_pg - Copy] - Skip table [table_10] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:49:07.467 - [任务 24][local_pg - Copy] - Skip table [table_11] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:49:10.114 - [任务 24][local_pg - Copy] - Skip table [table_12] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:49:10.554 - [任务 24][local_pg - Copy] - Skip table [table_13] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:49:11.084 - [任务 24][local_pg - Copy] - Skip table [table_14] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:49:13.271 - [任务 24][local_pg - Copy] - Skip table [table_15] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2025-06-25 10:49:13.272 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 10:49:13.275 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[TRACE] 2025-06-25 10:49:13.679 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[INFO ] 2025-06-25 10:49:14.610 - [任务 24][local_pg - Copy] - Table table_16 has been completed batch read 
[INFO ] 2025-06-25 10:49:14.610 - [任务 24][local_pg - Copy] - Starting batch read from table: table_17 
[TRACE] 2025-06-25 10:49:14.610 - [任务 24][local_pg - Copy] - Table table_17 is going to be initial synced 
[INFO ] 2025-06-25 10:49:15.666 - [任务 24][local_pg - Copy] - Table table_17 has been completed batch read 
[INFO ] 2025-06-25 10:49:15.667 - [任务 24][local_pg - Copy] - Starting batch read from table: table_19 
[TRACE] 2025-06-25 10:49:15.667 - [任务 24][local_pg - Copy] - Table table_19 is going to be initial synced 
[INFO ] 2025-06-25 10:49:16.729 - [任务 24][local_pg - Copy] - Table table_19 has been completed batch read 
[INFO ] 2025-06-25 10:49:16.729 - [任务 24][local_pg - Copy] - Starting batch read from table: table_40 
[TRACE] 2025-06-25 10:49:16.729 - [任务 24][local_pg - Copy] - Table table_40 is going to be initial synced 
[INFO ] 2025-06-25 10:49:17.504 - [任务 24][local_pg - Copy] - Table table_40 has been completed batch read 
[INFO ] 2025-06-25 10:49:17.504 - [任务 24][local_pg - Copy] - Starting batch read from table: table_41 
[TRACE] 2025-06-25 10:49:17.504 - [任务 24][local_pg - Copy] - Table table_41 is going to be initial synced 
[INFO ] 2025-06-25 10:49:18.291 - [任务 24][local_pg - Copy] - Table table_41 has been completed batch read 
[INFO ] 2025-06-25 10:49:18.291 - [任务 24][local_pg - Copy] - Starting batch read from table: table_42 
[TRACE] 2025-06-25 10:49:18.291 - [任务 24][local_pg - Copy] - Table table_42 is going to be initial synced 
[INFO ] 2025-06-25 10:49:18.813 - [任务 24][local_pg - Copy] - Table table_42 has been completed batch read 
[INFO ] 2025-06-25 10:49:18.816 - [任务 24][local_pg - Copy] - Starting batch read from table: table_43 
[TRACE] 2025-06-25 10:49:18.816 - [任务 24][local_pg - Copy] - Table table_43 is going to be initial synced 
[INFO ] 2025-06-25 10:49:19.377 - [任务 24][local_pg - Copy] - Table table_43 has been completed batch read 
[INFO ] 2025-06-25 10:49:19.377 - [任务 24][local_pg - Copy] - Starting batch read from table: table_44 
[TRACE] 2025-06-25 10:49:19.377 - [任务 24][local_pg - Copy] - Table table_44 is going to be initial synced 
[INFO ] 2025-06-25 10:49:20.330 - [任务 24][local_pg - Copy] - Table table_44 has been completed batch read 
[INFO ] 2025-06-25 10:49:20.330 - [任务 24][local_pg - Copy] - Starting batch read from table: table_45 
[TRACE] 2025-06-25 10:49:20.330 - [任务 24][local_pg - Copy] - Table table_45 is going to be initial synced 
[INFO ] 2025-06-25 10:49:21.092 - [任务 24][local_pg - Copy] - Table table_45 has been completed batch read 
[INFO ] 2025-06-25 10:49:21.092 - [任务 24][local_pg - Copy] - Starting batch read from table: table_46 
[TRACE] 2025-06-25 10:49:21.092 - [任务 24][local_pg - Copy] - Table table_46 is going to be initial synced 
[INFO ] 2025-06-25 10:49:22.169 - [任务 24][local_pg - Copy] - Table table_46 has been completed batch read 
[INFO ] 2025-06-25 10:49:22.169 - [任务 24][local_pg - Copy] - Starting batch read from table: table_47 
[TRACE] 2025-06-25 10:49:22.169 - [任务 24][local_pg - Copy] - Table table_47 is going to be initial synced 
[INFO ] 2025-06-25 10:49:22.788 - [任务 24][local_pg - Copy] - Table table_47 has been completed batch read 
[INFO ] 2025-06-25 10:49:22.789 - [任务 24][local_pg - Copy] - Starting batch read from table: table_48 
[TRACE] 2025-06-25 10:49:22.789 - [任务 24][local_pg - Copy] - Table table_48 is going to be initial synced 
[INFO ] 2025-06-25 10:49:23.667 - [任务 24][local_pg - Copy] - Table table_48 has been completed batch read 
[INFO ] 2025-06-25 10:49:23.667 - [任务 24][local_pg - Copy] - Starting batch read from table: table_49 
[TRACE] 2025-06-25 10:49:23.667 - [任务 24][local_pg - Copy] - Table table_49 is going to be initial synced 
[INFO ] 2025-06-25 10:49:24.603 - [任务 24][local_pg - Copy] - Table table_49 has been completed batch read 
[INFO ] 2025-06-25 10:49:24.603 - [任务 24][local_pg - Copy] - Starting batch read from table: table_30 
[TRACE] 2025-06-25 10:49:24.603 - [任务 24][local_pg - Copy] - Table table_30 is going to be initial synced 
[INFO ] 2025-06-25 10:49:25.592 - [任务 24][local_pg - Copy] - Table table_30 has been completed batch read 
[INFO ] 2025-06-25 10:49:25.599 - [任务 24][local_pg - Copy] - Starting batch read from table: table_31 
[TRACE] 2025-06-25 10:49:25.599 - [任务 24][local_pg - Copy] - Table table_31 is going to be initial synced 
[INFO ] 2025-06-25 10:49:26.091 - [任务 24][local_pg - Copy] - Table table_31 has been completed batch read 
[INFO ] 2025-06-25 10:49:26.091 - [任务 24][local_pg - Copy] - Starting batch read from table: table_32 
[TRACE] 2025-06-25 10:49:26.091 - [任务 24][local_pg - Copy] - Table table_32 is going to be initial synced 
[INFO ] 2025-06-25 10:49:26.885 - [任务 24][local_pg - Copy] - Table table_32 has been completed batch read 
[INFO ] 2025-06-25 10:49:26.886 - [任务 24][local_pg - Copy] - Starting batch read from table: table_33 
[TRACE] 2025-06-25 10:49:26.886 - [任务 24][local_pg - Copy] - Table table_33 is going to be initial synced 
[INFO ] 2025-06-25 10:49:27.462 - [任务 24][local_pg - Copy] - Table table_33 has been completed batch read 
[INFO ] 2025-06-25 10:49:27.462 - [任务 24][local_pg - Copy] - Starting batch read from table: table_34 
[TRACE] 2025-06-25 10:49:27.465 - [任务 24][local_pg - Copy] - Table table_34 is going to be initial synced 
[INFO ] 2025-06-25 10:49:28.149 - [任务 24][local_pg - Copy] - Table table_34 has been completed batch read 
[INFO ] 2025-06-25 10:49:28.149 - [任务 24][local_pg - Copy] - Starting batch read from table: table_35 
[TRACE] 2025-06-25 10:49:28.149 - [任务 24][local_pg - Copy] - Table table_35 is going to be initial synced 
[INFO ] 2025-06-25 10:49:28.873 - [任务 24][local_pg - Copy] - Table table_35 has been completed batch read 
[INFO ] 2025-06-25 10:49:28.873 - [任务 24][local_pg - Copy] - Starting batch read from table: table_36 
[TRACE] 2025-06-25 10:49:28.873 - [任务 24][local_pg - Copy] - Table table_36 is going to be initial synced 
[INFO ] 2025-06-25 10:49:29.731 - [任务 24][local_pg - Copy] - Table table_36 has been completed batch read 
[INFO ] 2025-06-25 10:49:29.732 - [任务 24][local_pg - Copy] - Starting batch read from table: table_37 
[TRACE] 2025-06-25 10:49:29.732 - [任务 24][local_pg - Copy] - Table table_37 is going to be initial synced 
[INFO ] 2025-06-25 10:49:30.343 - [任务 24][local_pg - Copy] - Table table_37 has been completed batch read 
[INFO ] 2025-06-25 10:49:30.343 - [任务 24][local_pg - Copy] - Starting batch read from table: table_38 
[TRACE] 2025-06-25 10:49:30.343 - [任务 24][local_pg - Copy] - Table table_38 is going to be initial synced 
[INFO ] 2025-06-25 10:49:31.078 - [任务 24][local_pg - Copy] - Table table_38 has been completed batch read 
[INFO ] 2025-06-25 10:49:31.079 - [任务 24][local_pg - Copy] - Starting batch read from table: table_39 
[TRACE] 2025-06-25 10:49:31.079 - [任务 24][local_pg - Copy] - Table table_39 is going to be initial synced 
[INFO ] 2025-06-25 10:49:31.504 - [任务 24][local_pg - Copy] - Table table_39 has been completed batch read 
[INFO ] 2025-06-25 10:49:31.504 - [任务 24][local_pg - Copy] - Starting batch read from table: table_20 
[TRACE] 2025-06-25 10:49:31.504 - [任务 24][local_pg - Copy] - Table table_20 is going to be initial synced 
[INFO ] 2025-06-25 10:49:32.476 - [任务 24][local_pg - Copy] - Table table_20 has been completed batch read 
[INFO ] 2025-06-25 10:49:32.476 - [任务 24][local_pg - Copy] - Starting batch read from table: table_21 
[TRACE] 2025-06-25 10:49:32.477 - [任务 24][local_pg - Copy] - Table table_21 is going to be initial synced 
[INFO ] 2025-06-25 10:49:32.949 - [任务 24][local_pg - Copy] - Table table_21 has been completed batch read 
[INFO ] 2025-06-25 10:49:32.949 - [任务 24][local_pg - Copy] - Starting batch read from table: table_1 
[TRACE] 2025-06-25 10:49:32.949 - [任务 24][local_pg - Copy] - Table table_1 is going to be initial synced 
[INFO ] 2025-06-25 10:49:33.846 - [任务 24][local_pg - Copy] - Table table_1 has been completed batch read 
[INFO ] 2025-06-25 10:49:33.848 - [任务 24][local_pg - Copy] - Starting batch read from table: table_22 
[TRACE] 2025-06-25 10:49:33.848 - [任务 24][local_pg - Copy] - Table table_22 is going to be initial synced 
[INFO ] 2025-06-25 10:49:34.492 - [任务 24][local_pg - Copy] - Table table_22 has been completed batch read 
[INFO ] 2025-06-25 10:49:34.492 - [任务 24][local_pg - Copy] - Starting batch read from table: table_2 
[TRACE] 2025-06-25 10:49:34.492 - [任务 24][local_pg - Copy] - Table table_2 is going to be initial synced 
[INFO ] 2025-06-25 10:49:35.408 - [任务 24][local_pg - Copy] - Table table_2 has been completed batch read 
[INFO ] 2025-06-25 10:49:35.408 - [任务 24][local_pg - Copy] - Starting batch read from table: table_3 
[TRACE] 2025-06-25 10:49:35.408 - [任务 24][local_pg - Copy] - Table table_3 is going to be initial synced 
[INFO ] 2025-06-25 10:49:36.427 - [任务 24][local_pg - Copy] - Table table_3 has been completed batch read 
[INFO ] 2025-06-25 10:49:36.427 - [任务 24][local_pg - Copy] - Starting batch read from table: table_23 
[TRACE] 2025-06-25 10:49:36.427 - [任务 24][local_pg - Copy] - Table table_23 is going to be initial synced 
[INFO ] 2025-06-25 10:49:37.298 - [任务 24][local_pg - Copy] - Table table_23 has been completed batch read 
[INFO ] 2025-06-25 10:49:37.299 - [任务 24][local_pg - Copy] - Starting batch read from table: table_4 
[TRACE] 2025-06-25 10:49:37.299 - [任务 24][local_pg - Copy] - Table table_4 is going to be initial synced 
[INFO ] 2025-06-25 10:49:37.817 - [任务 24][local_pg - Copy] - Table table_4 has been completed batch read 
[INFO ] 2025-06-25 10:49:37.817 - [任务 24][local_pg - Copy] - Starting batch read from table: table_24 
[TRACE] 2025-06-25 10:49:37.817 - [任务 24][local_pg - Copy] - Table table_24 is going to be initial synced 
[INFO ] 2025-06-25 10:49:38.796 - [任务 24][local_pg - Copy] - Table table_24 has been completed batch read 
[INFO ] 2025-06-25 10:49:38.797 - [任务 24][local_pg - Copy] - Starting batch read from table: table_25 
[TRACE] 2025-06-25 10:49:38.797 - [任务 24][local_pg - Copy] - Table table_25 is going to be initial synced 
[INFO ] 2025-06-25 10:49:39.833 - [任务 24][local_pg - Copy] - Table table_25 has been completed batch read 
[INFO ] 2025-06-25 10:49:39.833 - [任务 24][local_pg - Copy] - Starting batch read from table: table_5 
[TRACE] 2025-06-25 10:49:39.833 - [任务 24][local_pg - Copy] - Table table_5 is going to be initial synced 
[INFO ] 2025-06-25 10:49:40.841 - [任务 24][local_pg - Copy] - Table table_5 has been completed batch read 
[INFO ] 2025-06-25 10:49:40.841 - [任务 24][local_pg - Copy] - Starting batch read from table: table_26 
[TRACE] 2025-06-25 10:49:40.841 - [任务 24][local_pg - Copy] - Table table_26 is going to be initial synced 
[INFO ] 2025-06-25 10:49:42.005 - [任务 24][local_pg - Copy] - Table table_26 has been completed batch read 
[INFO ] 2025-06-25 10:49:42.006 - [任务 24][local_pg - Copy] - Starting batch read from table: table_6 
[TRACE] 2025-06-25 10:49:42.006 - [任务 24][local_pg - Copy] - Table table_6 is going to be initial synced 
[INFO ] 2025-06-25 10:49:43.081 - [任务 24][local_pg - Copy] - Table table_6 has been completed batch read 
[INFO ] 2025-06-25 10:49:43.081 - [任务 24][local_pg - Copy] - Starting batch read from table: table_27 
[TRACE] 2025-06-25 10:49:43.081 - [任务 24][local_pg - Copy] - Table table_27 is going to be initial synced 
[INFO ] 2025-06-25 10:49:43.499 - [任务 24][local_pg - Copy] - Table table_27 has been completed batch read 
[INFO ] 2025-06-25 10:49:43.500 - [任务 24][local_pg - Copy] - Starting batch read from table: table_7 
[TRACE] 2025-06-25 10:49:43.500 - [任务 24][local_pg - Copy] - Table table_7 is going to be initial synced 
[INFO ] 2025-06-25 10:49:44.126 - [任务 24][local_pg - Copy] - Table table_7 has been completed batch read 
[INFO ] 2025-06-25 10:49:44.126 - [任务 24][local_pg - Copy] - Starting batch read from table: table_28 
[TRACE] 2025-06-25 10:49:44.126 - [任务 24][local_pg - Copy] - Table table_28 is going to be initial synced 
[INFO ] 2025-06-25 10:49:44.581 - [任务 24][local_pg - Copy] - Table table_28 has been completed batch read 
[INFO ] 2025-06-25 10:49:44.581 - [任务 24][local_pg - Copy] - Starting batch read from table: table_8 
[TRACE] 2025-06-25 10:49:44.582 - [任务 24][local_pg - Copy] - Table table_8 is going to be initial synced 
[INFO ] 2025-06-25 10:49:45.510 - [任务 24][local_pg - Copy] - Table table_8 has been completed batch read 
[INFO ] 2025-06-25 10:49:45.510 - [任务 24][local_pg - Copy] - Starting batch read from table: table_29 
[TRACE] 2025-06-25 10:49:45.510 - [任务 24][local_pg - Copy] - Table table_29 is going to be initial synced 
[INFO ] 2025-06-25 10:49:45.914 - [任务 24][local_pg - Copy] - Table table_29 has been completed batch read 
[INFO ] 2025-06-25 10:49:45.914 - [任务 24][local_pg - Copy] - Starting batch read from table: table_9 
[TRACE] 2025-06-25 10:49:45.914 - [任务 24][local_pg - Copy] - Table table_9 is going to be initial synced 
[INFO ] 2025-06-25 10:49:46.458 - [任务 24][local_pg - Copy] - Table table_9 has been completed batch read 
[TRACE] 2025-06-25 10:49:46.458 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:49:46.458 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:49:46.459 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:49:46.461 - [任务 24][local_pg - Copy] - Initial sync completed 
[TRACE] 2025-06-25 10:49:46.461 - [任务 24][local_pg - Copy] - Starting stream read, table list: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:49:46.461 - [任务 24][local_pg - Copy] - Starting incremental sync using database log parser 
[WARN ] 2025-06-25 10:49:46.464 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 10:49:46.641 - [任务 24][local_pg - Copy] - Using an existing logical replication slot, slotName:tapdata_cdc_4bae6a8b_d8b0_4a04_b887_5cc0c0a605f7 
[TRACE] 2025-06-25 10:49:46.641 - [任务 24][local_pg - Copy] - Connector PostgreSQL incremental start succeed, tables: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], data change syncing 
[TRACE] 2025-06-25 10:49:46.972 - [任务 24][local_pg] - Process after table "table_17" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.972 - [任务 24][local_pg] - Process after table "table_16" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.972 - [任务 24][local_pg] - Process after table "table_40" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.972 - [任务 24][local_pg] - Process after table "table_19" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.973 - [任务 24][local_pg] - Process after table "table_41" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.973 - [任务 24][local_pg] - Process after table "table_44" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.973 - [任务 24][local_pg] - Process after table "table_42" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.973 - [任务 24][local_pg] - Process after table "table_45" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.973 - [任务 24][local_pg] - Process after table "table_46" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.973 - [任务 24][local_pg] - Process after table "table_43" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_47" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_48" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_49" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_32" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_31" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_33" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_30" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_36" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_34" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.974 - [任务 24][local_pg] - Process after table "table_35" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.975 - [任务 24][local_pg] - Process after table "table_38" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.975 - [任务 24][local_pg] - Process after table "table_37" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.975 - [任务 24][local_pg] - Process after table "table_20" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.975 - [任务 24][local_pg] - Process after table "table_39" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.975 - [任务 24][local_pg] - Process after table "table_21" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.975 - [任务 24][local_pg] - Process after table "table_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.975 - [任务 24][local_pg] - Process after table "table_1" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.976 - [任务 24][local_pg] - Process after table "table_3" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.976 - [任务 24][local_pg] - Process after table "table_22" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.976 - [任务 24][local_pg] - Process after table "table_23" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.976 - [任务 24][local_pg] - Process after table "table_4" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.976 - [任务 24][local_pg] - Process after table "table_24" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.976 - [任务 24][local_pg] - Process after table "table_25" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.976 - [任务 24][local_pg] - Process after table "table_5" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.976 - [任务 24][local_pg] - Process after table "table_26" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.977 - [任务 24][local_pg] - Process after table "table_27" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.977 - [任务 24][local_pg] - Process after table "table_7" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.977 - [任务 24][local_pg] - Process after table "table_6" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.978 - [任务 24][local_pg] - Process after table "table_8" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.978 - [任务 24][local_pg] - Process after table "table_29" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.978 - [任务 24][local_pg] - Process after table "table_28" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.978 - [任务 24][local_pg] - Process after table "table_9" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 10:49:46.982 - [任务 24][local_pg] - Process after table "table_50" initial sync finished, cost: 8 ms 
[TRACE] 2025-06-25 10:49:46.982 - [任务 24][local_pg] - Process after table "table_12" initial sync finished, cost: 11 ms 
[TRACE] 2025-06-25 10:49:46.986 - [任务 24][local_pg] - Process after table "table_13" initial sync finished, cost: 12 ms 
[TRACE] 2025-06-25 10:49:46.986 - [任务 24][local_pg] - Process after table "table_15" initial sync finished, cost: 14 ms 
[TRACE] 2025-06-25 10:49:46.996 - [任务 24][local_pg] - Process after table "table_14" initial sync finished, cost: 24 ms 
[TRACE] 2025-06-25 10:49:46.996 - [任务 24][local_pg] - Process after table "table_10" initial sync finished, cost: 24 ms 
[TRACE] 2025-06-25 10:49:46.996 - [任务 24][local_pg] - Process after table "table_11" initial sync finished, cost: 24 ms 
[INFO ] 2025-06-25 10:49:47.197 - [任务 24][local_pg] - Process after all table(s) initial sync are finished，table number: 49 
[TRACE] 2025-06-25 10:50:20.944 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:50:21.377 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:50:21.377 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750819731301 
[TRACE] 2025-06-25 10:50:21.378 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750819731301 
[TRACE] 2025-06-25 10:50:21.378 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:50:21.379 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:50:21.379 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 436 ms 
[TRACE] 2025-06-25 10:50:21.379 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:50:21.390 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750819731305 
[TRACE] 2025-06-25 10:50:21.390 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750819731305 
[TRACE] 2025-06-25 10:50:21.390 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:50:21.390 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:50:21.594 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 10 ms 
[TRACE] 2025-06-25 10:50:30.180 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 10:50:31.183 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@12dea440 
[TRACE] 2025-06-25 10:50:31.185 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1eb1a368 
[TRACE] 2025-06-25 10:50:31.185 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 10:50:31.304 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 10:50:31.304 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 10:50:31.345 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 10:50:31.349 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:50:31.349 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:56:32.649 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:56:32.852 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:56:33.360 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:56:33.361 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:56:33.542 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:56:33.542 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:56:33.647 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:56:33.648 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:56:33.648 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:56:33.648 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:56:33.853 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:56:34.034 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:56:34.035 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:56:34.049 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:56:34.049 - [任务 24][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-25 10:56:34.134 - [任务 24][local_pg] - The table table_50 has already exist. 
[TRACE] 2025-06-25 10:56:34.135 - [任务 24][local_pg] - The table table_10 has already exist. 
[INFO ] 2025-06-25 10:56:34.151 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:56:34.151 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:56:34.151 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[TRACE] 2025-06-25 10:56:34.151 - [任务 24][local_pg - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-25 10:56:34.180 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[TRACE] 2025-06-25 10:56:34.184 - [任务 24][local_pg] - The table table_11 has already exist. 
[INFO ] 2025-06-25 10:56:34.185 - [任务 24][local_pg - Copy] - new logical replication slot created, slotName:tapdata_cdc_ab0483bd_a43e_434f_b5a8_b3218d4c1d65 
[INFO ] 2025-06-25 10:56:34.225 - [任务 24][local_pg - Copy] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-06-25 10:56:34.226 - [任务 24][local_pg] - The table table_12 has already exist. 
[TRACE] 2025-06-25 10:56:34.289 - [任务 24][local_pg] - The table table_13 has already exist. 
[TRACE] 2025-06-25 10:56:34.289 - [任务 24][local_pg] - The table table_14 has already exist. 
[TRACE] 2025-06-25 10:56:34.351 - [任务 24][local_pg] - The table table_15 has already exist. 
[TRACE] 2025-06-25 10:56:34.351 - [任务 24][local_pg] - The table table_16 has already exist. 
[INFO ] 2025-06-25 10:56:34.369 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:56:34.369 - [任务 24][local_pg - Copy] - Initial sync started 
[INFO ] 2025-06-25 10:56:34.370 - [任务 24][local_pg - Copy] - Starting batch read from table: table_50 
[TRACE] 2025-06-25 10:56:34.372 - [任务 24][local_pg - Copy] - Table table_50 is going to be initial synced 
[TRACE] 2025-06-25 10:56:34.432 - [任务 24][local_pg] - The table table_17 has already exist. 
[TRACE] 2025-06-25 10:56:34.432 - [任务 24][local_pg] - The table table_19 has already exist. 
[TRACE] 2025-06-25 10:56:34.514 - [任务 24][local_pg] - The table table_40 has already exist. 
[TRACE] 2025-06-25 10:56:34.514 - [任务 24][local_pg] - The table table_41 has already exist. 
[TRACE] 2025-06-25 10:56:34.581 - [任务 24][local_pg] - The table table_42 has already exist. 
[TRACE] 2025-06-25 10:56:34.581 - [任务 24][local_pg] - The table table_43 has already exist. 
[TRACE] 2025-06-25 10:56:34.649 - [任务 24][local_pg] - The table table_44 has already exist. 
[TRACE] 2025-06-25 10:56:34.649 - [任务 24][local_pg] - The table table_45 has already exist. 
[TRACE] 2025-06-25 10:56:34.774 - [任务 24][local_pg] - The table table_46 has already exist. 
[TRACE] 2025-06-25 10:56:34.774 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[TRACE] 2025-06-25 10:56:34.774 - [任务 24][local_pg] - The table table_47 has already exist. 
[TRACE] 2025-06-25 10:56:34.834 - [任务 24][local_pg] - The table table_48 has already exist. 
[TRACE] 2025-06-25 10:56:34.834 - [任务 24][local_pg] - The table table_49 has already exist. 
[TRACE] 2025-06-25 10:56:34.891 - [任务 24][local_pg] - The table table_30 has already exist. 
[TRACE] 2025-06-25 10:56:34.891 - [任务 24][local_pg] - The table table_31 has already exist. 
[TRACE] 2025-06-25 10:56:34.953 - [任务 24][local_pg] - The table table_32 has already exist. 
[TRACE] 2025-06-25 10:56:34.953 - [任务 24][local_pg] - The table table_33 has already exist. 
[TRACE] 2025-06-25 10:56:35.012 - [任务 24][local_pg] - The table table_34 has already exist. 
[TRACE] 2025-06-25 10:56:35.012 - [任务 24][local_pg] - The table table_35 has already exist. 
[TRACE] 2025-06-25 10:56:35.073 - [任务 24][local_pg] - The table table_36 has already exist. 
[TRACE] 2025-06-25 10:56:35.073 - [任务 24][local_pg] - The table table_37 has already exist. 
[TRACE] 2025-06-25 10:56:35.131 - [任务 24][local_pg] - The table table_38 has already exist. 
[TRACE] 2025-06-25 10:56:35.132 - [任务 24][local_pg] - The table table_39 has already exist. 
[TRACE] 2025-06-25 10:56:35.196 - [任务 24][local_pg] - The table table_20 has already exist. 
[TRACE] 2025-06-25 10:56:35.196 - [任务 24][local_pg] - The table table_21 has already exist. 
[TRACE] 2025-06-25 10:56:35.260 - [任务 24][local_pg] - The table table_1 has already exist. 
[TRACE] 2025-06-25 10:56:35.261 - [任务 24][local_pg] - The table table_22 has already exist. 
[TRACE] 2025-06-25 10:56:35.410 - [任务 24][local_pg] - The table table_2 has already exist. 
[TRACE] 2025-06-25 10:56:35.411 - [任务 24][local_pg] - The table table_3 has already exist. 
[TRACE] 2025-06-25 10:56:35.471 - [任务 24][local_pg] - The table table_23 has already exist. 
[TRACE] 2025-06-25 10:56:35.471 - [任务 24][local_pg] - The table table_4 has already exist. 
[TRACE] 2025-06-25 10:56:35.530 - [任务 24][local_pg] - The table table_24 has already exist. 
[TRACE] 2025-06-25 10:56:35.530 - [任务 24][local_pg] - The table table_25 has already exist. 
[TRACE] 2025-06-25 10:56:35.586 - [任务 24][local_pg] - The table table_5 has already exist. 
[TRACE] 2025-06-25 10:56:35.586 - [任务 24][local_pg] - The table table_26 has already exist. 
[TRACE] 2025-06-25 10:56:35.645 - [任务 24][local_pg] - The table table_6 has already exist. 
[TRACE] 2025-06-25 10:56:35.645 - [任务 24][local_pg] - The table table_27 has already exist. 
[TRACE] 2025-06-25 10:56:35.705 - [任务 24][local_pg] - The table table_7 has already exist. 
[TRACE] 2025-06-25 10:56:35.705 - [任务 24][local_pg] - The table table_28 has already exist. 
[TRACE] 2025-06-25 10:56:35.763 - [任务 24][local_pg] - The table table_8 has already exist. 
[TRACE] 2025-06-25 10:56:35.764 - [任务 24][local_pg] - The table table_29 has already exist. 
[TRACE] 2025-06-25 10:56:35.965 - [任务 24][local_pg] - The table table_9 has already exist. 
[INFO ] 2025-06-25 10:56:36.903 - [任务 24][local_pg - Copy] - Table table_50 has been completed batch read 
[INFO ] 2025-06-25 10:56:36.907 - [任务 24][local_pg - Copy] - Starting batch read from table: table_10 
[TRACE] 2025-06-25 10:56:36.907 - [任务 24][local_pg - Copy] - Table table_10 is going to be initial synced 
[INFO ] 2025-06-25 10:56:37.330 - [任务 24][local_pg - Copy] - Table table_10 has been completed batch read 
[INFO ] 2025-06-25 10:56:37.330 - [任务 24][local_pg - Copy] - Starting batch read from table: table_11 
[TRACE] 2025-06-25 10:56:37.531 - [任务 24][local_pg - Copy] - Table table_11 is going to be initial synced 
[INFO ] 2025-06-25 10:56:38.037 - [任务 24][local_pg - Copy] - Table table_11 has been completed batch read 
[INFO ] 2025-06-25 10:56:38.037 - [任务 24][local_pg - Copy] - Starting batch read from table: table_12 
[TRACE] 2025-06-25 10:56:38.240 - [任务 24][local_pg - Copy] - Table table_12 is going to be initial synced 
[INFO ] 2025-06-25 10:56:38.821 - [任务 24][local_pg - Copy] - Table table_12 has been completed batch read 
[INFO ] 2025-06-25 10:56:38.822 - [任务 24][local_pg - Copy] - Starting batch read from table: table_13 
[TRACE] 2025-06-25 10:56:38.822 - [任务 24][local_pg - Copy] - Table table_13 is going to be initial synced 
[INFO ] 2025-06-25 10:56:39.310 - [任务 24][local_pg - Copy] - Table table_13 has been completed batch read 
[INFO ] 2025-06-25 10:56:39.311 - [任务 24][local_pg - Copy] - Starting batch read from table: table_14 
[TRACE] 2025-06-25 10:56:39.311 - [任务 24][local_pg - Copy] - Table table_14 is going to be initial synced 
[INFO ] 2025-06-25 10:56:40.157 - [任务 24][local_pg - Copy] - Table table_14 has been completed batch read 
[INFO ] 2025-06-25 10:56:40.157 - [任务 24][local_pg - Copy] - Starting batch read from table: table_15 
[TRACE] 2025-06-25 10:56:40.157 - [任务 24][local_pg - Copy] - Table table_15 is going to be initial synced 
[INFO ] 2025-06-25 10:56:40.974 - [任务 24][local_pg - Copy] - Table table_15 has been completed batch read 
[INFO ] 2025-06-25 10:56:40.974 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 10:56:40.974 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[TRACE] 2025-06-25 10:56:41.000 - [任务 24][local_pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  
[ERROR] 2025-06-25 10:56:41.012 - [任务 24][local_pg] - Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type:  <-- Error Message -->
Target type in postgres does not match the incoming data when write record.
 - Table name: table_16
 - Target field: field_text1, type: integer
 - Data to be written: null
 - Java type: 

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectWriteType(PostgresExceptionCollector.java:66)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:165)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:503)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:848)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:808)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:701)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: org.postgresql.util.PSQLException: ERROR: column "field_text1" is of type integer but expression is of type bytea
  建议：You will need to rewrite or cast the expression.
  位置：204
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 98 more

[TRACE] 2025-06-25 10:56:41.012 - [任务 24][local_pg] - Job suspend in error handle 
[TRACE] 2025-06-25 10:56:41.992 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 10:56:41.992 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 10:56:41.992 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 10:56:41.992 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 10:56:41.992 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 10:56:41.994 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750820193906 
[TRACE] 2025-06-25 10:56:41.995 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750820193906 
[TRACE] 2025-06-25 10:56:41.995 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 10:56:41.995 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 10:56:41.997 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 8 ms 
[TRACE] 2025-06-25 10:56:41.997 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 10:56:42.012 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750820193892 
[TRACE] 2025-06-25 10:56:42.013 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750820193892 
[TRACE] 2025-06-25 10:56:42.013 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 10:56:42.013 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 10:56:42.013 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 16 ms 
[INFO ] 2025-06-25 10:56:48.376 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-25 10:56:49.522 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-25 10:56:49.724 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 10:56:50.842 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@2c3ca19c 
[TRACE] 2025-06-25 10:56:50.842 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@107396b2 
[TRACE] 2025-06-25 10:56:51.509 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 10:56:52.068 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 10:56:52.069 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 10:56:52.069 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 10:56:52.093 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:56:52.093 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 10:59:13.204 - [任务 24] - Task initialization... 
[TRACE] 2025-06-25 10:59:13.204 - [任务 24] - Start task milestones: 685b55793439e7780d3c42fd(任务 24) 
[INFO ] 2025-06-25 10:59:13.568 - [任务 24] - Loading table structure completed 
[TRACE] 2025-06-25 10:59:13.628 - [任务 24] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-25 10:59:13.693 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-25 10:59:13.693 - [任务 24] - Task started 
[TRACE] 2025-06-25 10:59:13.736 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:59:13.736 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] start preload schema,table counts: 49 
[TRACE] 2025-06-25 10:59:13.736 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-25 10:59:13.736 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] preload schema finished, cost 0 ms 
[INFO ] 2025-06-25 10:59:13.893 - [任务 24][local_pg - Copy] - Enable partition table support for source database 
[INFO ] 2025-06-25 10:59:13.893 - [任务 24][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-25 10:59:13.893 - [任务 24][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-25 10:59:13.893 - [任务 24][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-25 10:59:13.905 - [任务 24][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-25 10:59:14.012 - [任务 24][local_pg - Copy] - Source connector(local_pg - Copy) initialization completed 
[TRACE] 2025-06-25 10:59:14.012 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" read batch size: 100 
[TRACE] 2025-06-25 10:59:14.012 - [任务 24][local_pg - Copy] - Source node "local_pg - Copy" event queue capacity: 200 
[INFO ] 2025-06-25 10:59:14.013 - [任务 24][local_pg - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-25 10:59:14.164 - [任务 24][local_pg - Copy] - Use existing batch read offset: {"table_10":{"batch_read_connector_status":"OVER"},"table_11":{"batch_read_connector_status":"OVER"},"table_12":{"batch_read_connector_status":"OVER"},"table_13":{"batch_read_connector_status":"OVER"},"table_14":{"batch_read_connector_status":"OVER"},"table_15":{"batch_read_connector_status":"OVER"},"table_50":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 10:59:14.164 - [任务 24][local_pg - Copy] - Starting batch read from 49 tables 
[TRACE] 2025-06-25 10:59:14.169 - [任务 24][local_pg - Copy] - Initial sync started 
[TRACE] 2025-06-25 10:59:14.170 - [任务 24][local_pg - Copy] - Skip table [table_50] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:59:17.690 - [任务 24][local_pg - Copy] - Skip table [table_10] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:59:18.120 - [任务 24][local_pg - Copy] - Skip table [table_11] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:59:18.444 - [任务 24][local_pg - Copy] - Skip table [table_12] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:59:18.813 - [任务 24][local_pg - Copy] - Skip table [table_13] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:59:19.157 - [任务 24][local_pg - Copy] - Skip table [table_14] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-25 10:59:19.437 - [任务 24][local_pg - Copy] - Skip table [table_15] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2025-06-25 10:59:19.440 - [任务 24][local_pg - Copy] - Starting batch read from table: table_16 
[TRACE] 2025-06-25 10:59:19.440 - [任务 24][local_pg - Copy] - Table table_16 is going to be initial synced 
[TRACE] 2025-06-25 10:59:21.198 - [任务 24][local_pg - Copy] - Query snapshot row size completed: local_pg - Copy(7f8f28e1-da3e-498c-9f29-50a4ced6933c) 
[INFO ] 2025-06-25 10:59:22.110 - [任务 24][local_pg - Copy] - Table table_16 has been completed batch read 
[INFO ] 2025-06-25 10:59:22.110 - [任务 24][local_pg - Copy] - Starting batch read from table: table_17 
[TRACE] 2025-06-25 10:59:22.116 - [任务 24][local_pg - Copy] - Table table_17 is going to be initial synced 
[INFO ] 2025-06-25 10:59:23.161 - [任务 24][local_pg - Copy] - Table table_17 has been completed batch read 
[INFO ] 2025-06-25 10:59:23.161 - [任务 24][local_pg - Copy] - Starting batch read from table: table_19 
[TRACE] 2025-06-25 10:59:23.161 - [任务 24][local_pg - Copy] - Table table_19 is going to be initial synced 
[INFO ] 2025-06-25 10:59:23.851 - [任务 24][local_pg - Copy] - Table table_19 has been completed batch read 
[INFO ] 2025-06-25 10:59:23.852 - [任务 24][local_pg - Copy] - Starting batch read from table: table_40 
[TRACE] 2025-06-25 10:59:23.852 - [任务 24][local_pg - Copy] - Table table_40 is going to be initial synced 
[INFO ] 2025-06-25 10:59:27.587 - [任务 24][local_pg - Copy] - Table table_40 has been completed batch read 
[INFO ] 2025-06-25 10:59:27.587 - [任务 24][local_pg - Copy] - Starting batch read from table: table_41 
[TRACE] 2025-06-25 10:59:27.587 - [任务 24][local_pg - Copy] - Table table_41 is going to be initial synced 
[INFO ] 2025-06-25 10:59:28.378 - [任务 24][local_pg - Copy] - Table table_41 has been completed batch read 
[INFO ] 2025-06-25 10:59:28.378 - [任务 24][local_pg - Copy] - Starting batch read from table: table_42 
[TRACE] 2025-06-25 10:59:28.378 - [任务 24][local_pg - Copy] - Table table_42 is going to be initial synced 
[INFO ] 2025-06-25 10:59:28.907 - [任务 24][local_pg - Copy] - Table table_42 has been completed batch read 
[INFO ] 2025-06-25 10:59:28.907 - [任务 24][local_pg - Copy] - Starting batch read from table: table_43 
[TRACE] 2025-06-25 10:59:37.828 - [任务 24][local_pg - Copy] - Table table_43 is going to be initial synced 
[INFO ] 2025-06-25 10:59:43.780 - [任务 24][local_pg - Copy] - Table table_43 has been completed batch read 
[INFO ] 2025-06-25 10:59:43.780 - [任务 24][local_pg - Copy] - Starting batch read from table: table_44 
[TRACE] 2025-06-25 10:59:43.780 - [任务 24][local_pg - Copy] - Table table_44 is going to be initial synced 
[INFO ] 2025-06-25 10:59:44.337 - [任务 24][local_pg - Copy] - Table table_44 has been completed batch read 
[INFO ] 2025-06-25 10:59:44.337 - [任务 24][local_pg - Copy] - Starting batch read from table: table_45 
[TRACE] 2025-06-25 10:59:44.337 - [任务 24][local_pg - Copy] - Table table_45 is going to be initial synced 
[INFO ] 2025-06-25 10:59:44.892 - [任务 24][local_pg - Copy] - Table table_45 has been completed batch read 
[INFO ] 2025-06-25 10:59:44.892 - [任务 24][local_pg - Copy] - Starting batch read from table: table_46 
[TRACE] 2025-06-25 10:59:45.097 - [任务 24][local_pg - Copy] - Table table_46 is going to be initial synced 
[INFO ] 2025-06-25 10:59:45.995 - [任务 24][local_pg - Copy] - Table table_46 has been completed batch read 
[INFO ] 2025-06-25 10:59:45.995 - [任务 24][local_pg - Copy] - Starting batch read from table: table_47 
[TRACE] 2025-06-25 10:59:46.195 - [任务 24][local_pg - Copy] - Table table_47 is going to be initial synced 
[INFO ] 2025-06-25 10:59:46.621 - [任务 24][local_pg - Copy] - Table table_47 has been completed batch read 
[INFO ] 2025-06-25 10:59:46.621 - [任务 24][local_pg - Copy] - Starting batch read from table: table_48 
[TRACE] 2025-06-25 10:59:46.621 - [任务 24][local_pg - Copy] - Table table_48 is going to be initial synced 
[INFO ] 2025-06-25 10:59:47.516 - [任务 24][local_pg - Copy] - Table table_48 has been completed batch read 
[INFO ] 2025-06-25 10:59:47.516 - [任务 24][local_pg - Copy] - Starting batch read from table: table_49 
[TRACE] 2025-06-25 10:59:47.516 - [任务 24][local_pg - Copy] - Table table_49 is going to be initial synced 
[INFO ] 2025-06-25 10:59:48.469 - [任务 24][local_pg - Copy] - Table table_49 has been completed batch read 
[INFO ] 2025-06-25 10:59:48.469 - [任务 24][local_pg - Copy] - Starting batch read from table: table_30 
[TRACE] 2025-06-25 10:59:48.469 - [任务 24][local_pg - Copy] - Table table_30 is going to be initial synced 
[INFO ] 2025-06-25 10:59:49.506 - [任务 24][local_pg - Copy] - Table table_30 has been completed batch read 
[INFO ] 2025-06-25 10:59:49.507 - [任务 24][local_pg - Copy] - Starting batch read from table: table_31 
[TRACE] 2025-06-25 10:59:49.507 - [任务 24][local_pg - Copy] - Table table_31 is going to be initial synced 
[INFO ] 2025-06-25 10:59:49.993 - [任务 24][local_pg - Copy] - Table table_31 has been completed batch read 
[INFO ] 2025-06-25 10:59:49.993 - [任务 24][local_pg - Copy] - Starting batch read from table: table_32 
[TRACE] 2025-06-25 10:59:49.994 - [任务 24][local_pg - Copy] - Table table_32 is going to be initial synced 
[INFO ] 2025-06-25 10:59:50.779 - [任务 24][local_pg - Copy] - Table table_32 has been completed batch read 
[INFO ] 2025-06-25 10:59:50.779 - [任务 24][local_pg - Copy] - Starting batch read from table: table_33 
[TRACE] 2025-06-25 10:59:50.779 - [任务 24][local_pg - Copy] - Table table_33 is going to be initial synced 
[INFO ] 2025-06-25 10:59:51.540 - [任务 24][local_pg - Copy] - Table table_33 has been completed batch read 
[INFO ] 2025-06-25 10:59:51.540 - [任务 24][local_pg - Copy] - Starting batch read from table: table_34 
[TRACE] 2025-06-25 10:59:51.540 - [任务 24][local_pg - Copy] - Table table_34 is going to be initial synced 
[INFO ] 2025-06-25 10:59:52.285 - [任务 24][local_pg - Copy] - Table table_34 has been completed batch read 
[INFO ] 2025-06-25 10:59:52.285 - [任务 24][local_pg - Copy] - Starting batch read from table: table_35 
[TRACE] 2025-06-25 10:59:52.285 - [任务 24][local_pg - Copy] - Table table_35 is going to be initial synced 
[INFO ] 2025-06-25 10:59:53.026 - [任务 24][local_pg - Copy] - Table table_35 has been completed batch read 
[INFO ] 2025-06-25 10:59:53.027 - [任务 24][local_pg - Copy] - Starting batch read from table: table_36 
[TRACE] 2025-06-25 10:59:53.027 - [任务 24][local_pg - Copy] - Table table_36 is going to be initial synced 
[INFO ] 2025-06-25 10:59:54.053 - [任务 24][local_pg - Copy] - Table table_36 has been completed batch read 
[INFO ] 2025-06-25 10:59:54.054 - [任务 24][local_pg - Copy] - Starting batch read from table: table_37 
[TRACE] 2025-06-25 10:59:54.054 - [任务 24][local_pg - Copy] - Table table_37 is going to be initial synced 
[INFO ] 2025-06-25 10:59:54.683 - [任务 24][local_pg - Copy] - Table table_37 has been completed batch read 
[INFO ] 2025-06-25 10:59:54.686 - [任务 24][local_pg - Copy] - Starting batch read from table: table_38 
[TRACE] 2025-06-25 10:59:54.686 - [任务 24][local_pg - Copy] - Table table_38 is going to be initial synced 
[INFO ] 2025-06-25 10:59:55.393 - [任务 24][local_pg - Copy] - Table table_38 has been completed batch read 
[INFO ] 2025-06-25 10:59:55.393 - [任务 24][local_pg - Copy] - Starting batch read from table: table_39 
[TRACE] 2025-06-25 10:59:55.393 - [任务 24][local_pg - Copy] - Table table_39 is going to be initial synced 
[INFO ] 2025-06-25 10:59:55.804 - [任务 24][local_pg - Copy] - Table table_39 has been completed batch read 
[INFO ] 2025-06-25 10:59:55.804 - [任务 24][local_pg - Copy] - Starting batch read from table: table_20 
[TRACE] 2025-06-25 10:59:55.805 - [任务 24][local_pg - Copy] - Table table_20 is going to be initial synced 
[INFO ] 2025-06-25 10:59:57.279 - [任务 24][local_pg - Copy] - Table table_20 has been completed batch read 
[INFO ] 2025-06-25 10:59:57.279 - [任务 24][local_pg - Copy] - Starting batch read from table: table_21 
[TRACE] 2025-06-25 10:59:57.279 - [任务 24][local_pg - Copy] - Table table_21 is going to be initial synced 
[INFO ] 2025-06-25 10:59:57.741 - [任务 24][local_pg - Copy] - Table table_21 has been completed batch read 
[INFO ] 2025-06-25 10:59:57.742 - [任务 24][local_pg - Copy] - Starting batch read from table: table_1 
[TRACE] 2025-06-25 10:59:57.742 - [任务 24][local_pg - Copy] - Table table_1 is going to be initial synced 
[INFO ] 2025-06-25 10:59:58.679 - [任务 24][local_pg - Copy] - Table table_1 has been completed batch read 
[INFO ] 2025-06-25 10:59:58.679 - [任务 24][local_pg - Copy] - Starting batch read from table: table_22 
[TRACE] 2025-06-25 10:59:58.679 - [任务 24][local_pg - Copy] - Table table_22 is going to be initial synced 
[INFO ] 2025-06-25 10:59:59.395 - [任务 24][local_pg - Copy] - Table table_22 has been completed batch read 
[INFO ] 2025-06-25 10:59:59.396 - [任务 24][local_pg - Copy] - Starting batch read from table: table_2 
[TRACE] 2025-06-25 10:59:59.396 - [任务 24][local_pg - Copy] - Table table_2 is going to be initial synced 
[INFO ] 2025-06-25 11:00:00.309 - [任务 24][local_pg - Copy] - Table table_2 has been completed batch read 
[INFO ] 2025-06-25 11:00:00.310 - [任务 24][local_pg - Copy] - Starting batch read from table: table_3 
[TRACE] 2025-06-25 11:00:00.310 - [任务 24][local_pg - Copy] - Table table_3 is going to be initial synced 
[INFO ] 2025-06-25 11:00:01.314 - [任务 24][local_pg - Copy] - Table table_3 has been completed batch read 
[INFO ] 2025-06-25 11:00:01.314 - [任务 24][local_pg - Copy] - Starting batch read from table: table_23 
[TRACE] 2025-06-25 11:00:01.315 - [任务 24][local_pg - Copy] - Table table_23 is going to be initial synced 
[INFO ] 2025-06-25 11:00:02.247 - [任务 24][local_pg - Copy] - Table table_23 has been completed batch read 
[INFO ] 2025-06-25 11:00:02.247 - [任务 24][local_pg - Copy] - Starting batch read from table: table_4 
[TRACE] 2025-06-25 11:00:02.247 - [任务 24][local_pg - Copy] - Table table_4 is going to be initial synced 
[INFO ] 2025-06-25 11:00:02.801 - [任务 24][local_pg - Copy] - Table table_4 has been completed batch read 
[INFO ] 2025-06-25 11:00:02.801 - [任务 24][local_pg - Copy] - Starting batch read from table: table_24 
[TRACE] 2025-06-25 11:00:02.801 - [任务 24][local_pg - Copy] - Table table_24 is going to be initial synced 
[INFO ] 2025-06-25 11:00:03.708 - [任务 24][local_pg - Copy] - Table table_24 has been completed batch read 
[INFO ] 2025-06-25 11:00:03.708 - [任务 24][local_pg - Copy] - Starting batch read from table: table_25 
[TRACE] 2025-06-25 11:00:03.708 - [任务 24][local_pg - Copy] - Table table_25 is going to be initial synced 
[INFO ] 2025-06-25 11:00:04.761 - [任务 24][local_pg - Copy] - Table table_25 has been completed batch read 
[INFO ] 2025-06-25 11:00:04.761 - [任务 24][local_pg - Copy] - Starting batch read from table: table_5 
[TRACE] 2025-06-25 11:00:04.761 - [任务 24][local_pg - Copy] - Table table_5 is going to be initial synced 
[INFO ] 2025-06-25 11:00:05.763 - [任务 24][local_pg - Copy] - Table table_5 has been completed batch read 
[INFO ] 2025-06-25 11:00:05.763 - [任务 24][local_pg - Copy] - Starting batch read from table: table_26 
[TRACE] 2025-06-25 11:00:05.964 - [任务 24][local_pg - Copy] - Table table_26 is going to be initial synced 
[INFO ] 2025-06-25 11:00:06.896 - [任务 24][local_pg - Copy] - Table table_26 has been completed batch read 
[INFO ] 2025-06-25 11:00:06.896 - [任务 24][local_pg - Copy] - Starting batch read from table: table_6 
[TRACE] 2025-06-25 11:00:06.897 - [任务 24][local_pg - Copy] - Table table_6 is going to be initial synced 
[INFO ] 2025-06-25 11:00:08.031 - [任务 24][local_pg - Copy] - Table table_6 has been completed batch read 
[INFO ] 2025-06-25 11:00:08.031 - [任务 24][local_pg - Copy] - Starting batch read from table: table_27 
[TRACE] 2025-06-25 11:00:08.031 - [任务 24][local_pg - Copy] - Table table_27 is going to be initial synced 
[INFO ] 2025-06-25 11:00:08.479 - [任务 24][local_pg - Copy] - Table table_27 has been completed batch read 
[INFO ] 2025-06-25 11:00:08.480 - [任务 24][local_pg - Copy] - Starting batch read from table: table_7 
[TRACE] 2025-06-25 11:00:08.480 - [任务 24][local_pg - Copy] - Table table_7 is going to be initial synced 
[INFO ] 2025-06-25 11:00:09.259 - [任务 24][local_pg - Copy] - Table table_7 has been completed batch read 
[INFO ] 2025-06-25 11:00:09.259 - [任务 24][local_pg - Copy] - Starting batch read from table: table_28 
[TRACE] 2025-06-25 11:00:09.260 - [任务 24][local_pg - Copy] - Table table_28 is going to be initial synced 
[INFO ] 2025-06-25 11:00:09.750 - [任务 24][local_pg - Copy] - Table table_28 has been completed batch read 
[INFO ] 2025-06-25 11:00:09.751 - [任务 24][local_pg - Copy] - Starting batch read from table: table_8 
[TRACE] 2025-06-25 11:00:09.751 - [任务 24][local_pg - Copy] - Table table_8 is going to be initial synced 
[INFO ] 2025-06-25 11:00:10.722 - [任务 24][local_pg - Copy] - Table table_8 has been completed batch read 
[INFO ] 2025-06-25 11:00:10.722 - [任务 24][local_pg - Copy] - Starting batch read from table: table_29 
[TRACE] 2025-06-25 11:00:10.722 - [任务 24][local_pg - Copy] - Table table_29 is going to be initial synced 
[INFO ] 2025-06-25 11:00:11.157 - [任务 24][local_pg - Copy] - Table table_29 has been completed batch read 
[INFO ] 2025-06-25 11:00:11.157 - [任务 24][local_pg - Copy] - Starting batch read from table: table_9 
[TRACE] 2025-06-25 11:00:11.157 - [任务 24][local_pg - Copy] - Table table_9 is going to be initial synced 
[INFO ] 2025-06-25 11:00:11.706 - [任务 24][local_pg - Copy] - Table table_9 has been completed batch read 
[TRACE] 2025-06-25 11:00:11.707 - [任务 24][local_pg - Copy] - Initial sync completed 
[INFO ] 2025-06-25 11:00:11.707 - [任务 24][local_pg - Copy] - Batch read completed. 
[TRACE] 2025-06-25 11:00:11.708 - [任务 24][local_pg - Copy] - Incremental sync starting... 
[TRACE] 2025-06-25 11:00:11.708 - [任务 24][local_pg - Copy] - Initial sync completed 
[TRACE] 2025-06-25 11:00:11.709 - [任务 24][local_pg - Copy] - Starting stream read, table list: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-25 11:00:11.709 - [任务 24][local_pg - Copy] - Starting incremental sync using database log parser 
[WARN ] 2025-06-25 11:00:11.723 - [任务 24][local_pg - Copy] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-25 11:00:11.723 - [任务 24][local_pg - Copy] - Using an existing logical replication slot, slotName:tapdata_cdc_ab0483bd_a43e_434f_b5a8_b3218d4c1d65 
[TRACE] 2025-06-25 11:00:11.924 - [任务 24][local_pg - Copy] - Connector PostgreSQL incremental start succeed, tables: [table_50, table_10, table_11, table_12, table_13, table_14, table_15, table_16, table_17, table_19, table_40, table_41, table_42, table_43, table_44, table_45, table_46, table_47, table_48, table_49, table_30, table_31, table_32, table_33, table_34, table_35, table_36, table_37, table_38, table_39, table_20, table_21, table_1, table_22, table_2, table_3, table_23, table_4, table_24, table_25, table_5, table_26, table_6, table_27, table_7, table_28, table_8, table_29, table_9], data change syncing 
[TRACE] 2025-06-25 11:00:12.726 - [任务 24][local_pg] - Process after table "table_40" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.727 - [任务 24][local_pg] - Process after table "table_16" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.727 - [任务 24][local_pg] - Process after table "table_17" initial sync finished, cost: 1 ms 
[TRACE] 2025-06-25 11:00:12.727 - [任务 24][local_pg] - Process after table "table_41" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.727 - [任务 24][local_pg] - Process after table "table_19" initial sync finished, cost: 1 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_43" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_44" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_42" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_47" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_48" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_46" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_30" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_31" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_49" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_32" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_33" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_35" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_34" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_45" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_36" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_37" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.728 - [任务 24][local_pg] - Process after table "table_20" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_39" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_21" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_1" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_38" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_3" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_22" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_23" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_24" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_5" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.729 - [任务 24][local_pg] - Process after table "table_25" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.730 - [任务 24][local_pg] - Process after table "table_4" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.730 - [任务 24][local_pg] - Process after table "table_26" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.730 - [任务 24][local_pg] - Process after table "table_7" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.730 - [任务 24][local_pg] - Process after table "table_6" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.730 - [任务 24][local_pg] - Process after table "table_27" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.730 - [任务 24][local_pg] - Process after table "table_8" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.730 - [任务 24][local_pg] - Process after table "table_28" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.730 - [任务 24][local_pg] - Process after table "table_29" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.730 - [任务 24][local_pg] - Process after table "table_9" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-25 11:00:12.733 - [任务 24][local_pg] - Process after table "table_11" initial sync finished, cost: 6 ms 
[TRACE] 2025-06-25 11:00:12.733 - [任务 24][local_pg] - Process after table "table_12" initial sync finished, cost: 8 ms 
[TRACE] 2025-06-25 11:00:12.737 - [任务 24][local_pg] - Process after table "table_13" initial sync finished, cost: 10 ms 
[TRACE] 2025-06-25 11:00:12.737 - [任务 24][local_pg] - Process after table "table_14" initial sync finished, cost: 12 ms 
[TRACE] 2025-06-25 11:00:12.741 - [任务 24][local_pg] - Process after table "table_50" initial sync finished, cost: 16 ms 
[TRACE] 2025-06-25 11:00:12.741 - [任务 24][local_pg] - Process after table "table_15" initial sync finished, cost: 16 ms 
[TRACE] 2025-06-25 11:00:12.741 - [任务 24][local_pg] - Process after table "table_10" initial sync finished, cost: 16 ms 
[INFO ] 2025-06-25 11:00:12.943 - [任务 24][local_pg] - Process after all table(s) initial sync are finished，table number: 49 
[TRACE] 2025-06-25 11:01:13.953 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] running status set to false 
[TRACE] 2025-06-25 11:01:14.122 - [任务 24][local_pg - Copy] - Incremental sync completed 
[TRACE] 2025-06-25 11:01:14.123 - [任务 24][local_pg - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750820353763 
[TRACE] 2025-06-25 11:01:14.123 - [任务 24][local_pg - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_7f8f28e1-da3e-498c-9f29-50a4ced6933c_1750820353763 
[TRACE] 2025-06-25 11:01:14.123 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] schema data cleaned 
[TRACE] 2025-06-25 11:01:14.123 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] monitor closed 
[TRACE] 2025-06-25 11:01:14.124 - [任务 24][local_pg - Copy] - Node local_pg - Copy[7f8f28e1-da3e-498c-9f29-50a4ced6933c] close complete, cost 176 ms 
[TRACE] 2025-06-25 11:01:14.124 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] running status set to false 
[TRACE] 2025-06-25 11:01:14.130 - [任务 24][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750820353759 
[TRACE] 2025-06-25 11:01:14.130 - [任务 24][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_cd318a71-4925-416e-a177-0690f8c3e950_1750820353759 
[TRACE] 2025-06-25 11:01:14.130 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] schema data cleaned 
[TRACE] 2025-06-25 11:01:14.130 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] monitor closed 
[TRACE] 2025-06-25 11:01:14.335 - [任务 24][local_pg] - Node local_pg[cd318a71-4925-416e-a177-0690f8c3e950] close complete, cost 7 ms 
[TRACE] 2025-06-25 11:01:23.286 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-25 11:01:24.296 - [任务 24] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@d5de0ad 
[TRACE] 2025-06-25 11:01:24.298 - [任务 24] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6775372a 
[TRACE] 2025-06-25 11:01:24.299 - [任务 24] - Stop task milestones: 685b55793439e7780d3c42fd(任务 24)  
[TRACE] 2025-06-25 11:01:24.414 - [任务 24] - Stopped task aspect(s) 
[TRACE] 2025-06-25 11:01:24.415 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2025-06-25 11:01:24.415 - [任务 24] - Task stopped. 
[TRACE] 2025-06-25 11:01:24.445 - [任务 24] - Remove memory task client succeed, task: 任务 24[685b55793439e7780d3c42fd] 
[TRACE] 2025-06-25 11:01:24.445 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[685b55793439e7780d3c42fd] 
