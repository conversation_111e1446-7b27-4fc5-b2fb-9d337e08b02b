[TRACE] 2025-06-23 11:21:37.037 - [任务 23] - Task initialization... 
[TRACE] 2025-06-23 11:21:37.037 - [任务 23] - Start task milestones: 6858c7f8783fb251d8b8dadf(任务 23) 
[INFO ] 2025-06-23 11:21:37.244 - [任务 23] - Loading table structure completed 
[TRACE] 2025-06-23 11:21:37.244 - [任务 23] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-06-23 11:21:37.280 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 11:21:37.280 - [任务 23] - Task started 
[TRACE] 2025-06-23 11:21:37.291 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:21:37.291 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:21:37.291 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:21:37.291 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 11:21:37.493 - [任务 23][local_pg] - Enable partition table support for source database 
[INFO ] 2025-06-23 11:21:37.769 - [任务 23][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-06-23 11:21:37.769 - [任务 23][local_pg] - Source node "local_pg" read batch size: 100 
[TRACE] 2025-06-23 11:21:37.769 - [任务 23][local_pg] - Source node "local_pg" event queue capacity: 200 
[TRACE] 2025-06-23 11:21:37.787 - [任务 23][local_pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-23 11:21:37.787 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 11:21:37.800 - [任务 23][local_pg] - new logical replication slot created, slotName:tapdata_cdc_717ddfb7_0ce8_4a13_ba69_9a5f6dc258af 
[INFO ] 2025-06-23 11:21:37.800 - [任务 23][local_pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 11:21:37.846 - [任务 23][local_pg] - Starting batch read from 1 tables 
[TRACE] 2025-06-23 11:21:37.847 - [任务 23][local_pg] - Initial sync started 
[INFO ] 2025-06-23 11:21:37.847 - [任务 23][local_pg] - Starting batch read from table: refreshtoken 
[TRACE] 2025-06-23 11:21:37.847 - [任务 23][local_pg] - Table refreshtoken is going to be initial synced 
[TRACE] 2025-06-23 11:21:37.848 - [任务 23][local_pg] - Query snapshot row size completed: local_pg(65b109a1-e801-4111-b351-91c82e7846cd) 
[INFO ] 2025-06-23 11:21:37.849 - [任务 23][local_pg] - Table refreshtoken has been completed batch read 
[TRACE] 2025-06-23 11:21:37.849 - [任务 23][local_pg] - Initial sync completed 
[INFO ] 2025-06-23 11:21:37.849 - [任务 23][local_pg] - Batch read completed. 
[TRACE] 2025-06-23 11:21:37.849 - [任务 23][local_pg] - Incremental sync starting... 
[TRACE] 2025-06-23 11:21:37.849 - [任务 23][local_pg] - Initial sync completed 
[TRACE] 2025-06-23 11:21:37.849 - [任务 23][local_pg] - Starting stream read, table list: [refreshtoken], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 11:21:37.849 - [任务 23][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-06-23 11:21:37.853 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 11:21:37.853 - [任务 23][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_717ddfb7_0ce8_4a13_ba69_9a5f6dc258af 
[INFO ] 2025-06-23 11:21:37.952 - [任务 23][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 11:21:37.952 - [任务 23][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 11:21:37.952 - [任务 23][sqlserver_ad] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 11:21:38.019 - [任务 23][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-23 11:21:38.019 - [任务 23][local_pg] - Connector PostgreSQL incremental start succeed, tables: [refreshtoken], data change syncing 
[TRACE] 2025-06-23 11:21:38.220 - [任务 23][sqlserver_ad] - The table refreshtoken has already exist. 
[TRACE] 2025-06-23 11:21:38.913 - [任务 23][sqlserver_ad] - Table 'refreshtoken' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-23 11:21:38.914 - [任务 23][sqlserver_ad] - Process after table "refreshtoken" initial sync finished, cost: 1 ms 
[INFO ] 2025-06-23 11:21:38.915 - [任务 23][sqlserver_ad] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-23 11:23:30.182 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] running status set to false 
[TRACE] 2025-06-23 11:23:30.184 - [任务 23][local_pg] - Incremental sync completed 
[TRACE] 2025-06-23 11:23:30.184 - [任务 23][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750648897629 
[TRACE] 2025-06-23 11:23:30.184 - [任务 23][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750648897629 
[TRACE] 2025-06-23 11:23:30.184 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] schema data cleaned 
[TRACE] 2025-06-23 11:23:30.184 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] monitor closed 
[TRACE] 2025-06-23 11:23:30.185 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] close complete, cost 59 ms 
[TRACE] 2025-06-23 11:23:30.195 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] running status set to false 
[TRACE] 2025-06-23 11:23:30.195 - [任务 23][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750648897613 
[TRACE] 2025-06-23 11:23:30.196 - [任务 23][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750648897613 
[TRACE] 2025-06-23 11:23:30.196 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] schema data cleaned 
[TRACE] 2025-06-23 11:23:30.196 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] monitor closed 
[TRACE] 2025-06-23 11:23:30.197 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] close complete, cost 11 ms 
[TRACE] 2025-06-23 11:23:36.414 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 11:23:37.421 - [任务 23] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@3796db6d 
[TRACE] 2025-06-23 11:23:37.425 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@66b8d27 
[TRACE] 2025-06-23 11:23:37.426 - [任务 23] - Stop task milestones: 6858c7f8783fb251d8b8dadf(任务 23)  
[TRACE] 2025-06-23 11:23:37.550 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-06-23 11:23:37.550 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 11:23:37.550 - [任务 23] - Task stopped. 
[TRACE] 2025-06-23 11:23:37.601 - [任务 23] - Remove memory task client succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 11:23:37.601 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 11:35:11.973 - [任务 23] - Task initialization... 
[TRACE] 2025-06-23 11:35:11.974 - [任务 23] - Start task milestones: 6858c7f8783fb251d8b8dadf(任务 23) 
[INFO ] 2025-06-23 11:35:12.073 - [任务 23] - Loading table structure completed 
[TRACE] 2025-06-23 11:35:12.073 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-23 11:35:12.112 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 11:35:12.112 - [任务 23] - Task started 
[TRACE] 2025-06-23 11:35:12.124 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:35:12.124 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:35:12.124 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:35:12.124 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 11:35:12.124 - [任务 23][local_pg] - Enable partition table support for source database 
[INFO ] 2025-06-23 11:35:12.588 - [任务 23][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-06-23 11:35:12.588 - [任务 23][local_pg] - Source node "local_pg" read batch size: 100 
[TRACE] 2025-06-23 11:35:12.588 - [任务 23][local_pg] - Source node "local_pg" event queue capacity: 200 
[TRACE] 2025-06-23 11:35:12.588 - [任务 23][local_pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-23 11:35:12.604 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 11:35:12.615 - [任务 23][local_pg] - new logical replication slot created, slotName:tapdata_cdc_9e81f23a_e128_45fa_a12c_02db4e199e9b 
[INFO ] 2025-06-23 11:35:12.615 - [任务 23][local_pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 11:35:12.665 - [任务 23][local_pg] - Batch read completed. 
[TRACE] 2025-06-23 11:35:12.665 - [任务 23][local_pg] - Incremental sync starting... 
[TRACE] 2025-06-23 11:35:12.666 - [任务 23][local_pg] - Initial sync completed 
[TRACE] 2025-06-23 11:35:12.666 - [任务 23][local_pg] - Starting stream read, table list: [refreshtoken], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 11:35:12.666 - [任务 23][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-06-23 11:35:12.666 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 11:35:12.688 - [任务 23][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_9e81f23a_e128_45fa_a12c_02db4e199e9b 
[TRACE] 2025-06-23 11:35:12.688 - [任务 23][local_pg] - Connector PostgreSQL incremental start succeed, tables: [refreshtoken], data change syncing 
[INFO ] 2025-06-23 11:35:12.785 - [任务 23][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 11:35:12.786 - [任务 23][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 11:35:12.786 - [任务 23][sqlserver_ad] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 11:35:12.913 - [任务 23][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-23 11:35:12.913 - [任务 23][sqlserver_ad] - The table refreshtoken has already exist. 
[WARN ] 2025-06-23 11:38:40.268 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:38:40.470 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 1/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:39:40 
[WARN ] 2025-06-23 11:39:40.349 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:39:40.550 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 2/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:40:40 
[WARN ] 2025-06-23 11:40:40.439 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:40:40.642 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 3/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:41:40 
[WARN ] 2025-06-23 11:41:40.521 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:41:40.723 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 4/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:42:40 
[WARN ] 2025-06-23 11:42:40.620 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:42:40.821 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 5/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:43:40 
[WARN ] 2025-06-23 11:43:40.696 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:43:40.901 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 6/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:44:40 
[WARN ] 2025-06-23 11:44:40.781 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:44:40.781 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 7/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:45:40 
[WARN ] 2025-06-23 11:45:40.865 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:45:40.865 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 8/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:46:40 
[WARN ] 2025-06-23 11:46:40.939 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:46:40.939 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 9/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:47:40 
[WARN ] 2025-06-23 11:47:40.998 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 11:47:41.200 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 10/10, first retry time 2025-06-23 11:38:40, next retry time 2025-06-23 11:48:41 
[INFO ] 2025-06-23 11:48:41.079 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD failed, total cost 00:10:00.873000 
[TRACE] 2025-06-23 11:48:41.112 - [任务 23][sqlserver_ad] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken 
[ERROR] 2025-06-23 11:48:41.113 - [任务 23][sqlserver_ad] - Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken

<-- Simple Stack Trace -->
Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1127)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:885)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:858)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:811)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:694)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: java.lang.RuntimeException: Error occurred when retrying write record: io.tapdata.entity.event.dml.TapInsertRecordEvent@69214cdc: {"after":{"userid":"UATSADM","clientip":"127.0.0.1","tokenid":"ZDU4M2YwMTA0YmM0NDM2NDI4NDc0YzBhZjA2YzQyYzQ=","expirydtm":"2025-06-23T09:49:11","createdtm":"2025-06-23T19:11:23","createby":"UATSADM","updatedtm":"2025-06-23T19:11:23","updateby":"UATSADM"},"containsIllegalDate":false,"database":"postgres","pdkGroup":"io.tapdata","pdkId":"postgres","pdkVersion":"1.0-SNAPSHOT","referenceTime":1750649918434,"schema":"public","tableId":"refreshtoken","time":1750649919188,"type":300}
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:166)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:497)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	... 96 more
Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 101 more

[TRACE] 2025-06-23 11:48:41.113 - [任务 23][sqlserver_ad] - Job suspend in error handle 
[TRACE] 2025-06-23 11:48:41.184 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] running status set to false 
[INFO ] 2025-06-23 11:48:41.387 - [任务 23] - Task [任务 23] cannot retry, reason: Maximum retry time exceeded: 2025-06-23T11:48:40.201+08:00[Asia/Shanghai] 
[TRACE] 2025-06-23 11:48:41.508 - [任务 23][local_pg] - Incremental sync completed 
[TRACE] 2025-06-23 11:48:41.513 - [任务 23][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750649712353 
[TRACE] 2025-06-23 11:48:41.513 - [任务 23][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750649712353 
[TRACE] 2025-06-23 11:48:41.513 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] schema data cleaned 
[TRACE] 2025-06-23 11:48:41.513 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] monitor closed 
[TRACE] 2025-06-23 11:48:41.513 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] close complete, cost 328 ms 
[TRACE] 2025-06-23 11:48:41.513 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] running status set to false 
[TRACE] 2025-06-23 11:48:41.519 - [任务 23][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750649712377 
[TRACE] 2025-06-23 11:48:41.519 - [任务 23][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750649712377 
[TRACE] 2025-06-23 11:48:41.519 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] schema data cleaned 
[TRACE] 2025-06-23 11:48:41.520 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] monitor closed 
[TRACE] 2025-06-23 11:48:41.520 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] close complete, cost 6 ms 
[INFO ] 2025-06-23 11:48:46.228 - [任务 23] - Task [任务 23] cannot retry, reason: Maximum retry time exceeded: 2025-06-23T11:48:40.201+08:00[Asia/Shanghai] 
[INFO ] 2025-06-23 11:48:51.239 - [任务 23] - Task [任务 23] cannot retry, reason: Maximum retry time exceeded: 2025-06-23T11:48:40.201+08:00[Asia/Shanghai] 
[TRACE] 2025-06-23 11:48:51.240 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 11:48:52.245 - [任务 23] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@5de43f83 
[TRACE] 2025-06-23 11:48:52.245 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@16a78a5b 
[TRACE] 2025-06-23 11:48:52.366 - [任务 23] - Stop task milestones: 6858c7f8783fb251d8b8dadf(任务 23)  
[TRACE] 2025-06-23 11:48:52.366 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-06-23 11:48:52.366 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 11:48:52.367 - [任务 23] - Task stopped. 
[TRACE] 2025-06-23 11:48:52.397 - [任务 23] - Remove memory task client succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 11:48:52.398 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 11:51:28.987 - [任务 23] - Task initialization... 
[TRACE] 2025-06-23 11:51:29.059 - [任务 23] - Start task milestones: 6858c7f8783fb251d8b8dadf(任务 23) 
[INFO ] 2025-06-23 11:51:29.059 - [任务 23] - Loading table structure completed 
[TRACE] 2025-06-23 11:51:29.159 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-23 11:51:29.159 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 11:51:29.214 - [任务 23] - Task started 
[TRACE] 2025-06-23 11:51:29.214 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:51:29.214 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] start preload schema,table counts: 1 
[TRACE] 2025-06-23 11:51:29.214 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 11:51:29.214 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 11:51:29.214 - [任务 23][local_pg] - Enable partition table support for source database 
[INFO ] 2025-06-23 11:51:29.371 - [任务 23][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-06-23 11:51:29.371 - [任务 23][local_pg] - Source node "local_pg" read batch size: 100 
[TRACE] 2025-06-23 11:51:29.371 - [任务 23][local_pg] - Source node "local_pg" event queue capacity: 200 
[INFO ] 2025-06-23 11:51:29.373 - [任务 23][local_pg] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-23 11:51:29.373 - [任务 23][local_pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":10549480840,\"lsn_commit\":10549480840,\"lsn\":10549480840,\"ts_usec\":1750649918434829}"} 
[INFO ] 2025-06-23 11:51:29.425 - [任务 23][local_pg] - Batch read completed. 
[TRACE] 2025-06-23 11:51:29.425 - [任务 23][local_pg] - Incremental sync starting... 
[TRACE] 2025-06-23 11:51:29.425 - [任务 23][local_pg] - Initial sync completed 
[TRACE] 2025-06-23 11:51:29.425 - [任务 23][local_pg] - Starting stream read, table list: [refreshtoken], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":10549480840,\"lsn_commit\":10549480840,\"lsn\":10549480840,\"ts_usec\":1750649918434829}"} 
[INFO ] 2025-06-23 11:51:29.425 - [任务 23][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-06-23 11:51:29.428 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 11:51:29.428 - [任务 23][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_9e81f23a_e128_45fa_a12c_02db4e199e9b 
[TRACE] 2025-06-23 11:51:29.580 - [任务 23][local_pg] - Connector PostgreSQL incremental start succeed, tables: [refreshtoken], data change syncing 
[INFO ] 2025-06-23 11:51:29.580 - [任务 23][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 11:51:29.580 - [任务 23][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 11:51:29.581 - [任务 23][sqlserver_ad] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 11:51:29.597 - [任务 23][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-23 11:52:16.525 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] running status set to false 
[TRACE] 2025-06-23 11:52:16.910 - [任务 23][local_pg] - Incremental sync completed 
[TRACE] 2025-06-23 11:52:16.911 - [任务 23][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750650689239 
[TRACE] 2025-06-23 11:52:16.911 - [任务 23][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750650689239 
[TRACE] 2025-06-23 11:52:16.911 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] schema data cleaned 
[TRACE] 2025-06-23 11:52:16.911 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] monitor closed 
[TRACE] 2025-06-23 11:52:16.912 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] close complete, cost 387 ms 
[TRACE] 2025-06-23 11:52:16.912 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] running status set to false 
[TRACE] 2025-06-23 11:52:16.916 - [任务 23][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750650689235 
[TRACE] 2025-06-23 11:52:16.916 - [任务 23][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750650689235 
[TRACE] 2025-06-23 11:52:16.917 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] schema data cleaned 
[TRACE] 2025-06-23 11:52:16.917 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] monitor closed 
[TRACE] 2025-06-23 11:52:16.917 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] close complete, cost 5 ms 
[TRACE] 2025-06-23 11:52:22.613 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 11:52:23.573 - [任务 23] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@f92105e 
[TRACE] 2025-06-23 11:52:23.574 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@71e92317 
[TRACE] 2025-06-23 11:52:23.574 - [任务 23] - Stop task milestones: 6858c7f8783fb251d8b8dadf(任务 23)  
[TRACE] 2025-06-23 11:52:23.696 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-06-23 11:52:23.696 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 11:52:23.696 - [任务 23] - Task stopped. 
[TRACE] 2025-06-23 11:52:23.740 - [任务 23] - Remove memory task client succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 11:52:23.740 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 12:13:49.112 - [任务 23] - Task initialization... 
[TRACE] 2025-06-23 12:13:49.244 - [任务 23] - Start task milestones: 6858c7f8783fb251d8b8dadf(任务 23) 
[INFO ] 2025-06-23 12:13:49.244 - [任务 23] - Loading table structure completed 
[TRACE] 2025-06-23 12:13:49.318 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-23 12:13:49.318 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 12:13:49.374 - [任务 23] - Task started 
[TRACE] 2025-06-23 12:13:49.374 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] start preload schema,table counts: 1 
[TRACE] 2025-06-23 12:13:49.374 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] start preload schema,table counts: 1 
[TRACE] 2025-06-23 12:13:49.374 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 12:13:49.375 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 12:13:49.375 - [任务 23][local_pg] - Enable partition table support for source database 
[INFO ] 2025-06-23 12:13:49.543 - [任务 23][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-06-23 12:13:49.543 - [任务 23][local_pg] - Source node "local_pg" read batch size: 100 
[TRACE] 2025-06-23 12:13:49.543 - [任务 23][local_pg] - Source node "local_pg" event queue capacity: 200 
[INFO ] 2025-06-23 12:13:49.544 - [任务 23][local_pg] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-23 12:13:49.544 - [任务 23][local_pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":10549480840,\"lsn_commit\":10549480840,\"lsn\":10549480840,\"ts_usec\":1750649918434829}"} 
[INFO ] 2025-06-23 12:13:49.586 - [任务 23][local_pg] - Batch read completed. 
[TRACE] 2025-06-23 12:13:49.586 - [任务 23][local_pg] - Incremental sync starting... 
[TRACE] 2025-06-23 12:13:49.586 - [任务 23][local_pg] - Initial sync completed 
[TRACE] 2025-06-23 12:13:49.586 - [任务 23][local_pg] - Starting stream read, table list: [refreshtoken], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":10549480840,\"lsn_commit\":10549480840,\"lsn\":10549480840,\"ts_usec\":1750649918434829}"} 
[INFO ] 2025-06-23 12:13:49.587 - [任务 23][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-06-23 12:13:49.587 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 12:13:49.612 - [任务 23][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_9e81f23a_e128_45fa_a12c_02db4e199e9b 
[TRACE] 2025-06-23 12:13:49.613 - [任务 23][local_pg] - Connector PostgreSQL incremental start succeed, tables: [refreshtoken], data change syncing 
[INFO ] 2025-06-23 12:13:49.817 - [任务 23][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 12:13:49.817 - [任务 23][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 12:13:49.817 - [任务 23][sqlserver_ad] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 12:13:50.019 - [任务 23][sqlserver_ad] - Apply table structure to target database 
[WARN ] 2025-06-23 12:22:41.204 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 12:22:41.205 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 1/10, first retry time 2025-06-23 12:22:41, next retry time 2025-06-23 12:23:41 
[TRACE] 2025-06-23 12:23:02.844 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] running status set to false 
[TRACE] 2025-06-23 12:23:03.093 - [任务 23][local_pg] - Incremental sync completed 
[TRACE] 2025-06-23 12:23:03.098 - [任务 23][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750652029397 
[TRACE] 2025-06-23 12:23:03.098 - [任务 23][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750652029397 
[TRACE] 2025-06-23 12:23:03.099 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] schema data cleaned 
[TRACE] 2025-06-23 12:23:03.099 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] monitor closed 
[TRACE] 2025-06-23 12:23:03.099 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] close complete, cost 255 ms 
[TRACE] 2025-06-23 12:23:03.099 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] running status set to false 
[INFO ] 2025-06-23 12:23:03.104 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD failed, total cost 00:00:21.900000 
[TRACE] 2025-06-23 12:23:03.104 - [任务 23][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750652029393 
[TRACE] 2025-06-23 12:23:03.104 - [任务 23][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750652029393 
[TRACE] 2025-06-23 12:23:03.105 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] schema data cleaned 
[TRACE] 2025-06-23 12:23:03.105 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] monitor closed 
[TRACE] 2025-06-23 12:23:03.119 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] close complete, cost 5 ms 
[TRACE] 2025-06-23 12:23:03.119 - [任务 23][sqlserver_ad] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): when operate table: refreshtoken, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-32) has been closed. 
[ERROR] 2025-06-23 12:23:03.324 - [任务 23][sqlserver_ad] - PDK retry exception (Server Error Code 0): when operate table: refreshtoken, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-32) has been closed. <-- Error Message -->
PDK retry exception (Server Error Code 0): when operate table: refreshtoken, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-32) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-32) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:31)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:51)
	io.tapdata.connector.mssql.dml.MssqlRecordWriterV2.<init>(MssqlRecordWriterV2.java:13)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-32) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:31)
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:51)
	at io.tapdata.connector.mssql.dml.MssqlRecordWriterV2.<init>(MssqlRecordWriterV2.java:13)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:474)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:885)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:858)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:811)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:694)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-32) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 103 more

[TRACE] 2025-06-23 12:23:10.240 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 12:23:11.060 - [任务 23] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@40a616a9 
[TRACE] 2025-06-23 12:23:11.060 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@146a1ef9 
[TRACE] 2025-06-23 12:23:11.184 - [任务 23] - Stop task milestones: 6858c7f8783fb251d8b8dadf(任务 23)  
[TRACE] 2025-06-23 12:23:11.184 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-06-23 12:23:11.184 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 12:23:11.184 - [任务 23] - Task stopped. 
[TRACE] 2025-06-23 12:23:11.231 - [任务 23] - Remove memory task client succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 12:23:11.233 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 12:23:17.513 - [任务 23] - Task initialization... 
[TRACE] 2025-06-23 12:23:17.575 - [任务 23] - Start task milestones: 6858c7f8783fb251d8b8dadf(任务 23) 
[INFO ] 2025-06-23 12:23:17.575 - [任务 23] - Loading table structure completed 
[TRACE] 2025-06-23 12:23:17.653 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-23 12:23:17.653 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 12:23:17.696 - [任务 23] - Task started 
[TRACE] 2025-06-23 12:23:17.696 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] start preload schema,table counts: 1 
[TRACE] 2025-06-23 12:23:17.696 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] start preload schema,table counts: 1 
[TRACE] 2025-06-23 12:23:17.696 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 12:23:17.696 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 12:23:17.843 - [任务 23][local_pg] - Enable partition table support for source database 
[INFO ] 2025-06-23 12:23:17.843 - [任务 23][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-06-23 12:23:17.843 - [任务 23][local_pg] - Source node "local_pg" read batch size: 100 
[TRACE] 2025-06-23 12:23:17.843 - [任务 23][local_pg] - Source node "local_pg" event queue capacity: 200 
[INFO ] 2025-06-23 12:23:17.843 - [任务 23][local_pg] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-23 12:23:17.885 - [任务 23][local_pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":10549480840,\"lsn_commit\":10549480840,\"lsn\":10549480840,\"ts_usec\":1750649918434829}"} 
[INFO ] 2025-06-23 12:23:17.885 - [任务 23][local_pg] - Batch read completed. 
[TRACE] 2025-06-23 12:23:17.885 - [任务 23][local_pg] - Incremental sync starting... 
[TRACE] 2025-06-23 12:23:17.885 - [任务 23][local_pg] - Initial sync completed 
[TRACE] 2025-06-23 12:23:17.885 - [任务 23][local_pg] - Starting stream read, table list: [refreshtoken], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":10549480840,\"lsn_commit\":10549480840,\"lsn\":10549480840,\"ts_usec\":1750649918434829}"} 
[INFO ] 2025-06-23 12:23:17.885 - [任务 23][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-06-23 12:23:17.888 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 12:23:17.888 - [任务 23][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_9e81f23a_e128_45fa_a12c_02db4e199e9b 
[TRACE] 2025-06-23 12:23:18.092 - [任务 23][local_pg] - Connector PostgreSQL incremental start succeed, tables: [refreshtoken], data change syncing 
[INFO ] 2025-06-23 12:23:18.120 - [任务 23][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 12:23:18.120 - [任务 23][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 12:23:18.140 - [任务 23][sqlserver_ad] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 12:23:18.140 - [任务 23][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-23 12:27:56.371 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] running status set to false 
[TRACE] 2025-06-23 12:27:56.699 - [任务 23][local_pg] - Incremental sync completed 
[TRACE] 2025-06-23 12:27:56.699 - [任务 23][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750652597715 
[TRACE] 2025-06-23 12:27:56.699 - [任务 23][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750652597715 
[TRACE] 2025-06-23 12:27:56.700 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] schema data cleaned 
[TRACE] 2025-06-23 12:27:56.700 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] monitor closed 
[TRACE] 2025-06-23 12:27:56.701 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] close complete, cost 412 ms 
[TRACE] 2025-06-23 12:27:56.706 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] running status set to false 
[TRACE] 2025-06-23 12:27:56.706 - [任务 23][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750652597719 
[TRACE] 2025-06-23 12:27:56.706 - [任务 23][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750652597719 
[TRACE] 2025-06-23 12:27:56.706 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] schema data cleaned 
[TRACE] 2025-06-23 12:27:56.707 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] monitor closed 
[TRACE] 2025-06-23 12:27:56.707 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] close complete, cost 5 ms 
[TRACE] 2025-06-23 12:28:06.444 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 12:28:07.450 - [任务 23] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@3266197f 
[TRACE] 2025-06-23 12:28:07.453 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@610f529d 
[TRACE] 2025-06-23 12:28:07.453 - [任务 23] - Stop task milestones: 6858c7f8783fb251d8b8dadf(任务 23)  
[TRACE] 2025-06-23 12:28:07.575 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-06-23 12:28:07.576 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 12:28:07.576 - [任务 23] - Task stopped. 
[TRACE] 2025-06-23 12:28:07.621 - [任务 23] - Remove memory task client succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 12:28:07.621 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 14:10:21.842 - [任务 23] - Task initialization... 
[TRACE] 2025-06-23 14:10:21.843 - [任务 23] - Start task milestones: 6858c7f8783fb251d8b8dadf(任务 23) 
[INFO ] 2025-06-23 14:10:22.096 - [任务 23] - Loading table structure completed 
[TRACE] 2025-06-23 14:10:22.472 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-23 14:10:22.472 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 14:10:22.624 - [任务 23] - Task started 
[TRACE] 2025-06-23 14:10:22.624 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] start preload schema,table counts: 1 
[TRACE] 2025-06-23 14:10:22.625 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] start preload schema,table counts: 1 
[TRACE] 2025-06-23 14:10:22.625 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 14:10:22.625 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 14:10:22.827 - [任务 23][local_pg] - Enable partition table support for source database 
[INFO ] 2025-06-23 14:10:23.294 - [任务 23][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-06-23 14:10:23.296 - [任务 23][local_pg] - Source node "local_pg" read batch size: 100 
[TRACE] 2025-06-23 14:10:23.296 - [任务 23][local_pg] - Source node "local_pg" event queue capacity: 200 
[TRACE] 2025-06-23 14:10:23.296 - [任务 23][local_pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-23 14:10:23.298 - [任务 23][local_pg] - Postgres specified time start increment is not supported except walminer, use the current time as the start increment 
[WARN ] 2025-06-23 14:10:23.321 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 14:10:23.321 - [任务 23][local_pg] - new logical replication slot created, slotName:tapdata_cdc_44343706_c194_47ca_a07c_3464ceda566f 
[INFO ] 2025-06-23 14:10:23.381 - [任务 23][local_pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 14:10:23.381 - [任务 23][local_pg] - Batch read completed. 
[TRACE] 2025-06-23 14:10:23.382 - [任务 23][local_pg] - Incremental sync starting... 
[TRACE] 2025-06-23 14:10:23.382 - [任务 23][local_pg] - Initial sync completed 
[TRACE] 2025-06-23 14:10:23.384 - [任务 23][local_pg] - Starting stream read, table list: [refreshtoken], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 14:10:23.384 - [任务 23][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-06-23 14:10:23.395 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 14:10:23.395 - [任务 23][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_44343706_c194_47ca_a07c_3464ceda566f 
[INFO ] 2025-06-23 14:10:23.403 - [任务 23][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 14:10:23.404 - [任务 23][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 14:10:23.404 - [任务 23][sqlserver_ad] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 14:10:23.534 - [任务 23][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-23 14:10:23.535 - [任务 23][local_pg] - Connector PostgreSQL incremental start succeed, tables: [refreshtoken], data change syncing 
[TRACE] 2025-06-23 14:10:23.738 - [任务 23][sqlserver_ad] - The table refreshtoken has already exist. 
[WARN ] 2025-06-23 14:13:26.562 - [任务 23][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-23 14:13:26.563 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 1/10, first retry time 2025-06-23 14:13:26, next retry time 2025-06-23 14:14:26 
[TRACE] 2025-06-23 14:13:38.335 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] running status set to false 
[TRACE] 2025-06-23 14:13:38.636 - [任务 23][local_pg] - Incremental sync completed 
[TRACE] 2025-06-23 14:13:38.660 - [任务 23][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750659022918 
[TRACE] 2025-06-23 14:13:38.660 - [任务 23][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750659022918 
[TRACE] 2025-06-23 14:13:38.662 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] schema data cleaned 
[TRACE] 2025-06-23 14:13:38.662 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] monitor closed 
[TRACE] 2025-06-23 14:13:38.664 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] close complete, cost 514 ms 
[TRACE] 2025-06-23 14:13:38.664 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] running status set to false 
[INFO ] 2025-06-23 14:14:37.045 - [任务 23][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD failed, total cost 00:01:10.536000 
[TRACE] 2025-06-23 14:14:37.046 - [任务 23][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750659022879 
[TRACE] 2025-06-23 14:14:37.046 - [任务 23][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750659022879 
[TRACE] 2025-06-23 14:14:37.046 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] schema data cleaned 
[TRACE] 2025-06-23 14:14:37.046 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] monitor closed 
[TRACE] 2025-06-23 14:14:37.061 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] close complete, cost 58383 ms 
[TRACE] 2025-06-23 14:14:37.061 - [任务 23][sqlserver_ad] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken 
[ERROR] 2025-06-23 14:14:37.267 - [任务 23][sqlserver_ad] - Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Socket closed
	java.base/sun.nio.ch.NioSocketImpl.endRead(NioSocketImpl.java:253)
	java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:332)
	java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1127)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:885)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:858)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:811)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:694)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2066)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7804)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7767)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection$1ConnectionCommand.doExecute(SQLServerConnection.java:3530)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectionCommand(SQLServerConnection.java:3534)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.setAutoCommit(SQLServerConnection.java:3667)
	at com.zaxxer.hikari.pool.ProxyConnection.setAutoCommit(ProxyConnection.java:414)
	at com.zaxxer.hikari.pool.HikariProxyConnection.setAutoCommit(HikariProxyConnection.java)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:34)
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:51)
	at io.tapdata.connector.mssql.dml.MssqlRecordWriterV2.<init>(MssqlRecordWriterV2.java:13)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:474)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	... 96 more
Caused by: java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.endRead(NioSocketImpl.java:253)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:332)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	... 113 more

[TRACE] 2025-06-23 14:14:42.140 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 14:14:43.035 - [任务 23] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@66b9898f 
[TRACE] 2025-06-23 14:14:43.036 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@15afa562 
[TRACE] 2025-06-23 14:14:43.164 - [任务 23] - Stop task milestones: 6858c7f8783fb251d8b8dadf(任务 23)  
[TRACE] 2025-06-23 14:14:43.165 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-06-23 14:14:43.167 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 14:14:43.168 - [任务 23] - Task stopped. 
[TRACE] 2025-06-23 14:14:43.222 - [任务 23] - Remove memory task client succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 14:14:43.225 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 14:15:55.637 - [任务 23] - Task initialization... 
[TRACE] 2025-06-23 14:15:55.710 - [任务 23] - Start task milestones: 6858c7f8783fb251d8b8dadf(任务 23) 
[INFO ] 2025-06-23 14:15:55.711 - [任务 23] - Loading table structure completed 
[TRACE] 2025-06-23 14:15:55.808 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-23 14:15:55.808 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 14:15:55.829 - [任务 23] - Task started 
[TRACE] 2025-06-23 14:15:55.829 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] start preload schema,table counts: 1 
[TRACE] 2025-06-23 14:15:55.829 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 14:15:55.830 - [任务 23][local_pg] - Enable partition table support for source database 
[TRACE] 2025-06-23 14:15:55.839 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] start preload schema,table counts: 1 
[TRACE] 2025-06-23 14:15:55.839 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 14:15:56.236 - [任务 23][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-06-23 14:15:56.236 - [任务 23][local_pg] - Source node "local_pg" read batch size: 100 
[TRACE] 2025-06-23 14:15:56.236 - [任务 23][local_pg] - Source node "local_pg" event queue capacity: 200 
[TRACE] 2025-06-23 14:15:56.236 - [任务 23][local_pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-23 14:15:56.236 - [任务 23][local_pg] - Postgres specified time start increment is not supported except walminer, use the current time as the start increment 
[WARN ] 2025-06-23 14:15:56.258 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 14:15:56.258 - [任务 23][local_pg] - new logical replication slot created, slotName:tapdata_cdc_d1f6fd82_f42f_41d4_a0bc_568cc907b33b 
[INFO ] 2025-06-23 14:15:56.336 - [任务 23][local_pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 14:15:56.336 - [任务 23][local_pg] - Batch read completed. 
[TRACE] 2025-06-23 14:15:56.336 - [任务 23][local_pg] - Incremental sync starting... 
[TRACE] 2025-06-23 14:15:56.336 - [任务 23][local_pg] - Initial sync completed 
[TRACE] 2025-06-23 14:15:56.337 - [任务 23][local_pg] - Starting stream read, table list: [refreshtoken], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 14:15:56.338 - [任务 23][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-06-23 14:15:56.338 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 14:15:56.360 - [任务 23][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_d1f6fd82_f42f_41d4_a0bc_568cc907b33b 
[TRACE] 2025-06-23 14:15:56.360 - [任务 23][local_pg] - Connector PostgreSQL incremental start succeed, tables: [refreshtoken], data change syncing 
[INFO ] 2025-06-23 14:15:56.493 - [任务 23][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 14:15:56.493 - [任务 23][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 14:15:56.493 - [任务 23][sqlserver_ad] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 14:15:56.631 - [任务 23][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-23 14:15:56.631 - [任务 23][sqlserver_ad] - The table refreshtoken has already exist. 
[TRACE] 2025-06-23 14:16:13.882 - [任务 23][sqlserver_ad] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken 
[ERROR] 2025-06-23 14:16:13.884 - [任务 23][sqlserver_ad] - Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken

<-- Simple Stack Trace -->
Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: refreshtoken
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1127)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:885)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:858)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:811)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:694)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initTargetQueueConsumer(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:210)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: java.lang.RuntimeException: Error occurred when retrying write record: io.tapdata.entity.event.dml.TapInsertRecordEvent@58b9fe27: {"after":{"userid":"UATSADM","clientip":"127.0.0.1","tokenid":"ZDU4MzYwMTA0YmIzNDM2MWE2MGFjNTJiZTM2NzI3YjI=","expirydtm":"2025-06-23T09:49:11","createdtm":"2025-06-23T19:11:23","createby":"UATSADM","updatedtm":"2025-06-23T19:11:23","updateby":"UATSADM"},"containsIllegalDate":false,"database":"postgres","pdkGroup":"io.tapdata","pdkId":"postgres","pdkVersion":"1.0-SNAPSHOT","referenceTime":1750659368171,"schema":"public","tableId":"refreshtoken","time":1750659368860,"type":300}
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:166)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:497)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	... 94 more
Caused by: java.sql.BatchUpdateException: 将截断字符串或二进制数据。
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2101)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:152)
	... 99 more

[TRACE] 2025-06-23 14:16:13.884 - [任务 23][sqlserver_ad] - Job suspend in error handle 
[TRACE] 2025-06-23 14:16:13.988 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] running status set to false 
[TRACE] 2025-06-23 14:16:14.172 - [任务 23][local_pg] - Incremental sync completed 
[TRACE] 2025-06-23 14:16:14.175 - [任务 23][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750659356090 
[TRACE] 2025-06-23 14:16:14.175 - [任务 23][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750659356090 
[TRACE] 2025-06-23 14:16:14.175 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] schema data cleaned 
[TRACE] 2025-06-23 14:16:14.176 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] monitor closed 
[TRACE] 2025-06-23 14:16:14.176 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] close complete, cost 188 ms 
[TRACE] 2025-06-23 14:16:14.176 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] running status set to false 
[TRACE] 2025-06-23 14:16:14.192 - [任务 23][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750659356123 
[TRACE] 2025-06-23 14:16:14.192 - [任务 23][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750659356123 
[TRACE] 2025-06-23 14:16:14.192 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] schema data cleaned 
[TRACE] 2025-06-23 14:16:14.193 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] monitor closed 
[TRACE] 2025-06-23 14:16:14.193 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] close complete, cost 16 ms 
[INFO ] 2025-06-23 14:16:18.463 - [任务 23] - Task [任务 23] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-23 14:16:23.317 - [任务 23] - Task [任务 23] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-23 14:16:23.521 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 14:16:24.326 - [任务 23] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@1c521ab8 
[TRACE] 2025-06-23 14:16:24.327 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@64637240 
[TRACE] 2025-06-23 14:16:24.445 - [任务 23] - Stop task milestones: 6858c7f8783fb251d8b8dadf(任务 23)  
[TRACE] 2025-06-23 14:16:24.445 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-06-23 14:16:24.445 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 14:16:24.445 - [任务 23] - Task stopped. 
[TRACE] 2025-06-23 14:16:24.475 - [任务 23] - Remove memory task client succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 14:16:24.475 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 14:17:38.308 - [任务 23] - Task initialization... 
[TRACE] 2025-06-23 14:17:38.311 - [任务 23] - Start task milestones: 6858c7f8783fb251d8b8dadf(任务 23) 
[INFO ] 2025-06-23 14:17:38.451 - [任务 23] - Loading table structure completed 
[TRACE] 2025-06-23 14:17:38.451 - [任务 23] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-23 14:17:38.502 - [任务 23] - The engine receives 任务 23 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-23 14:17:38.502 - [任务 23] - Task started 
[TRACE] 2025-06-23 14:17:38.514 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] start preload schema,table counts: 1 
[TRACE] 2025-06-23 14:17:38.514 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] start preload schema,table counts: 1 
[TRACE] 2025-06-23 14:17:38.514 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-23 14:17:38.514 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] preload schema finished, cost 0 ms 
[INFO ] 2025-06-23 14:17:38.716 - [任务 23][local_pg] - Enable partition table support for source database 
[INFO ] 2025-06-23 14:17:38.917 - [任务 23][local_pg] - Source connector(local_pg) initialization completed 
[TRACE] 2025-06-23 14:17:38.917 - [任务 23][local_pg] - Source node "local_pg" read batch size: 100 
[TRACE] 2025-06-23 14:17:38.917 - [任务 23][local_pg] - Source node "local_pg" event queue capacity: 200 
[TRACE] 2025-06-23 14:17:38.917 - [任务 23][local_pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-23 14:17:38.944 - [任务 23][local_pg] - Postgres specified time start increment is not supported except walminer, use the current time as the start increment 
[WARN ] 2025-06-23 14:17:38.944 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 14:17:38.955 - [任务 23][local_pg] - new logical replication slot created, slotName:tapdata_cdc_ed2d4252_e9db_4f6e_9a64_02e8b78ba7f7 
[INFO ] 2025-06-23 14:17:38.955 - [任务 23][local_pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 14:17:39.001 - [任务 23][local_pg] - Batch read completed. 
[TRACE] 2025-06-23 14:17:39.001 - [任务 23][local_pg] - Incremental sync starting... 
[TRACE] 2025-06-23 14:17:39.001 - [任务 23][local_pg] - Initial sync completed 
[TRACE] 2025-06-23 14:17:39.002 - [任务 23][local_pg] - Starting stream read, table list: [refreshtoken], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-23 14:17:39.002 - [任务 23][local_pg] - Starting incremental sync using database log parser 
[WARN ] 2025-06-23 14:17:39.005 - [任务 23][local_pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-23 14:17:39.005 - [任务 23][local_pg] - Using an existing logical replication slot, slotName:tapdata_cdc_ed2d4252_e9db_4f6e_9a64_02e8b78ba7f7 
[TRACE] 2025-06-23 14:17:39.152 - [任务 23][local_pg] - Connector PostgreSQL incremental start succeed, tables: [refreshtoken], data change syncing 
[INFO ] 2025-06-23 14:17:39.152 - [任务 23][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-23 14:17:39.152 - [任务 23][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-23 14:17:39.152 - [任务 23][sqlserver_ad] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-23 14:17:39.164 - [任务 23][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-23 14:17:39.365 - [任务 23][sqlserver_ad] - The table refreshtoken has already exist. 
[TRACE] 2025-06-23 17:19:18.911 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] running status set to false 
[TRACE] 2025-06-23 17:19:19.248 - [任务 23][local_pg] - Incremental sync completed 
[TRACE] 2025-06-23 17:19:19.254 - [任务 23][local_pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750659458777 
[TRACE] 2025-06-23 17:19:19.254 - [任务 23][local_pg] - PDK connector node released: HazelcastSourcePdkDataNode_65b109a1-e801-4111-b351-91c82e7846cd_1750659458777 
[TRACE] 2025-06-23 17:19:19.255 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] schema data cleaned 
[TRACE] 2025-06-23 17:19:19.255 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] monitor closed 
[TRACE] 2025-06-23 17:19:19.256 - [任务 23][local_pg] - Node local_pg[65b109a1-e801-4111-b351-91c82e7846cd] close complete, cost 346 ms 
[TRACE] 2025-06-23 17:19:19.256 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] running status set to false 
[TRACE] 2025-06-23 17:19:19.266 - [任务 23][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750659458810 
[TRACE] 2025-06-23 17:19:19.266 - [任务 23][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_998034a3-f84c-4ce2-aa35-a0658ecdc51b_1750659458810 
[TRACE] 2025-06-23 17:19:19.267 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] schema data cleaned 
[TRACE] 2025-06-23 17:19:19.267 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] monitor closed 
[TRACE] 2025-06-23 17:19:19.267 - [任务 23][sqlserver_ad] - Node sqlserver_ad[998034a3-f84c-4ce2-aa35-a0658ecdc51b] close complete, cost 11 ms 
[TRACE] 2025-06-23 17:19:28.790 - [任务 23] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-23 17:19:29.710 - [任务 23] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@37f32429 
[TRACE] 2025-06-23 17:19:29.711 - [任务 23] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@60e797d4 
[TRACE] 2025-06-23 17:19:29.833 - [任务 23] - Stop task milestones: 6858c7f8783fb251d8b8dadf(任务 23)  
[TRACE] 2025-06-23 17:19:29.833 - [任务 23] - Stopped task aspect(s) 
[TRACE] 2025-06-23 17:19:29.833 - [任务 23] - Snapshot order controller have been removed 
[INFO ] 2025-06-23 17:19:29.834 - [任务 23] - Task stopped. 
[TRACE] 2025-06-23 17:19:29.884 - [任务 23] - Remove memory task client succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
[TRACE] 2025-06-23 17:19:29.886 - [任务 23] - Destroy memory task client cache succeed, task: 任务 23[6858c7f8783fb251d8b8dadf] 
