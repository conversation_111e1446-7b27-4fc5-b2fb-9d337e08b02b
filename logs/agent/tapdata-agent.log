[WARN ] 2025-06-25 00:11:48.973  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1044624 ms
[INFO ] 2025-06-25 00:11:49.928  [hz.zed_flow_engine.cached.thread-9] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-24 23:54:20.311 to 2025-06-25 00:11:49.927 since last heartbeat (+1044616 ms)
[WARN ] 2025-06-25 00:11:49.928  [hz.zed_flow_engine.cached.thread-9] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1044616 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 00:28:04.574  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 970608 ms
[INFO ] 2025-06-25 00:28:04.785  [WebSocketClient-AsyncIO-12] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 00:28:05.529  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 00:11:49.927 to 2025-06-25 00:28:05.528 since last heartbeat (+970601 ms)
[WARN ] 2025-06-25 00:28:05.529  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 970601 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 00:28:08.388  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[WARN ] 2025-06-25 00:56:55.197  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1725624 ms
[INFO ] 2025-06-25 00:56:56.152  [hz.zed_flow_engine.cached.thread-9] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 00:28:05.528 to 2025-06-25 00:56:56.151 since last heartbeat (+1725623 ms)
[WARN ] 2025-06-25 00:56:56.153  [hz.zed_flow_engine.cached.thread-9] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1725623 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 01:14:44.147  [WebSocketClient-AsyncIO-12] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 01:14:44.147  [WebSocketClient-AsyncIO-12] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 01:14:44.797  [hz.zed_flow_engine.cached.thread-8] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 00:56:56.151 to 2025-06-25 01:14:44.797 since last heartbeat (+1063646 ms)
[WARN ] 2025-06-25 01:14:44.798  [hz.zed_flow_engine.cached.thread-8] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1063646 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 01:14:44.841  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1063644 ms
[INFO ] 2025-06-25 01:14:47.032  [nioEventLoopGroup-9-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-25 01:14:47.033  [nioEventLoopGroup-9-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-25 01:14:47.033  [nioEventLoopGroup-9-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-25 01:14:47.340  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-25 01:14:47.353  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzMjQyNDYxODE4Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lX2VmODY5NGRhYzNhMTQxYmVhOWRkZDBhZWRmMDNjNThkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDc4NTMxNywiaWF0IjoxNzUwNzg1Mjg3fQ.LStKTODhI5bF4HdDnZPX3SHusvggJ0aRRjwne9Auf9c]
[INFO ] 2025-06-25 01:14:47.356  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/e9df73dacf765ef931f43a68f137401f wsPort 8246]
[INFO ] 2025-06-25 01:14:47.362  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-25 01:14:47.363  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzMjQyNDYxODE4Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lX2VmODY5NGRhYzNhMTQxYmVhOWRkZDBhZWRmMDNjNThkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDc4NTMxNywiaWF0IjoxNzUwNzg1Mjg3fQ.LStKTODhI5bF4HdDnZPX3SHusvggJ0aRRjwne9Auf9c]
[INFO ] 2025-06-25 01:14:47.364  [nioEventLoopGroup-11-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-25 01:14:47.364  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 01:14:47.776  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 01:14:47.777  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 01:14:49.061  [WebSocketClient-AsyncIO-1] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 01:31:22.426  [hz.zed_flow_engine.cached.thread-4] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 01:14:44.797 to 2025-06-25 01:31:22.426 since last heartbeat (+992629 ms)
[WARN ] 2025-06-25 01:31:22.427  [hz.zed_flow_engine.cached.thread-4] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 992629 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 01:31:22.472  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 992637 ms
[INFO ] 2025-06-25 01:31:26.512  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-25 01:31:26.513  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[WARN ] 2025-06-25 01:47:03.120  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 935649 ms
[WARN ] 2025-06-25 01:47:03.121  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 7645265 ms
[INFO ] 2025-06-25 01:47:03.121  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 01:31:22.426 to 2025-06-25 01:47:03.121 since last heartbeat (+935695 ms)
[WARN ] 2025-06-25 01:47:03.121  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 935695 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 01:47:06.177  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[INFO ] 2025-06-25 01:47:07.340  [WebSocketClient-AsyncIO-1] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 01:47:07.340  [WebSocketClient-AsyncIO-1] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[WARN ] 2025-06-25 02:02:52.801  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 943690 ms
[INFO ] 2025-06-25 02:02:56.033  [WebSocketClient-AsyncIO-2] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 02:02:56.761  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 01:47:08.070 to 2025-06-25 02:02:56.760 since last heartbeat (+943690 ms)
[WARN ] 2025-06-25 02:02:56.761  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 943690 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 02:19:33.418  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 995614 ms
[INFO ] 2025-06-25 02:19:35.602  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 02:19:35.603  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 02:19:35.615  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[INFO ] 2025-06-25 02:19:37.376  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 02:02:56.760 to 2025-06-25 02:19:37.376 since last heartbeat (+995616 ms)
[WARN ] 2025-06-25 02:19:37.377  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 995616 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 02:36:59.086  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1040663 ms
[INFO ] 2025-06-25 02:37:02.246  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 02:37:02.247  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 02:37:02.967  [hz.zed_flow_engine.cached.thread-13] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 02:19:37.376 to 2025-06-25 02:37:02.966 since last heartbeat (+1040590 ms)
[WARN ] 2025-06-25 02:37:02.967  [hz.zed_flow_engine.cached.thread-13] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1040590 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 02:44:09.627  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 424622 ms
[INFO ] 2025-06-25 02:44:09.814  [nioEventLoopGroup-11-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-25 02:44:09.814  [nioEventLoopGroup-11-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-25 02:44:09.814  [nioEventLoopGroup-11-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-25 02:44:10.121  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-25 02:44:10.133  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzMjQyNDYxODE4Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lX2VmODY5NGRhYzNhMTQxYmVhOWRkZDBhZWRmMDNjNThkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDc5MDY4MCwiaWF0IjoxNzUwNzkwNjUwfQ.C9i7m_LsmfByhZqtOotn00KQp-1XKRBArgReSvTqTHw]
[INFO ] 2025-06-25 02:44:10.135  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/e9df73dacf765ef931f43a68f137401f wsPort 8246]
[INFO ] 2025-06-25 02:44:10.141  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-25 02:44:10.141  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzMjQyNDYxODE4Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lX2VmODY5NGRhYzNhMTQxYmVhOWRkZDBhZWRmMDNjNThkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDc5MDY4MCwiaWF0IjoxNzUwNzkwNjUwfQ.C9i7m_LsmfByhZqtOotn00KQp-1XKRBArgReSvTqTHw]
[INFO ] 2025-06-25 02:44:10.143  [nioEventLoopGroup-13-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-25 02:44:10.143  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 02:44:10.936  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 02:44:10.936  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 02:44:11.864  [WebSocketClient-AsyncIO-4] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 02:44:12.586  [hz.zed_flow_engine.cached.thread-2] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 02:37:02.966 to 2025-06-25 02:44:12.586 since last heartbeat (+424620 ms)
[WARN ] 2025-06-25 02:44:12.587  [hz.zed_flow_engine.cached.thread-2] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 424620 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 03:01:58.428  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1057797 ms
[INFO ] 2025-06-25 03:01:59.573  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[INFO ] 2025-06-25 03:01:59.669  [WebSocketClient-AsyncIO-4] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 03:01:59.669  [WebSocketClient-AsyncIO-4] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 03:02:00.386  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 02:44:17.585 to 2025-06-25 03:02:00.385 since last heartbeat (+1057800 ms)
[WARN ] 2025-06-25 03:02:00.386  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1057800 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 03:02:09.675  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 03:02:09.676  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 03:19:41.217  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 03:02:15.387 to 2025-06-25 03:19:41.216 since last heartbeat (+1040829 ms)
[WARN ] 2025-06-25 03:19:41.217  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1040829 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 03:19:41.260  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1040832 ms
[INFO ] 2025-06-25 03:19:45.310  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-25 03:19:45.310  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 03:19:55.521  [WebSocketClient-AsyncIO-9] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 03:20:00.859  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[WARN ] 2025-06-25 03:20:01.259  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 5503138 ms
[INFO ] 2025-06-25 03:20:05.523  [WebSocketClient-AsyncIO-9] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 03:20:05.523  [WebSocketClient-AsyncIO-9] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 03:20:10.965  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 03:20:10.966  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-25 03:36:44.045  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 966788 ms
[INFO ] 2025-06-25 03:36:44.976  [nioEventLoopGroup-13-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-25 03:36:44.976  [nioEventLoopGroup-13-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-25 03:36:44.976  [nioEventLoopGroup-13-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-25 03:36:45.288  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-25 03:36:45.296  [ForkJoinPool.commonPool-worker-16] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzMjQyNDYxODE4Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lX2VmODY5NGRhYzNhMTQxYmVhOWRkZDBhZWRmMDNjNThkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDc5MzgzNSwiaWF0IjoxNzUwNzkzODA1fQ.lisikHoAlNUGRffC7vqGbp_K5H-5Aatao_cozkneb_A]
[INFO ] 2025-06-25 03:36:45.298  [ForkJoinPool.commonPool-worker-16] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/e9df73dacf765ef931f43a68f137401f wsPort 8246]
[INFO ] 2025-06-25 03:36:45.304  [ForkJoinPool.commonPool-worker-16] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-25 03:36:45.304  [ForkJoinPool.commonPool-worker-16] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzMjQyNDYxODE4Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lX2VmODY5NGRhYzNhMTQxYmVhOWRkZDBhZWRmMDNjNThkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDc5MzgzNSwiaWF0IjoxNzUwNzkzODA1fQ.lisikHoAlNUGRffC7vqGbp_K5H-5Aatao_cozkneb_A]
[INFO ] 2025-06-25 03:36:45.306  [nioEventLoopGroup-15-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-25 03:36:45.306  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 03:36:47.067  [WebSocketClient-AsyncIO-2] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 03:36:47.744  [hz.zed_flow_engine.cached.thread-12] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 03:20:36.215 to 2025-06-25 03:36:47.744 since last heartbeat (+966529 ms)
[WARN ] 2025-06-25 03:36:47.745  [hz.zed_flow_engine.cached.thread-12] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 966529 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 03:36:47.852  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[INFO ] 2025-06-25 03:36:57.077  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 03:36:57.079  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 03:36:57.967  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 03:36:57.968  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-25 03:37:22.784  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 966525 ms
[WARN ] 2025-06-25 03:55:16.106  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1006322 ms
[INFO ] 2025-06-25 03:55:18.442  [WebSocketClient-AsyncIO-119] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 03:55:19.060  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 03:38:27.751 to 2025-06-25 03:55:19.060 since last heartbeat (+1006309 ms)
[WARN ] 2025-06-25 03:55:19.061  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1006309 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 04:12:12.633  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1010531 ms
[INFO ] 2025-06-25 04:12:14.594  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 03:55:19.060 to 2025-06-25 04:12:14.593 since last heartbeat (+1010533 ms)
[WARN ] 2025-06-25 04:12:14.595  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1010533 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 04:12:14.637  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 2016853 ms
[INFO ] 2025-06-25 04:12:15.946  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[WARN ] 2025-06-25 04:28:21.201  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 963561 ms
[INFO ] 2025-06-25 04:28:22.544  [WebSocketClient-AsyncIO-119] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 04:28:22.545  [WebSocketClient-AsyncIO-119] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 04:28:23.156  [hz.zed_flow_engine.cached.thread-12] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 04:12:14.593 to 2025-06-25 04:28:23.156 since last heartbeat (+963563 ms)
[WARN ] 2025-06-25 04:28:23.156  [hz.zed_flow_engine.cached.thread-12] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 963563 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 04:45:11.999  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1005797 ms
[INFO ] 2025-06-25 04:45:13.087  [WebSocketClient-AsyncIO-2] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 04:45:13.696  [hz.zed_flow_engine.cached.thread-2] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 04:28:23.156 to 2025-06-25 04:45:13.695 since last heartbeat (+1005539 ms)
[WARN ] 2025-06-25 04:45:13.696  [hz.zed_flow_engine.cached.thread-2] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1005539 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 04:45:15.162  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 04:45:15.162  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 04:45:15.174  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[WARN ] 2025-06-25 05:00:57.321  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 939581 ms
[INFO ] 2025-06-25 05:00:57.391  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-25 05:00:57.391  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 05:00:58.282  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 04:45:13.695 to 2025-06-25 05:00:58.282 since last heartbeat (+939587 ms)
[WARN ] 2025-06-25 05:00:58.283  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 939587 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 05:18:32.975  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1050651 ms
[INFO ] 2025-06-25 05:18:33.284  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 05:18:33.284  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 05:18:33.883  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 05:00:58.282 to 2025-06-25 05:18:33.883 since last heartbeat (+1050601 ms)
[WARN ] 2025-06-25 05:18:33.884  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1050601 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 05:18:35.477  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 05:18:35.478  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 05:35:32.964  [WebSocketClient-AsyncIO-4] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 05:35:33.432  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 05:18:33.883 to 2025-06-25 05:35:33.431 since last heartbeat (+1014548 ms)
[WARN ] 2025-06-25 05:35:33.432  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1014548 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 05:35:33.476  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1014554 ms
[INFO ] 2025-06-25 05:52:33.032  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 05:35:33.431 to 2025-06-25 05:52:33.032 since last heartbeat (+1014601 ms)
[WARN ] 2025-06-25 05:52:33.066  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1014601 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 05:52:33.071  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1014596 ms
[INFO ] 2025-06-25 05:52:34.738  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[INFO ] 2025-06-25 05:52:37.672  [WebSocketClient-AsyncIO-4] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 05:52:37.672  [WebSocketClient-AsyncIO-4] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[WARN ] 2025-06-25 06:10:30.670  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1071597 ms
[INFO ] 2025-06-25 06:10:34.162  [WebSocketClient-AsyncIO-8] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 06:10:34.627  [hz.zed_flow_engine.cached.thread-2] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 05:52:38.028 to 2025-06-25 06:10:34.626 since last heartbeat (+1071598 ms)
[WARN ] 2025-06-25 06:10:34.628  [hz.zed_flow_engine.cached.thread-2] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1071598 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 06:26:37.227  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 961557 ms
[INFO ] 2025-06-25 06:26:38.008  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 06:26:38.009  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 06:26:38.020  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[INFO ] 2025-06-25 06:26:41.183  [hz.zed_flow_engine.cached.thread-16] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 06:10:34.626 to 2025-06-25 06:26:41.183 since last heartbeat (+961557 ms)
[WARN ] 2025-06-25 06:26:41.184  [hz.zed_flow_engine.cached.thread-16] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 961557 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 06:41:38.922  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 896694 ms
[INFO ] 2025-06-25 06:41:42.287  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 06:41:42.287  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 06:41:42.746  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 06:26:41.183 to 2025-06-25 06:41:42.745 since last heartbeat (+896562 ms)
[WARN ] 2025-06-25 06:41:42.746  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 896562 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 06:57:09.204  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 06:57:09.204  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-25 06:57:09.303  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 924514 ms
[INFO ] 2025-06-25 06:57:11.803  [WebSocketClient-AsyncIO-3] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 06:57:12.260  [hz.zed_flow_engine.cached.thread-12] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 06:41:42.745 to 2025-06-25 06:57:12.260 since last heartbeat (+924515 ms)
[WARN ] 2025-06-25 06:57:12.260  [hz.zed_flow_engine.cached.thread-12] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 924515 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 07:15:04.899  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1070592 ms
[INFO ] 2025-06-25 07:15:07.762  [hz.zed_flow_engine.cached.thread-6] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 06:57:12.260 to 2025-06-25 07:15:07.761 since last heartbeat (+1070501 ms)
[WARN ] 2025-06-25 07:15:07.762  [hz.zed_flow_engine.cached.thread-6] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1070501 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 07:15:09.823  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[WARN ] 2025-06-25 07:30:19.368  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 908563 ms
[INFO ] 2025-06-25 07:30:20.867  [WebSocketClient-AsyncIO-3] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 07:30:20.867  [WebSocketClient-AsyncIO-3] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 07:30:21.321  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 07:15:07.761 to 2025-06-25 07:30:21.321 since last heartbeat (+908560 ms)
[WARN ] 2025-06-25 07:30:21.321  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 908560 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 07:46:21.916  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 957550 ms
[INFO ] 2025-06-25 07:46:23.424  [WebSocketClient-AsyncIO-119] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 07:46:23.872  [hz.zed_flow_engine.cached.thread-6] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 07:30:21.321 to 2025-06-25 07:46:23.871 since last heartbeat (+957550 ms)
[WARN ] 2025-06-25 07:46:23.872  [hz.zed_flow_engine.cached.thread-6] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 957550 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 07:46:26.043  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 07:46:26.043  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 07:46:26.051  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[WARN ] 2025-06-25 08:03:55.519  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1047611 ms
[INFO ] 2025-06-25 08:03:55.589  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-25 08:03:55.589  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 08:03:56.477  [hz.zed_flow_engine.cached.thread-8] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 07:46:23.871 to 2025-06-25 08:03:56.476 since last heartbeat (+1047605 ms)
[WARN ] 2025-06-25 08:03:56.477  [hz.zed_flow_engine.cached.thread-8] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1047605 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 08:03:56.518  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 13826881 ms
[WARN ] 2025-06-25 08:19:47.924  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 947404 ms
[INFO ] 2025-06-25 08:19:48.440  [WebSocketClient-AsyncIO-119] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 08:19:48.440  [WebSocketClient-AsyncIO-119] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 08:19:48.884  [hz.zed_flow_engine.cached.thread-4] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 08:03:56.476 to 2025-06-25 08:19:48.883 since last heartbeat (+947407 ms)
[WARN ] 2025-06-25 08:19:48.884  [hz.zed_flow_engine.cached.thread-4] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 947407 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 08:19:51.181  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 08:19:51.181  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 08:33:25.246  [WebSocketClient-AsyncIO-11] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 08:33:25.685  [hz.zed_flow_engine.cached.thread-8] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 08:19:48.883 to 2025-06-25 08:33:25.685 since last heartbeat (+811802 ms)
[WARN ] 2025-06-25 08:33:25.686  [hz.zed_flow_engine.cached.thread-8] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 811802 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 08:33:25.726  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 811798 ms
[INFO ] 2025-06-25 08:33:33.114  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[WARN ] 2025-06-25 08:35:04.761  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 90036 ms
[INFO ] 2025-06-25 08:35:05.201  [WebSocketClient-AsyncIO-11] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 08:35:05.202  [WebSocketClient-AsyncIO-11] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[WARN ] 2025-06-25 08:35:05.634  [hz.zed_flow_engine.cached.thread-4] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 89949 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 08:35:10.200  [WebSocketClient-AsyncIO-1] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 08:35:13.169  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 08:35:13.171  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 08:35:13.185  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[INFO ] 2025-06-25 08:35:20.209  [WebSocketClient-AsyncIO-1] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 08:35:20.210  [WebSocketClient-AsyncIO-1] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 08:35:23.300  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 08:35:23.300  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-25 08:36:14.729  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 40049 ms
[WARN ] 2025-06-25 08:36:15.457  [hz.zed_flow_engine.cached.thread-8] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 39824 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 08:36:17.684  [nioEventLoopGroup-15-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-25 08:36:17.684  [nioEventLoopGroup-15-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-25 08:36:17.684  [nioEventLoopGroup-15-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-25 08:36:17.994  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-25 08:36:18.010  [ForkJoinPool.commonPool-worker-17] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzMjQyNDYxODE4Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lX2VmODY5NGRhYzNhMTQxYmVhOWRkZDBhZWRmMDNjNThkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDgxMTgwOCwiaWF0IjoxNzUwODExNzc4fQ.vIySBtOJqnALgu_fF4--lAUaCyxr-gcp_5LQ7v2kXV4]
[INFO ] 2025-06-25 08:36:18.013  [ForkJoinPool.commonPool-worker-17] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/e9df73dacf765ef931f43a68f137401f wsPort 8246]
[INFO ] 2025-06-25 08:36:18.019  [ForkJoinPool.commonPool-worker-17] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-25 08:36:18.019  [ForkJoinPool.commonPool-worker-17] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIzMjQyNDYxODE4Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lX2VmODY5NGRhYzNhMTQxYmVhOWRkZDBhZWRmMDNjNThkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDgxMTgwOCwiaWF0IjoxNzUwODExNzc4fQ.vIySBtOJqnALgu_fF4--lAUaCyxr-gcp_5LQ7v2kXV4]
[INFO ] 2025-06-25 08:36:18.021  [nioEventLoopGroup-17-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-25 08:36:18.021  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 08:36:20.037  [WebSocketClient-AsyncIO-11] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 08:36:23.345  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[INFO ] 2025-06-25 08:36:30.040  [WebSocketClient-AsyncIO-11] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 08:36:30.041  [WebSocketClient-AsyncIO-11] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 08:36:33.457  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 08:36:33.457  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-25 08:36:40.495  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 1888977 ms
[WARN ] 2025-06-25 08:53:59.073  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1021578 ms
[INFO ] 2025-06-25 08:54:02.027  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 08:36:55.455 to 2025-06-25 08:54:02.025 since last heartbeat (+1021570 ms)
[WARN ] 2025-06-25 08:54:02.027  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1021570 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 09:10:00.816  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 956745 ms
[INFO ] 2025-06-25 09:10:03.260  [WebSocketClient-AsyncIO-2] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb, status code: 1000, reason: null
[INFO ] 2025-06-25 09:10:03.653  [hz.zed_flow_engine.cached.thread-8] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-25 08:54:02.025 to 2025-06-25 09:10:03.652 since last heartbeat (+956627 ms)
[WARN ] 2025-06-25 09:10:03.653  [hz.zed_flow_engine.cached.thread-8] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 956627 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-25 09:10:46.217  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 39525 ms
[WARN ] 2025-06-25 09:10:48.174  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 39522 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-25 09:10:51.633  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=41e297edf5334cf7817c1ab770d1e9bbc76f3c0b512d457dbe34b0e3e2bcc6c0&singletonTag=aa3c35c1-f9d4-41fa-93c7-c8aec0c318cb
[INFO ] 2025-06-25 09:10:52.787  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-25 09:10:52.788  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-25 09:11:05.748  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-25 09:11:05.960  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-25 09:11:06.168  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-25 09:11:06.168  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 09:11:09.226  [hz.zed_flow_engine.HealthMonitor] HealthMonitor - [**********]:5701 [dev] [5.5.0] processors=12, physical.memory.total=32.0G, physical.memory.free=148.0M, swap.space.total=5.0G, swap.space.free=891.5M, heap.memory.used=203.9M, heap.memory.free=172.1M, heap.memory.total=376.0M, heap.memory.max=8.0G, heap.memory.used/total=54.22%, heap.memory.used/max=2.49%, minor.gc.count=50, minor.gc.time=507ms, major.gc.count=0, major.gc.time=0ms, load.process=8.12%, load.system=66.67%, load.systemAverage=9.61, thread.count=244, thread.peakCount=262, cluster.timeDiff=-49412893, event.q.size=0, executor.q.async.size=0, executor.q.client.size=0, executor.q.client.query.size=0, executor.q.client.blocking.size=0, executor.q.query.size=0, executor.q.scheduled.size=0, executor.q.io.size=0, executor.q.system.size=0, executor.q.operations.size=0, executor.q.priorityOperation.size=0, operations.completed.count=1, executor.q.mapLoad.size=0, executor.q.mapLoadAllKeys.size=0, executor.q.cluster.size=0, executor.q.response.size=0, operations.running.count=0, operations.pending.invocations.percentage=0.00%, operations.pending.invocations.count=0, proxy.count=0, clientEndpoint.count=0, connection.active.count=0, client.connection.count=0, connection.count=0
[WARN ] 2025-06-25 09:11:37.078  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 2021583 ms
[INFO ] 2025-06-25 09:17:06.173  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 09:17:06.173  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-25 09:22:06.192  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-25 09:22:06.192  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 09:27:06.212  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-25 09:27:06.212  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-25 09:32:06.223  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-25 09:32:06.223  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
