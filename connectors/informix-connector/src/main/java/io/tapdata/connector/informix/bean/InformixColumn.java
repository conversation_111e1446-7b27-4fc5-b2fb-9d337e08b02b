package io.tapdata.connector.informix.bean;

import io.tapdata.common.CommonColumn;
import io.tapdata.connector.informix.constant.InformixDateTimeEnum;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.utils.DataMap;

/**
 * <AUTHOR>
 * @date 2022/4/20
 */
public class InformixColumn extends CommonColumn {

    public InformixColumn(DataMap dataMap) {
        this.columnName = dataMap.getString("columnName");
        this.dataType = getDataType(dataMap); //'dataType' with precision and scale (postgres has its function)
//        this.dataType = dataMap.getString("data_type"); //'data_type' without precision or scale
        this.nullable = dataMap.getString("nullable");
//        this.remarks = dataMap.getString("comments");
        //create table in target has no need to set default value
        this.columnDefaultValue = null;
//        this.columnDefaultValue = getDefaultValue(dataMap.getString("column_default"));
    }

    @Override
    public TapField getTapField() {
        return new TapField(this.columnName, this.dataType).nullable(this.isNullable()).
                defaultValue(columnDefaultValue).comment(this.remarks);
    }

    private String getDataType(DataMap dataMap) {
        String originType = dataMap.getString("dataType").trim();
        if (originType.startsWith("NVCHAR")) {
            return originType.replace("NVCHAR", "NVARCHAR");
        }
        switch (originType) {
            case "CHAR":
            case "VARCHAR":
            case "NVARCHAR":
            case "NCHAR":
            case "LVARCHAR":
                return originType + "(" + dataMap.getString("dataLength") + ")";
            case "BOOL":
                return "BOOLEAN";
            case "FLOAT":
                return "FLOAT(" + dataMap.getString("dataLength") + ")";
            case "DATETIME": {
                int colLength = Integer.parseInt(dataMap.getString("dataLength"));
                String from;
                int doubleLength;
                int startNumber = 2;
                switch (colLength % 257) {
                    case 32:
                        from = "MONTH";
                        doubleLength = 4;
                        break;
                    case 66:
                        from = "DAY";
                        doubleLength = 3;
                        break;
                    case 100:
                        from = "HOUR";
                        doubleLength = 2;
                        break;
                    case 134:
                        from = "MINUTE";
                        doubleLength = 1;
                        break;
                    case 168:
                        from = "SECOND";
                        doubleLength = 0;
                        break;
                    default:
                        from = "YEAR";
                        startNumber = 3;
                        doubleLength = 5;
                        break;
                }
                String to = InformixDateTimeEnum.fromLevel(startNumber + doubleLength * 2 - (colLength / 257)).getTypeName();
                return "DATETIME " + from + " TO " + to;
            }
            case "DECIMAL": {
                int colLength = Integer.parseInt(dataMap.getString("dataLength"));
                String dataType = "DECIMAL(" + (colLength / 256);
                if (colLength % 256 == 255) {
                    dataType += ")";
                } else {
                    dataType += "," + (colLength % 256) + ")";
                }
                return dataType;
            }
            default:
                return originType;
        }
    }

}
