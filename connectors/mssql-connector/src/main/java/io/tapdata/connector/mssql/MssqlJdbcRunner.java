package io.tapdata.connector.mssql;

import io.tapdata.common.JdbcContext;
import io.tapdata.connector.mssql.config.MssqlConfig;
import io.tapdata.connector.mssql.exception.MssqlExceptionCollector;
import io.tapdata.entity.logger.TapLogger;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class MssqlJdbcRunner extends JdbcContext {
    private static final String TAG = MssqlJdbcRunner.class.getSimpleName();

    public MssqlJdbcRunner(MssqlConfig config) {
        super(config);
        exceptionCollector = new MssqlExceptionCollector();
    }

    @Override
    public Connection getConnection() throws SQLException {
        Connection connection = super.getConnection();
        // set autoCommit=true since mssql will throw deadlock error when processing
        // non-primary-key data; more detail
        connection.setAutoCommit(!Boolean.TRUE.equals(getConfig().getDoubleActive()));
        return connection;
    }

    @Override
    public String queryVersion() {
        AtomicReference<String> version = new AtomicReference<>("");
        try {
            queryWithNext(MS_VERSION, resultSet -> version.set(resultSet.getString(1)));
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return version.get();
    }

    @Override
    public Long queryTimestamp() throws SQLException {
        AtomicReference<Timestamp> currentTime = new AtomicReference<>();
        queryWithNext(MSSQL_CURRENT_TIME, resultSet -> currentTime.set(resultSet.getTimestamp(1)));
        return currentTime.get().getTime();
    }

    @Override
    protected String queryAllTablesSql(String schema, List<String> tableNames) {
        return String.format(MS_ALL_TABLE, MssqlMaker.escape(getConfig().getSchema(), "'"), EmptyKit.isEmpty(tableNames) ? "" : "AND tbl.name IN ('" + tableNames.stream().map(v -> MssqlMaker.escape(v, "'")).collect(Collectors.joining("','")) + "')");
    }

    @Override
    protected String queryAllColumnsSql(String schema, List<String> tableNames) {
        return String.format(MS_ALL_COLUMN, MssqlMaker.escape(getConfig().getSchema(), "'"), EmptyKit.isEmpty(tableNames) ? "" : "AND tbl.name IN ('" + tableNames.stream().map(v -> MssqlMaker.escape(v, "'")).collect(Collectors.joining("','")) + "')");
    }

    @Override
    protected String queryAllForeignKeysSql(String schema, List<String> tableNames) {
        return String.format(MS_ALL_FOREIGN_KEY, getConfig().getSchema(), EmptyKit.isEmpty(tableNames) ? "" : " and o.name in ('" + tableNames.stream().map(v -> MssqlMaker.escape(v, "'")).collect(Collectors.joining("','")) + "')");
    }

    @Override
    protected String queryAllIndexesSql(String schema, List<String> tableNames) {
        return String.format(MS_ALL_INDEX, MssqlMaker.escape(getConfig().getSchema(), "'"), EmptyKit.isEmpty(tableNames) ? "" : "AND object_name(t_i.object_id) IN ('" + tableNames.stream().map(v -> MssqlMaker.escape(v, "'")).collect(Collectors.joining("','")) + "')");
    }

    public DataMap getTableInfo(String tableName) {
        DataMap dataMap = DataMap.create();
        try {
            query(String.format(TABLE_INFO, MssqlMaker.escape(tableName, "'")), resultSet -> {
                List<String> columnNames = DbKit.getColumnsFromResultSet(resultSet);
                while (resultSet.next()) {
                    dataMap.putAll(DbKit.getRowFromResultSet(resultSet, columnNames));
                }
            });

        } catch (Throwable e) {
            TapLogger.error(TAG, "Execute getTableInfo failed, error: " + e.getMessage(), e);
        }
        return dataMap;
    }

    public Long queryTimeZoneOffset() {
        AtomicReference<Long> timeZoneOffset = new AtomicReference<>();
        try {
            queryWithNext(TIME_ZONE_OFFSET, resultSet -> timeZoneOffset.set(resultSet.getLong(1)));
        } catch (SQLException e) {
            TapLogger.error(TAG, "Failed to get time zone offset, error: " + e.getMessage(), e);
        }
        return timeZoneOffset.get();
    }

    public static final String TIME_ZONE_OFFSET = "SELECT DATEPART(TZOFFSET, SYSDATETIMEOFFSET()) * 60 * 1000  AS TimeZoneOffset_Milliseconds";

    private final static String TABLE_INFO = "EXEC sp_spaceused '%s'";
    private final static String MS_VERSION = "SELECT @@VERSION";
    private final static String MS_ALL_TABLE = "" +
            "   SELECT tbl.name as tableName, \n" +
            "          ext.value as tableComment \n" +
            "     FROM sys.tables tbl \n" +
            "LEFT JOIN sys.extended_properties ext \n" +
            "       ON ext.major_id = tbl.object_id \n" +
            "      AND ext.minor_id = 0 \n" +
            "      AND ext.name = 'MS_Description' \n" +
            "    WHERE schema_name(tbl.schema_id) = '%s' \n" +
            "      AND tbl.is_ms_shipped = 0 \n" +
            "      %s \n" +
            "ORDER BY tableName";

    private final static String MS_ALL_COLUMN = "SELECT tbl.name AS tableName, \n" +
            "           col.name AS columnName, \n" +
            "           ext.value AS columnComment, \n" +
            "           col.column_id AS columnId, \n" +
            "           types.name AS dataType, \n" +
            "           col.max_length AS dataLength, \n" +
            "           col.precision AS dataPrecision, \n" +
            "           col.scale AS dataScale,\n" +
            "           col.is_nullable AS nullable, \n" +
            "           col.is_identity AS autoInc,\n" +
            "           icol.seed_value AS seedValue,\n" +
            "           icol.increment_value AS incrementValue,\n" +
            "           dc.definition columnDefault\n" +
            "      FROM sys.tables tbl \n" +
            " INNER JOIN sys.columns col \n" +
            "        ON tbl.object_id = col.object_id \n" +
            " LEFT JOIN sys.identity_columns icol\n" +
            "        ON icol.object_id = col.object_id\n" +
            "       AND icol.column_id = col.column_id" +
            " LEFT JOIN sys.default_constraints dc\n" +
            "        ON dc.object_id = col.default_object_id" +
            " LEFT JOIN sys.types types \n" +
            "        ON col.user_type_id = types.user_type_id \n" +
            " LEFT JOIN sys.extended_properties ext \n" +
            "        ON ext.major_id = col.object_id \n" +
            "       AND ext.minor_id = col.column_id \n" +
            "       AND ext.name = 'MS_Description' \n" +
            "     WHERE schema_name(tbl.schema_id) = '%s' \n" +
            "       %s \n" +
            "  ORDER BY tbl.name, col.column_id";

    private final static String MS_ALL_FOREIGN_KEY = "select\n" +
            "(select name from sys.sysobjects where id=fkc.constraint_object_id) constraintName,\n" +
            "o.name tableName,\n" +
            "(select name from sys.sysobjects where id=fkc.referenced_object_id) referencesTableName,\n" +
            "(select name from sys.syscolumns where id=fkc.parent_object_id and colid=fkc.parent_column_id) fk,\n" +
            "(select name from sys.syscolumns where id=fkc.referenced_object_id and colid=fkc.referenced_column_id) rfk,\n" +
            "fks.delete_referential_action_desc onDelete,\n" +
            "fks.update_referential_action_desc onUpdate\n" +
            "from sys.foreign_key_columns fkc\n" +
            "join sys.foreign_keys fks on (fks.referenced_object_id=fkc.referenced_object_id and fks.parent_object_id=fkc.parent_object_id)\n" +
            "join sys.sysobjects o on o.id=fkc.parent_object_id\n" +
            "join sys.sysusers u on u.uid=o.uid\n" +
            "where u.name='%s' %s";

    private final static String MS_ALL_INDEX = "SELECT t_i.Name indexName, \n" +
            "           t_i.type_desc indexType, \n" +
            "           IIF(t_i.is_primary_key = 'true', 1, 0) isPk, \n" +
            "           IIF(t_i.is_unique = 'true', 1, 0) isUnique, \n" +
            "           object_name(t_i.object_id) tableName, \n" +
            "           t_c.Name columnName, \n" +
            "           t_ic.index_column_id indexColumnId, \n" +
            "           CASE INDEXKEY_PROPERTY(t_ic.object_id, t_ic.index_id, t_ic.index_column_id, 'IsDescending') WHEN 1 THEN 0 WHEN 0 THEN 1 ELSE null END isAsc \n" +
            "      FROM sys.indexes t_i \n" +
            "INNER JOIN sys.index_columns t_ic \n" +
            "        ON t_i.object_id = t_ic.object_id \n" +
            "       AND t_i.index_id= t_ic.index_id \n" +
            "INNER JOIN sys.columns t_c \n" +
            "        ON t_c.object_id = t_ic.object_id \n" +
            "       AND t_c.column_id= t_ic.Column_id \n" +
            "     WHERE t_i.is_disabled = 0 \n" +
            "       AND object_schema_name(t_i.object_id) = '%s' \n" +
            "       %s \n" +
            "     ORDER BY t_i.index_id, t_ic.index_column_id";

    private final static String MSSQL_CURRENT_TIME = "SELECT SYSDATETIMEOFFSET()";
}
