package io.tapdata.connector.mssql.cdc;


import com.google.common.io.BaseEncoding;
import io.tapdata.common.ddl.DDLFactory;
import io.tapdata.common.ddl.ccj.CCJBaseDDLWrapper;
import io.tapdata.common.ddl.type.DDLParserType;
import io.tapdata.common.exception.ExceptionCollector;
import io.tapdata.connector.mssql.JdbcUtil;
import io.tapdata.connector.mssql.MssqlJdbcRunner;
import io.tapdata.connector.mssql.MssqlMaker;
import io.tapdata.connector.mssql.config.MssqlConfig;
import io.tapdata.connector.mssql.ddl.ccj.MssqlDDLWrapper;
import io.tapdata.connector.mssql.exception.MssqlExceptionCollector;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.ddl.TapDDLEvent;
import io.tapdata.entity.event.ddl.TapDDLUnknownEvent;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.exception.TapPdkOffsetOutOfLogEx;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import org.apache.commons.lang3.StringUtils;

import java.sql.Date;
import java.sql.*;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
public class MssqlCdcRunner {

    private static final String START_LSN_FIELD = "__$start_lsn";
    private static final String START_TIME_FIELD = "__$start_time";
    private static final String END_LSN_FIELD = "__$end_lsn";
    private static final String SEQ_VAL_FIELD = "__$seqval";
    private static final String UPDATE_MASK_FIELD = "__$update_mask";
    private static final String COMMAND_ID_FIELD = "__$command_id";
    private static final String OPERATION_FIELD = "__$operation";

    private static final int DELETE_STATUS = 1;
    private static final int INSERT_STATUS = 2;
    private static final int UPDATE_PREVIOUS_STATUS = 3;
    private static final int UPDATE_AFTER_STATUS = 4;

    public static Set<String> excludeField = new HashSet<>();

    static {
        excludeField.add(END_LSN_FIELD);
        excludeField.add(SEQ_VAL_FIELD);
        excludeField.add(UPDATE_MASK_FIELD);
        excludeField.add(COMMAND_ID_FIELD);
    }

    private final AtomicBoolean running = new AtomicBoolean(true);
    private final AtomicReference<Throwable> error = new AtomicReference<>();

    // keep the default value as 1.X
    private static final int cdcFetchSize = 500;
    // keep the default value as 1.X
    private static final int readCdcInterval = 500;
    private static final int readCdcDDLInterval = 5000;

    private final MssqlJdbcRunner jdbcRunner;
    private final Log tapLogger;
    private final MssqlConfig config;
    private int recordSize;
    private StreamReadConsumer consumer;
    private List<String> tables;
    private CdcOffset offset;
    private Long timeZoneOffset;
    private Connection connection;
    private Map<String, TableCaptureInstance> tableCdcCaptureInstance;
    private Map<String, PreparedStatement> tablesCdcWherePstmt;
    private Map<String, String> tablesCdcWherePstmtRawSQL;
    private ExecutorService ddlCheckThreadPool;
    private Map<String, List<TapEvent>> ddlCache;
    private BlockingQueue<String> tableQueue;
    private static final CCJBaseDDLWrapper.CCJDDLWrapperConfig DDL_WRAPPER_CONFIG = CCJBaseDDLWrapper.CCJDDLWrapperConfig.create().split("\""); //DDL parser config
    private DDLParserType ddlParserType; //DDL parser type
    protected KVReadOnlyMap<TapTable> tableMap; //pdk tableMap in streamRead
    private ExceptionCollector exceptionCollector;
    private boolean reMiner = false;


    public MssqlCdcRunner(MssqlJdbcRunner jdbcRunner, Log tapLogger) {
        this.jdbcRunner = jdbcRunner;
        this.tapLogger = tapLogger;
        this.config = ((MssqlConfig) jdbcRunner.getConfig()).log(tapLogger);
        this.exceptionCollector = new MssqlExceptionCollector();
    }

    public MssqlCdcRunner withTables(List<String> tables, KVReadOnlyMap<TapTable> tableMap) {
        this.tables = tables;
        if (config.getDoubleActive()) {
            this.tables.add("_tap_double_active");
        }
        this.tableMap = tableMap;
        return this;
    }

    public MssqlCdcRunner withOffset(CdcOffset offset) {
        this.offset = offset;
        return this;
    }

    public MssqlCdcRunner withTimeZoneOffset(Long timeZoneOffset) {
        this.timeZoneOffset = timeZoneOffset;
        return this;
    }

    public MssqlCdcRunner withConsumer(int recordSize, StreamReadConsumer consumer) {
        this.recordSize = recordSize;
        this.consumer = consumer;
        return this;
    }

    public void close() {
        running.set(false);
        TapSimplify.sleep(3000);
        if (EmptyKit.isNotNull(ddlCheckThreadPool)) {
            ddlCheckThreadPool.shutdown();
        }
    }

    public void init() throws Exception {
        tableCdcCaptureInstance = new ConcurrentHashMap<>(tables.size());
        tablesCdcWherePstmt = new ConcurrentHashMap<>(tables.size());
        tablesCdcWherePstmtRawSQL = new ConcurrentHashMap<>(tables.size());
        ddlCache = new ConcurrentHashMap<>(tables.size());
        tableQueue = new LinkedBlockingQueue<>(tables.size());
        tableQueue.addAll(tables);
        tables.forEach(v -> ddlCache.put(v, TapSimplify.list()));
        ddlParserType = DDLParserType.MSSQL_CCJ_SQL_PARSER;
        connection = jdbcRunner.getConnection();
        error.set(null);
        Set<String> cdcTables = getCdcTables(jdbcRunner);
        tapLogger.info("opened cdc tables: {}", cdcTables);
        tables.stream().filter(v -> !cdcTables.contains(v)).forEach(v -> Optional.ofNullable(tableMap.get(v)).ifPresent(this::buildCT));
        if (null == offset || EmptyKit.isEmpty(offset.getCurrentStartLSN())) {
            String currentLSN = SqlServerCdcHelper.getCurrentLSN(connection);
            offset = new CdcOffset();
            offset.setCurrentStartLSN(currentLSN);
        }
        if (null == offset.getDdlOffset()) {
            offset.setDdlOffset(EmptyKit.isNull(offset.getCurrentStartLSN()) ? null : BaseEncoding.base16().decode(offset.getCurrentStartLSN()));
        }
        Map<String, Object> tableOffset = offset.getTablesOffset();
        if (EmptyKit.isNotEmpty(tableOffset)) {
            reMiner = true;
        }
        for (String table : tables) {
            tableOffset.putIfAbsent(table, EmptyKit.isNull(offset.getCurrentStartLSN()) ? "" : offset.getCurrentStartLSN());
        }
        try {
            initDdlCheckThread();
            initCdcStatement();
        } catch (Throwable e) {
            releaseConnection(connection);
            throw new RuntimeException(e);
        }
    }

    private void initDdlCheckThread() {
        if (EmptyKit.isNull(ddlCheckThreadPool)) {
            ddlCheckThreadPool = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
        }
        ddlCheckThreadPool.submit(() -> {
            while (running.get() && null == error.get()) {
                TapSimplify.sleep(readCdcDDLInterval);

                boolean hasOffset = EmptyKit.isNotNull(offset) && EmptyKit.isNotNull(offset.getDdlOffset());
                try (PreparedStatement ddlPstmt = hasOffset
                        ? connection.prepareStatement("SELECT * FROM cdc.ddl_history where ddl_lsn > ?")
                        : connection.prepareStatement("SELECT * FROM cdc.ddl_history")
                ) {
                    if (hasOffset) {
                        ddlPstmt.setBytes(1, offset.getDdlOffset());
                    }

                    if (null != ddlPstmt) {
                        try (ResultSet resultSet = ddlPstmt.executeQuery()) {
                            while (resultSet.next()) {
                                String ddlSql = resultSet.getString("ddl_command");
                                long referenceTime = resultSet.getTimestamp("ddl_time").getTime();
                                offset.setDdlOffset(resultSet.getBytes("ddl_lsn"));
                                StringBuilder tableId = new StringBuilder();
                                try {
                                    DDLFactory.ddlToTapDDLEvent(ddlParserType, replaceBrackets(ddlSql),
                                            DDL_WRAPPER_CONFIG,
                                            tableMap,
                                            tapDDLEvent -> {
                                                tapDDLEvent.setTime(System.currentTimeMillis());
                                                tapDDLEvent.setReferenceTime(referenceTime);
                                                tapDDLEvent.setOriginDDL(replaceBrackets(ddlSql));
                                                ddlCache.get(tapDDLEvent.getTableId()).add(tapDDLEvent);
                                            }, (ddl, wrapper) -> {
                                                boolean unIgnoreTable = true;
                                                if (wrapper instanceof MssqlDDLWrapper) {
                                                    String tableName = ((MssqlDDLWrapper) wrapper).getTableName(ddl);
                                                    unIgnoreTable = null == tables || tables.contains(tableName);
                                                    tableId.append(tableName);
                                                    if (!unIgnoreTable) {
                                                        tapLogger.debug("Table [{}] not in task table list, ignore ddl add TapDDLEvent", tableName);
                                                    } else {
                                                        tapLogger.warn("waiting DDL [{}] synchronizing... in the meantime SqlServer DML will lost!", ddlSql);
                                                    }
                                                }
                                                return unIgnoreTable;
                                            });
                                } catch (Throwable e) {
                                    if (StringUtils.isNotBlank(tableId.toString())) {
                                        TapDDLEvent tapDDLEvent = new TapDDLUnknownEvent();
                                        tapDDLEvent.setTime(System.currentTimeMillis());
                                        tapDDLEvent.setReferenceTime(referenceTime);
                                        tapDDLEvent.setOriginDDL(replaceBrackets(ddlSql));
                                        ddlCache.get(tableId.toString()).add(tapDDLEvent);
                                    }
                                }
                            }
                        }
                    }
                } catch (Throwable e) {
                    error.set(e);
                }
            }
        });
    }

    public Set<String> getCdcTables(MssqlJdbcRunner jdbcRunner) {
        Set<String> cdcTables = new HashSet<>();
        try {
            jdbcRunner.query("sys.sp_cdc_help_change_data_capture", rs -> {
                String schema, table;
                while (rs.next()) {
                    schema = rs.getString("source_schema").trim();
                    table = rs.getString("source_table").trim();
                    if (jdbcRunner.getConfig().getSchema().equalsIgnoreCase(schema)) {
                        cdcTables.add(table);
                    }
                }
            });
        } catch (Throwable e) {
            tapLogger.warn("Failed to get cdc tables for {} failed", jdbcRunner.getConfig().getSchema());
        }

        return cdcTables;
    }

    private void buildCT(TapTable table) {
        tapLogger.info("building CT table for table {}", table.getId());
        try {
            Collection<String> primaryKeys = table.primaryKeys();
            jdbcRunner.execute(String.format(MssqlCdcRunner.ENABLE_CDC_TABLE, MssqlMaker.escape(config.getSchema(), "'"), MssqlMaker.escape(table.getId(), "'"), MssqlMaker.escape(config.getSchema(), "'"), MssqlMaker.escape(table.getId(), "'"), EmptyKit.isEmpty(primaryKeys) ? "0" : "1"));
        } catch (SQLException e) {
            tapLogger.warn("build CT table for table {} failed", table.getId());
        }
    }

    public void startCdcRunner() throws Throwable {
        if (Boolean.TRUE.equals(config.getMultiQueryCdc())) {
            startCdcRunnerWithMultiThread();
        } else {
            startCdcRunnerWithSingleThread();
        }
    }

    public void startCdcRunnerWithSingleThread() throws Throwable {
        try {
            init();
            Map<String, Object> tablesOffset = offset.getTablesOffset();
            ResultSet resultSet = null;
            boolean firstLoopTime = true;
            AtomicBoolean firstTime = new AtomicBoolean(true);
            while (running.get() && null == error.get()) {
                List<TapEvent> eventList = TapSimplify.list();
                for (Map.Entry<String, PreparedStatement> entry : tablesCdcWherePstmt.entrySet()) {
                    String table = entry.getKey();
                    try {
                        PreparedStatement tableCdcWherePstmt = entry.getValue();
                        TableCaptureInstance tableCaptureInstance = tableCdcCaptureInstance.get(table);

                        if (tableCdcWherePstmt != null) {
                            tableCdcWherePstmt.setFetchSize(cdcFetchSize);
                        }

                        if (firstTime.compareAndSet(true, false)) {
                            tapLogger.info("Start to reading cdc table, fetch size: {}, interval: {} ms", cdcFetchSize, readCdcInterval);
                            consumer.streamReadStarted();
                        }

                        if (firstLoopTime && reMiner) {
                            // if multi data was inserted in one transaction, data in the same transaction will share one lsn
                            // in cdc table, so the db cached lsn may not been totally processed
                            if (tableCdcWherePstmt != null) {
                                String rawSQL = tablesCdcWherePstmtRawSQL.get(table);
                                if (rawSQL != null) {
                                    rawSQL = rawSQL.replace(" AND __$start_lsn > ?", " AND __$start_lsn >= ?");
                                    tableCdcWherePstmt = connection.prepareStatement(rawSQL);
                                }
                            }
                        }

                        String hexString = (String) tablesOffset.get(table);
                        String lastHexString = hexString;
                        long serial = 0L;
                        byte[] decode = EmptyKit.isBlank(hexString) ? null : BaseEncoding.base16().decode(hexString);
                        tableCdcWherePstmt.setBytes(1, decode);
                        resultSet = tableCdcWherePstmt.executeQuery();

                        ResultSetMetaData metaData = resultSet.getMetaData();
                        int columnCount = metaData.getColumnCount();
                        TapRecordEvent event = null;
                        while (resultSet.next()) {
                            Map<String, Object> value = new HashMap<>();
                            switch (resultSet.getInt(OPERATION_FIELD)) {
                                case DELETE_STATUS:
                                    if (null == event) {
                                        event = new TapDeleteRecordEvent().init()
                                                .table(table)
                                                .before(value);
                                    }
                                    break;
                                case INSERT_STATUS:
                                    if (null == event) {
                                        event = new TapInsertRecordEvent().init()
                                                .table(table)
                                                .after(value);
                                    }
                                    break;
                                case UPDATE_AFTER_STATUS:
                                    if (null == event) {
                                        event = new TapUpdateRecordEvent().init()
                                                .table(table);
                                    }
                                    assert event instanceof TapUpdateRecordEvent;
                                    ((TapUpdateRecordEvent) event).after(value);
                                    break;
                                case UPDATE_PREVIOUS_STATUS:
                                    if (null == event) {
                                        event = new TapUpdateRecordEvent().init()
                                                .table(table)
                                                .before(value);
                                    }
                                    assert event instanceof TapUpdateRecordEvent;
                                    ((TapUpdateRecordEvent) event).before(value);
                                    break;
                            }
                            Map<String, Object> streamOffset = new HashMap<>();

                            for (int i = 1; i <= columnCount; i++) {
                                String columnName = metaData.getColumnName(i);

                                if (excludeField.contains(columnName)) {
                                    continue;
                                }

                                if (OPERATION_FIELD.equals(columnName)) {
                                    continue;
                                }

                                if (START_LSN_FIELD.equals(columnName)) {
                                    byte[] startLSN = resultSet.getBytes(columnName);
                                    hexString = BaseEncoding.base16().encode(startLSN);
                                    if (Objects.equals(hexString, lastHexString)) {
                                        serial++;
                                    } else {
                                        serial = 1;
                                        lastHexString = hexString;
                                    }
                                    event.setExactlyOnceId(hexString + "_" + serial);
                                    streamOffset.put("scn", hexString);
                                    tablesOffset.put(table, hexString);
                                    offset.setCurrentStartLSN(hexString);
                                    continue;
                                } else if (START_TIME_FIELD.equals(columnName)) {
                                    assert event != null;
                                    timeZoneOffset = timeZoneOffset == null ? 0L : timeZoneOffset;
                                    long timestamp = resultSet.getTimestamp(columnName).getTime() - timeZoneOffset + TimeZone.getDefault().getRawOffset();
                                    event.setReferenceTime(timestamp);
                                    streamOffset.put("commitTime", timestamp);
                                    event.addInfo("streamOffset", streamOffset);
                                    continue;
                                }

                                Object object = resultSet.getObject(i);
                                if (!config.getOldVersionTimezone()) {
                                    object = handleDateTime(object, metaData.getColumnTypeName(i));
                                }
                                value.put(columnName, object);
                            }

                            if (completedEvent(
                                    event,
                                    resultSet.getBytes(UPDATE_MASK_FIELD),
                                    tableCaptureInstance.getCapturedColumnList()
                            )) {
                                eventList.add(event);
                                event = null;
                                if (eventList.size() >= recordSize) {
                                    consumer.accept(eventList, TapSimplify.toJson(offset));
                                    eventList = TapSimplify.list();
                                }

                            }
                        }

                        JdbcUtil.tryCommit(connection);
                    } finally {
                        JdbcUtil.closeQuietly(resultSet);
                    }
                    List<TapEvent> list = ddlCache.get(table);
                    if (list.size() > 0) {
                        Long lastEventTime = list.get(list.size() - 1).getTime();
                        consumer.accept(ddlCache.get(table), TapSimplify.toJson(offset));
                        ddlCache.put(table, TapSimplify.list());
                        reBuildCT(table, lastEventTime);
                    }
                }

                if (EmptyKit.isNotEmpty(eventList)) {
                    consumer.accept(eventList, TapSimplify.toJson(offset));
                }

                firstLoopTime = false;
                TapSimplify.sleep(readCdcInterval);
            }
        } catch (Throwable e) {
            error.set(e);
        } finally {
            releaseConnection(connection);
            consumer.streamReadEnded();
        }

        Throwable throwable = error.get();
        if (null != throwable) {
            exceptionCollector.collectTerminateByServer(throwable);
            exceptionCollector.revealException(throwable);
            throw throwable;
        }
    }

    public void startCdcRunnerWithMultiThread() throws Throwable {
        int multiThreads = Math.min(config.getMultiQueryThreadSize(), tables.size());
        ExecutorService executorService = Executors.newFixedThreadPool(multiThreads);
        try {
            init();
            Map<String, Object> tablesOffset = offset.getTablesOffset();
            consumer.streamReadStarted();
            AtomicInteger queryTime = new AtomicInteger(0);
            for (int th = 0; th < multiThreads; th++) {
                executorService.submit(() -> {
                    ResultSet resultSet = null;
                    while (running.get() && null == error.get()) {
                        List<TapEvent> eventList = TapSimplify.list();
                        String table = tableQueue.poll();
                        String lastHexString = (String) tablesOffset.get(table);
                        if (EmptyKit.isEmpty(table)) {
                            continue;
                        }
                        try {
                            PreparedStatement tableCdcWherePstmt = tablesCdcWherePstmt.get(table);
                            TableCaptureInstance tableCaptureInstance = tableCdcCaptureInstance.get(table);

                            if (tableCdcWherePstmt != null) {
                                tableCdcWherePstmt.setFetchSize(cdcFetchSize);
                            }

                            synchronized (this) {
                                if (queryTime.get() < tables.size()) {
                                    queryTime.incrementAndGet();
                                    // if multi data was inserted in one transaction, data in the same transaction will share one lsn
                                    // in cdc table, so the db cached lsn may not been totally processed
                                    if (tableCdcWherePstmt != null) {
                                        String rawSQL = tablesCdcWherePstmtRawSQL.get(table);
                                        if (rawSQL != null) {
                                            rawSQL = rawSQL.replace(" AND __$start_lsn > ?", " AND __$start_lsn >= ?");
                                            tableCdcWherePstmt = connection.prepareStatement(rawSQL);
                                        }
                                    }
                                }
                            }

                            String hexString = (String) tablesOffset.get(table);
                            String lastString = hexString;
                            long serial = 0L;
                            byte[] decode = EmptyKit.isBlank(hexString) ? null : BaseEncoding.base16().decode(hexString);
                            tableCdcWherePstmt.setBytes(1, decode);
                            resultSet = tableCdcWherePstmt.executeQuery();

                            ResultSetMetaData metaData = resultSet.getMetaData();
                            int columnCount = metaData.getColumnCount();
                            TapRecordEvent event = null;
                            while (resultSet.next()) {
                                Map<String, Object> value = new HashMap<>();
                                switch (resultSet.getInt(OPERATION_FIELD)) {
                                    case DELETE_STATUS:
                                        if (null == event) {
                                            event = new TapDeleteRecordEvent().init()
                                                    .table(table)
                                                    .before(value);
                                        }
                                        break;
                                    case INSERT_STATUS:
                                        if (null == event) {
                                            event = new TapInsertRecordEvent().init()
                                                    .table(table)
                                                    .after(value);
                                        }
                                        break;
                                    case UPDATE_AFTER_STATUS:
                                        if (null == event) {
                                            event = new TapUpdateRecordEvent().init()
                                                    .table(table);
                                        }
                                        assert event instanceof TapUpdateRecordEvent;
                                        ((TapUpdateRecordEvent) event).after(value);
                                        break;
                                    case UPDATE_PREVIOUS_STATUS:
                                        if (null == event) {
                                            event = new TapUpdateRecordEvent().init()
                                                    .table(table)
                                                    .before(value);
                                        }
                                        assert event instanceof TapUpdateRecordEvent;
                                        ((TapUpdateRecordEvent) event).before(value);
                                        break;
                                }
                                Map<String, Object> streamOffset = new HashMap<>();

                                for (int i = 1; i <= columnCount; i++) {
                                    String columnName = metaData.getColumnName(i);

                                    if (excludeField.contains(columnName)) {
                                        continue;
                                    }

                                    if (OPERATION_FIELD.equals(columnName)) {
                                        continue;
                                    }

                                    if (START_LSN_FIELD.equals(columnName)) {
                                        byte[] startLSN = resultSet.getBytes(columnName);
                                        hexString = BaseEncoding.base16().encode(startLSN);
                                        if (Objects.equals(hexString, lastString)) {
                                            serial++;
                                        } else {
                                            serial = 1;
                                            lastString = hexString;
                                        }
                                        event.setExactlyOnceId(hexString + "_" + serial);
                                        streamOffset.put("scn", hexString);
                                        //多线程下，offset需要提交后方能更新，全局current offset不影响
//                                        tablesOffset.put(table, hexString);
                                        lastHexString = hexString;
                                        offset.setCurrentStartLSN(hexString);
                                        continue;
                                    } else if (START_TIME_FIELD.equals(columnName)) {
                                        assert event != null;
                                        long timestamp = resultSet.getTimestamp(columnName).getTime() + TimeZone.getDefault().getRawOffset();
                                        event.setReferenceTime(timestamp);
                                        streamOffset.put("commitTime", timestamp);
                                        event.addInfo("streamOffset", streamOffset);
                                        continue;
                                    }

                                    Object object = resultSet.getObject(i);
                                    if (!config.getOldVersionTimezone()) {
                                        object = handleDateTime(object, metaData.getColumnTypeName(i));
                                    }
                                    value.put(columnName, object);
                                }

                                if (completedEvent(
                                        event,
                                        resultSet.getBytes(UPDATE_MASK_FIELD),
                                        tableCaptureInstance.getCapturedColumnList()
                                )) {
                                    eventList.add(event);
                                    event = null;
                                    if (eventList.size() >= recordSize) {
                                        submitEvents(eventList, TapSimplify.toJson(offset));
                                        tablesOffset.put(table, lastHexString);
                                        eventList = TapSimplify.list();
                                    }

                                }
                            }

                        } catch (Exception e) {
                            error.set(e);
                        } finally {
                            JdbcUtil.closeQuietly(resultSet);
                        }
                        if (EmptyKit.isNotEmpty(eventList)) {
                            submitEvents(eventList, TapSimplify.toJson(offset));
                            tablesOffset.put(table, lastHexString);
                        }
                        List<TapEvent> list = ddlCache.get(table);
                        if (list.size() > 0) {
                            Long lastEventTime = list.get(list.size() - 1).getTime();
                            submitEvents(ddlCache.get(table), TapSimplify.toJson(offset));
                            ddlCache.put(table, TapSimplify.list());
                            reBuildCT(table, lastEventTime);
                        }
                        tableQueue.offer(table);
                    }
                });
            }
            executorService.shutdown();
            while (!executorService.awaitTermination(1, TimeUnit.SECONDS)) {
                if (!running.get()) return;
            }
        } catch (Throwable e) {
            error.set(e);
        } finally {
            releaseConnection(connection);
            executorService.shutdownNow();
            consumer.streamReadEnded();
        }

        Throwable throwable = error.get();
        if (null != throwable) {
            exceptionCollector.collectTerminateByServer(throwable);
            exceptionCollector.revealException(throwable);
            throw throwable;
        }
    }

    private synchronized void submitEvents(List<TapEvent> eventList, String offset) {
        consumer.accept(eventList, offset);
    }

    private void reBuildCT(String table, Long lastEventTime) {
        tapLogger.info("rebuilding CT table for table {}", table);
        try {
            Collection<String> primaryKeys = tableMap.get(table).primaryKeys();
            jdbcRunner.queryWithNext(String.format(QUERY_CAPTURE_INSTANCE, MssqlMaker.escape(config.getSchema(), "'"), MssqlMaker.escape(table, "'")), resultSet -> {
                String captureInstance = resultSet.getString("capture_instance");
                List<String> reBuildSqls = TapSimplify.list();
                reBuildSqls.add(String.format(DISABLE_CDC_TABLE, MssqlMaker.escape(config.getSchema(), "'"), MssqlMaker.escape(table, "'"), MssqlMaker.escape(captureInstance, "'")));
                reBuildSqls.add(String.format(ENABLE_CDC_TABLE, MssqlMaker.escape(config.getSchema(), "'"), MssqlMaker.escape(table, "'"), MssqlMaker.escape(config.getSchema(), "'"), MssqlMaker.escape(table, "'"), EmptyKit.isEmpty(primaryKeys) ? "0" : "1"));
                jdbcRunner.batchExecute(reBuildSqls);
                tapLogger.warn("DDL for table {} has been finished, SqlServer DML recovery!", table);
            });
            changeCdcStatement(table, lastEventTime);
        } catch (SQLException e) {
            tapLogger.warn("rebuild CT table for table {} failed", table);
        }
    }

    private boolean completedEvent(TapRecordEvent event, byte[] updateMask, List<String> columnList) {
        if (!(event instanceof TapUpdateRecordEvent)) {
            return true;
        }

        Set<String> updateFields = new HashSet<>();
        {
            String hexString = BaseEncoding.base16().encode(updateMask);
            byte[] val = new byte[hexString.length() / 2];
            for (int i = 0; i < val.length; i++) {
                int index = i * 2;
                int j = Integer.parseInt(hexString.substring(index, index + 2), 16);
                val[i] = (byte) j;
            }
            reverse(val);
            final BitSet bitSet = BitSet.valueOf(val);
            for (int i = 0; i < bitSet.size(); i++) {
                if (bitSet.get(i)) {
                    updateFields.add(columnList.get(i));
                }
            }
        }

        // update events must have before and after
        final Map<String, Object> before = ((TapUpdateRecordEvent) event).getBefore();
        final Map<String, Object> after = ((TapUpdateRecordEvent) event).getAfter();
        if (EmptyKit.isNotEmpty(before) && EmptyKit.isNotEmpty(after)) {
            if (EmptyKit.isNotEmpty(updateFields)) { // 将没更新的列填充到目标中
                for (Map.Entry<String, Object> en : before.entrySet()) {
                    if (!updateFields.contains(en.getKey())) {
                        en.setValue(after.get(en.getKey()));
                    }
                }
            }
            return true;
        }
        return false;
    }

    public static void reverse(byte[] array) {
        if (array == null) {
            return;
        }
        int i = 0;
        int j = array.length - 1;
        byte tmp;
        while (j > i) {
            tmp = array[j];
            array[j] = array[i];
            array[i] = tmp;
            j--;
            i++;
        }
    }

    private void initCdcStatement() throws Exception {
        for (String table : tables) {

            Long lastEventTime = 0L;
            String currentStartLSN = offset.getCurrentStartLSN();
            if (null != currentStartLSN) {
                byte[] binCurrentStartLSN = BaseEncoding.base16().decode(currentStartLSN);
                lastEventTime = SqlServerCdcHelper.lsn2Time(connection, binCurrentStartLSN);
                if (null == lastEventTime) {
                    String currentLSN = SqlServerCdcHelper.getCurrentLSN(connection);
                    if (StringUtils.isBlank(currentLSN)) {
                        throw new TapPdkOffsetOutOfLogEx("sqlserver", offset, new Exception("offset is invalid"));
                    }
                    byte[] binCurrentLSN = BaseEncoding.base16().decode(currentLSN);
                    lastEventTime = SqlServerCdcHelper.lsn2Time(connection, binCurrentLSN);
                    tapLogger.warn("Failed to obtain events through LSN.The log may have been cleared. Will get current LSN event. offsetLSN: {}, currentLSN: {}", currentStartLSN, currentLSN);
                }
            }
            changeCdcStatement(table, lastEventTime);

            // 设置 Cdc 日志在辅助数据库不确认接受情况下，也使复制日志读取器前移。（全局）
            try {
                boolean isPrimaryReplica;
                try (PreparedStatement ps = connection.prepareStatement("select sys.fn_hadr_is_primary_replica (?)")) {
                    ps.setObject(1, config.getDatabase());
                    try (ResultSet rs = ps.executeQuery()) {
                        isPrimaryReplica = rs.next() && rs.getInt(1) == 1;
                    }
                } catch (Throwable throwable) {
                    //如果是单节点，无sys.fn_hadr_is_primary_replica函数，无须执行
                    tapLogger.debug("fn_hadr_is_primary_replica exec failed", throwable);
                    isPrimaryReplica = false;
                }
                JdbcUtil.tryCommit(connection);
                if (isPrimaryReplica) {
                    try (Statement s = connection.createStatement()) {
                        s.execute("dbcc traceon(1448,-1)");
                    }
                    JdbcUtil.tryCommit(connection);
                }
            } catch (Exception e) {
                tapLogger.warn("dbcc traceon 1448 failed: " + e.getMessage());
            }
        }
    }

    private void changeCdcStatement(String table, Long lastEventTime) throws SQLException {
        TableCaptureInstance captureInstance = SqlServerCdcHelper.getCaptureInstance(connection, config.getSchema(), table, lastEventTime);
        if (null == captureInstance) {
            tapLogger.warn("Cdc is not enabled for Table {}, cannot perform Cdc operation.", table);
            return;
        }
        tableCdcCaptureInstance.put(table, captureInstance);
        StringBuilder sb = new StringBuilder("cdc").append(".[").append(MssqlMaker.escape(captureInstance.getInstanceName(), "]")).append("_CT] t");
        sb = new StringBuilder(String.format(TABLE_CDC_SQL, sb));
        sb.append("   AND __$start_lsn > ?");
        if (config.getDoubleActive()) {
            sb.append(String.format(" AND not exists (select 1 from [cdc].[%s] where __$start_lsn = t.__$start_lsn)", MssqlMaker.escape(config.getSchema(), "]") + "__tap_double_active_CT"));
        }
        String rawSQL = sb.toString();
        tablesCdcWherePstmtRawSQL.put(table, rawSQL);
        tablesCdcWherePstmt.put(table, connection.prepareStatement(rawSQL));
    }

    private void releaseConnection(Connection connection) {

        if (EmptyKit.isNotEmpty(tablesCdcWherePstmt)) {
            for (PreparedStatement ps : tablesCdcWherePstmt.values()) {
                JdbcUtil.closeQuietly(ps);
            }
            tablesCdcWherePstmt.clear();
        }

        JdbcUtil.closeQuietly(connection);
    }

    public CdcOffset getOffsetByTs(Long ts) throws SQLException {
        String lsn = SqlServerCdcHelper.getLsnByTs(jdbcRunner.getConnection(), ts);
        CdcOffset offset = new CdcOffset();
        if (lsn != null) {
            offset.setCurrentStartLSN(lsn);
        }

        return offset;
    }

    private String replaceBrackets(String str) {
        //替换非双引号内的中括号
        return str.replaceAll("(?<!\")\\[|](?!\")", "\"");
    }

    public static final String TABLE_CDC_SQL = "" +
            "SELECT sys.fn_cdc_map_lsn_to_time([__$start_lsn]) __$start_time, * " +
            "  FROM %s " +
            " WHERE (__$operation = 1  OR __$operation = 2 OR __$operation = 3 OR __$operation = 4)";
    public static final String QUERY_CAPTURE_INSTANCE = "sys.sp_cdc_help_change_data_capture @source_schema = '%s', @source_name = '%s'";
    public static final String DISABLE_CDC_TABLE = "sys.sp_cdc_disable_table @source_schema = '%s', @source_name = '%s', @capture_instance = '%s'";
    public static final String ENABLE_CDC_TABLE = "if exists(select * from sys.tables t, sys.schemas s\n" +
            "                   where s.name='%s' and t.name='%s' and s.schema_id=t.schema_id and is_tracked_by_cdc = 0)\n" +
            " exec sys.sp_cdc_enable_table\n" +
            "        @source_schema = '%s',\n" +
            "        @source_name = '%s',\n" +
            "        @capture_instance = NULL,\n" +
            "        @supports_net_changes = %s,\n" +
            "        @role_name = NULL,\n" +
            "        @index_name = NULL,\n" +
            "        @captured_column_list = NULL";

    private Object handleDateTime(Object object, String dataType) {
        if (object instanceof Timestamp) {
            if (!dataType.startsWith("datetimeoffset")) {
                return ((Timestamp) object).toLocalDateTime().minusHours(config.getZoneOffsetHour());
            } else {
                return ((Timestamp) object).toLocalDateTime().atZone(ZoneId.systemDefault());
            }
        } else if (object instanceof java.sql.Date) {
            return Instant.ofEpochMilli(((Date) object).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        } else if (object instanceof Time) {
            return Instant.ofEpochMilli(((Time) object).getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime().minusHours(config.getZoneOffsetHour());
        }
        return object;
    }
}
