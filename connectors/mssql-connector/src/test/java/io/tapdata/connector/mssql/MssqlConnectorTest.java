package io.tapdata.connector.mssql;

import io.tapdata.common.*;
import io.tapdata.connector.mssql.cdc.MssqlCdcRunner;
import io.tapdata.connector.mssql.config.MssqlConfig;
import io.tapdata.connector.mssql.dml.MssqlRecordWriterV2;
import io.tapdata.connector.mssql.partition.MssqlPartitionContext;
import io.tapdata.connector.mssql.partition.vo.MSSQLPartitionInfo;
import io.tapdata.connector.mssql.partition.vo.PartitionFunctionAndStageResult;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.error.CoreException;
import io.tapdata.entity.event.ddl.table.TapCreateTableEvent;
import io.tapdata.entity.event.ddl.table.TapDropTableEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.logger.TapLog;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.partition.TapPartition;
import io.tapdata.entity.schema.type.*;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.entity.utils.cache.Entry;
import io.tapdata.entity.utils.cache.Iterator;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.pdk.apis.context.TapConnectionContext;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.ConnectorCapabilities;
import io.tapdata.pdk.apis.entity.TapAdvanceFilter;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.connector.common.vo.TapHashResult;
import io.tapdata.pdk.apis.functions.connector.target.CreateTableOptions;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockSettings;
import org.mockito.MockedConstruction;
import org.mockito.Mockito;
import org.mockito.internal.creation.MockSettingsImpl;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.*;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collector;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

public class MssqlConnectorTest {
    @Test
    void testRegisterCapabilitiesQueryTableHash(){
        MssqlConnector postgresConnector = new MssqlConnector();
        ConnectorFunctions connectorFunctions = new ConnectorFunctions();
        TapCodecsRegistry codecRegistry = new TapCodecsRegistry();
        ReflectionTestUtils.invokeMethod(postgresConnector,"registerCapabilities",connectorFunctions,codecRegistry);
        Assertions.assertTrue(connectorFunctions.getQueryHashByAdvanceFilterFunction()!=null);
    }


    @Test
    void testQueryTableHash() throws SQLException {

        MssqlConnector postgresConnector = new MssqlConnector();
        TapConnectorContext connectorContext = Mockito.mock(TapConnectorContext.class);
        TapAdvanceFilter filter = new TapAdvanceFilter();
        TapTable table = new TapTable();
        LinkedHashMap<String, TapField> map = new LinkedHashMap<>();
        table.setNameFieldMap(map);
        JdbcContext jdbcContext = Mockito.mock(JdbcContext.class);
        ReflectionTestUtils.setField(postgresConnector,"jdbcContext",jdbcContext);
        CommonSqlMaker commonSqlMaker = new CommonSqlMaker();
        ReflectionTestUtils.setField(postgresConnector,"commonSqlMaker",commonSqlMaker);
        doNothing().when(jdbcContext).query(Mockito.anyString(),Mockito.any());

        Consumer<TapHashResult<String>> consumer = new Consumer<TapHashResult<String>>() {
            @Override
            public void accept(TapHashResult<String> stringTapHashResult) {
                Assertions.assertTrue(stringTapHashResult == null);
            }
        };
        ReflectionTestUtils.invokeMethod(postgresConnector,"queryTableHash",connectorContext,filter,table,consumer);

    }


    @Test
    void testBuildHashSqlWith() throws SQLException {

        MssqlConnector postgresConnector = new MssqlConnector();
        TapAdvanceFilter filter = new TapAdvanceFilter();
        TapTable table = new TapTable();
        LinkedHashMap<String, TapField> map = new LinkedHashMap<>();

        buildNumberTapField("real",map);
        buildNumberTapField("double",map);
        buildNumberTapField("numeric",map);
        buildNumberTapField("float",map);
        buildNumberTapField("decimal",map);
        buildNumberTapField("money",map);


        TapField stringTapField  = new TapField();
        stringTapField.setTapType(new TapString());
        stringTapField.setName("char");
        stringTapField.setDataType("char(6)");
        map.put("char",stringTapField);

        TapField ncharTapField  = new TapField();
        ncharTapField.setTapType(new TapString());
        ncharTapField.setName("nchar");
        ncharTapField.setDataType("nchar(6)");
        map.put("nchar",ncharTapField);




        TapField timeTapField  = new TapField();
        timeTapField.setTapType(new TapTime());
        timeTapField.setName("time");
        timeTapField.setDataType("time");
        map.put("time",timeTapField);

        TapField datetimeTapField  = new TapField();
        datetimeTapField.setTapType(new TapDateTime());
        datetimeTapField.setName("timestamp");
        datetimeTapField.setDataType("timestamp");
        map.put("timestamp",datetimeTapField);

        TapField binaryTapField  = new TapField();
        binaryTapField.setTapType(new TapBinary());
        binaryTapField.setName("binary");
        binaryTapField.setDataType("binary");
        map.put("binary",binaryTapField);

        TapField intTapField  = new TapField();
        intTapField.setTapType(new TapNumber());
        intTapField.setName("bigint");
        intTapField.setDataType("bigint");
        map.put("int",intTapField);

        table.setNameFieldMap(map);
        JdbcContext jdbcContext = Mockito.mock(JdbcContext.class);
        ReflectionTestUtils.setField(postgresConnector,"jdbcContext",jdbcContext);
        CommonSqlMaker commonSqlMaker = new CommonSqlMaker();
        ReflectionTestUtils.setField(postgresConnector,"commonSqlMaker",commonSqlMaker);

        String actualData =ReflectionTestUtils.invokeMethod(postgresConnector,"buildHashSql",filter,table);

        Assertions.assertTrue(actualData.contains("CAST(real as bigint)"));
        Assertions.assertTrue(actualData.contains("CAST(double as bigint)"));
        Assertions.assertTrue(actualData.contains("CAST(numeric as bigint)"));
        Assertions.assertTrue(actualData.contains("CAST(float as bigint)"));
        Assertions.assertTrue(actualData.contains("CAST(decimal as bigint)"));
        Assertions.assertTrue(actualData.contains("TRIM( char )"));
        Assertions.assertTrue(actualData.contains("TRIM( nchar )"));
        Assertions.assertTrue(actualData.contains("cast(time as varchar(8))"));
        Assertions.assertTrue(actualData.contains("ROUND((DATEDIFF(SECOND,'1970-01-01 00:00:00',CAST(\"timestamp\" AS DATETIME))),0)"));
        Assertions.assertTrue(actualData.contains("int"));
        Assertions.assertTrue(!actualData.contains("binary"));



    }



    public void  buildNumberTapField(String name,LinkedHashMap<String, TapField> map){
        TapField numberTapField  = new TapField();
        numberTapField.setTapType(new TapNumber());
        numberTapField.setName(name);
        numberTapField.setDataType(name);
        map.put(name,numberTapField);
    }

    @Test
    void testRegisterCapabilitiesCountByPartitionFilter(){
        MssqlConnector mssqlConnector = new MssqlConnector();
        ConnectorFunctions connectorFunctions = new ConnectorFunctions();
        TapCodecsRegistry codecRegistry = new TapCodecsRegistry();
        ReflectionTestUtils.invokeMethod(mssqlConnector,"registerCapabilities",connectorFunctions,codecRegistry);
        Assertions.assertNotNull(connectorFunctions.getCountByPartitionFilterFunction());
    }


    @Nested
    class GetHashSplitStringSqlTest {
        TapTable tapTable;
        MssqlConnector connector;

        @BeforeEach
        void setUp() {
            connector = mock(MssqlConnector.class);
            tapTable = new TapTable();
            tapTable.setNameFieldMap(new LinkedHashMap<>());
            doCallRealMethod().when(connector).getHashSplitStringSql(tapTable);
        }

        private LinkedHashMap<String, TapField> generateFieldMap(TapField... fields) {
            if (null == fields) {
                return null;
            }
            LinkedHashMap<String, TapField> fieldMap = new LinkedHashMap<>();
            for (TapField field : fields) {
                fieldMap.put(field.getName(), field);
            }
            return fieldMap;
        }

        @Test
        void testEmptyField() {
            doCallRealMethod().when(connector).getHashSplitStringSql(tapTable);
            assertDoesNotThrow(() -> connector.getHashSplitStringSql(tapTable));
        }

        @Test
        void testNotPrimaryKeys() {
            tapTable.add(new TapField("ID", "INT"));
            tapTable.add(new TapField("TITLE", "VARCHAR(64)"));
            assertDoesNotThrow(() -> connector.getHashSplitStringSql(tapTable));
        }

        @Test
        void testPk1True() {
            tapTable.add(new TapField("ID", "INT").primaryKeyPos(1));
            tapTable.add(new TapField("TITLE", "VARCHAR(64)"));

            String result = connector.getHashSplitStringSql(tapTable);
            assertNotNull(result);
            assertTrue(result.contains("CONCAT"));
        }

        @Test
        void testPk2True() {
            tapTable.add(new TapField("ID1", "INT").primaryKeyPos(1));
            tapTable.add(new TapField("ID2", "INT").primaryKeyPos(2));
            tapTable.add(new TapField("TITLE", "VARCHAR(64)"));

            String result = connector.getHashSplitStringSql(tapTable);
            assertNotNull(result);
            assertTrue(result.contains("CONCAT"));
        }
    }
    @Nested
    class ConnectionTest{
        @Test
        void test(){
            MssqlConnector mssqlConnector = mock(MssqlConnector.class);
            TapConnectionContext connectionContext = mock(TapConnectionContext.class);
            when(connectionContext.getLog()).thenReturn(new TapLog());
            DataMap dataMap = new DataMap();
            dataMap.put("database","test");
            when(connectionContext.getConnectionConfig()).thenReturn(dataMap);
            Consumer<TestItem> consumer = testItem -> {
            };
            doCallRealMethod().when(mssqlConnector).connectionTest(connectionContext,consumer);
            Assertions.assertThrows(IllegalArgumentException.class,()->{mssqlConnector.connectionTest(connectionContext,consumer);});
        }
    }

    @Test
    void testDropPartitionTable() throws SQLException {
        List<String> list = Collections.singletonList("test");
        TapDropTableEvent event = new TapDropTableEvent();

        MssqlPartitionContext mssqlPartitionContext = mock(MssqlPartitionContext.class);
        when(mssqlPartitionContext.getDropPartitionTableSql(event)).thenReturn(list);

        MssqlJdbcRunner jdbcRunner = mock(MssqlJdbcRunner.class);
        MssqlConnector mssqlConnector = mock(MssqlConnector.class);

        ReflectionTestUtils.setField(mssqlConnector, "mssqlPartitionContext", mssqlPartitionContext);
        ReflectionTestUtils.setField(mssqlConnector, "jdbcRunner", jdbcRunner);

        doCallRealMethod().when(mssqlConnector).dropPartitionTable(null, event);

        doNothing().when(jdbcRunner).batchExecute(list);

        mssqlConnector.dropPartitionTable(null, event);
        verify(jdbcRunner, times(1)).batchExecute(list);
    }

    @Nested
    class testCreatePartitionTable {

        private MssqlConnector mssqlConnector;
        private MssqlJdbcRunner jdbcRunner;
        private MssqlPartitionContext mssqlPartitionContext;

        @BeforeEach
        void beforeEach() {
            mssqlConnector = new MssqlConnector();

            jdbcRunner = mock(MssqlJdbcRunner.class);
            mssqlPartitionContext = mock(MssqlPartitionContext.class);
            ReflectionTestUtils.setField(mssqlConnector, "jdbcRunner", jdbcRunner);
            mssqlConnector.mssqlPartitionContext = mssqlPartitionContext;
        }

        @Test
        void testCreatePartitionTableWithExists() throws SQLException {
            List<DataMap> existsTables = Collections.singletonList(new DataMap());
            when(jdbcRunner.queryAllTables(anyList())).thenReturn(existsTables);

            TapConnectorContext connectorContext = mock(TapConnectorContext.class);

            TapCreateTableEvent createTableEvent = new TapCreateTableEvent();
            createTableEvent.setTable(new TapTable());
            createTableEvent.getTable().setId("test_1");

            CreateTableOptions result = mssqlConnector.createPartitionTable(connectorContext, createTableEvent);
            Assertions.assertNotNull(result);
            Assertions.assertTrue(result.getTableExists());
        }

        @Test
        void testCreatePartitionTableWithNotExists() throws SQLException {
            List<DataMap> existsTables = Collections.emptyList();
            when(jdbcRunner.queryAllTables(anyList())).thenReturn(existsTables);

            TapConnectorContext connectorContext = mock(TapConnectorContext.class);

            TapCreateTableEvent createTableEvent = new TapCreateTableEvent();
            createTableEvent.setTable(new TapTable());
            createTableEvent.getTable().setId("test_1");

            List<String> sql = Collections.singletonList("test sql");

            when(mssqlPartitionContext.getPartitionTableSql(createTableEvent)).thenReturn(sql);

            CreateTableOptions result = mssqlConnector.createPartitionTable(connectorContext, createTableEvent);
            Assertions.assertDoesNotThrow(() -> {
                verify(jdbcRunner, times(1)).batchExecute(anyList());
            });

            Assertions.assertFalse(result.getTableExists());
        }

    }

    @Nested
    class testCreateSubPartitionTable {
        private MssqlConnector mssqlConnector;
        private MssqlJdbcRunner jdbcRunner;
        private MssqlPartitionContext mssqlPartitionContext;
        private TapConnectorContext connectorContext;

        @BeforeEach
        void beforeEach() {
            mssqlConnector = new MssqlConnector();

            jdbcRunner = mock(MssqlJdbcRunner.class);
            mssqlPartitionContext = mock(MssqlPartitionContext.class);
            ReflectionTestUtils.setField(mssqlConnector, "jdbcRunner", jdbcRunner);
            mssqlConnector.mssqlPartitionContext = mssqlPartitionContext;

            connectorContext = mock(TapConnectorContext.class);
            when(connectorContext.getLog()).thenReturn(mock(Log.class));
        }

        private TapCreateTableEvent createSubPartitionTable() {
            TapCreateTableEvent createTableEvent = new TapCreateTableEvent();
            createTableEvent.setTable(new TapTable());
            createTableEvent.getTable().setId("test_1");
            createTableEvent.getTable().setPartitionMasterTableId("test");
            createTableEvent.getTable().setPartitionInfo(new TapPartition());
            return createTableEvent;
        }

        @Test
        void testCreateSubPartitionTableWithNonTableId() {
            TapCreateTableEvent event = createSubPartitionTable();
            Assertions.assertDoesNotThrow(() -> {
                CreateTableOptions result = mssqlConnector.createSubPartitionTable(connectorContext, event, null);
                Assertions.assertNotNull(result);
                Assertions.assertNull(result.getTableExists());
            });
        }

        @Test
        void testCreateSubPartitionTableWithNonPartitionSchemaNameOrFunctionName() {
            TapCreateTableEvent event = createSubPartitionTable();

            Assertions.assertDoesNotThrow(() -> {

                PartitionFunctionAndStageResult partitionFunctionAndStageResult = new PartitionFunctionAndStageResult();
                when(mssqlPartitionContext.queryPartitionFunctions(anyString()))
                        .thenReturn(partitionFunctionAndStageResult);

                CreateTableOptions result = mssqlConnector.createSubPartitionTable(connectorContext, event, "test_1");
                Assertions.assertNotNull(result);
                Assertions.assertFalse(result.getTableExists());

                partitionFunctionAndStageResult.setPartitionSchemaName("dbo");
                result = mssqlConnector.createSubPartitionTable(connectorContext, event, "test_1");
                Assertions.assertNotNull(result);
                Assertions.assertFalse(result.getTableExists());
            });
        }

        @Test
        void testCreateSubPartitionTable() {
            TapCreateTableEvent event = createSubPartitionTable();

            Assertions.assertDoesNotThrow(() -> {

                PartitionFunctionAndStageResult partitionFunctionAndStageResult = new PartitionFunctionAndStageResult();
                partitionFunctionAndStageResult.setPartitionSchemaName("dbo");
                partitionFunctionAndStageResult.setPartitionFunctionName("partition_fun_date");

                when(mssqlPartitionContext.queryPartitionFunctions(anyString()))
                        .thenReturn(partitionFunctionAndStageResult);

                doNothing().when((JdbcContext)jdbcRunner).execute(anyString());

                CreateTableOptions result = mssqlConnector.createSubPartitionTable(connectorContext, event, "test_1");

                verify(jdbcRunner, times(1)).execute(anyString());

                Assertions.assertNotNull(result);
                Assertions.assertNull(result.getTableExists());
            });
        }

        @Test
        void testCreateSubPartitionTableWithSuccessful() {
            TapCreateTableEvent event = createSubPartitionTable();

            Assertions.assertDoesNotThrow(() -> {

                PartitionFunctionAndStageResult partitionFunctionAndStageResult = new PartitionFunctionAndStageResult();
                partitionFunctionAndStageResult.setPartitionSchemaName("dbo");
                partitionFunctionAndStageResult.setPartitionFunctionName("partition_fun_date");

                when(mssqlPartitionContext.queryPartitionFunctions(anyString()))
                        .thenReturn(partitionFunctionAndStageResult);

                doNothing().when((JdbcContext)jdbcRunner).execute(anyString());

                MSSQLPartitionInfo mssqlPartitionInfo = new MSSQLPartitionInfo();
                when(mssqlPartitionContext.getPartitionInfo(eq(event.getTable().getPartitionInfo()), eq("test_1"), any()))
                        .thenReturn(mssqlPartitionInfo);

                CreateTableOptions result = mssqlConnector.createSubPartitionTable(connectorContext, event, "test_1");

                verify(jdbcRunner, times(2)).execute(anyString());

                Assertions.assertNotNull(result);
                Assertions.assertNotNull(result.getTableExists());
                Assertions.assertTrue(result.getTableExists());
            });
        }
    }

    @Test
    void testGetSchemaAndTable() {
        MssqlConnector mssqlConnector = new MssqlConnector();
        MssqlConfig config = new MssqlConfig();
        config.setSchema("dbo");
        ReflectionTestUtils.setField(mssqlConnector, "config", config);
        String result = mssqlConnector.getSchemaAndTable("test");
        Assertions.assertEquals("[dbo].[test]", result);
    }

    @Nested
    class testWriteRecord {

        private MssqlConnector mssqlConnector;
        private MssqlJdbcRunner jdbcRunner;
        private MssqlPartitionContext mssqlPartitionContext;
        private TapConnectorContext connectorContext;

        @BeforeEach
        void beforeEach() {
            mssqlConnector = new MssqlConnector();

            jdbcRunner = mock(MssqlJdbcRunner.class);
            mssqlPartitionContext = mock(MssqlPartitionContext.class);
            ReflectionTestUtils.setField(mssqlConnector, "jdbcRunner", jdbcRunner);
            mssqlConnector.mssqlPartitionContext = mssqlPartitionContext;

            connectorContext = mock(TapConnectorContext.class);
            when(connectorContext.getLog()).thenReturn(mock(Log.class));
        }
        @Test
        void testWriteRecord() throws SQLException {

            ConnectorCapabilities capabilities = mock(ConnectorCapabilities.class);
            when(connectorContext.getConnectorCapabilities()).thenReturn(capabilities);

            when(capabilities.getCapabilityAlternative(eq(ConnectionOptions.DML_INSERT_POLICY)))
                    .thenReturn(null);
            when(capabilities.getCapabilityAlternative(eq(ConnectionOptions.DML_UPDATE_POLICY)))
                    .thenReturn(null);

            try(MockedConstruction<MssqlRecordWriterV2> mc = mockConstruction(MssqlRecordWriterV2.class, new MockSettingsImpl<MssqlRecordWriterV2>().defaultAnswer(answer -> {
                MssqlRecordWriterV2 mockWriter = mock(MssqlRecordWriterV2.class);
                when(mockWriter.setVersion(any())).thenReturn(mockWriter);
                when(mockWriter.setUpdatePolicy(any())).thenReturn(mockWriter);
                when(mockWriter.setInsertPolicy(any())).thenReturn(mockWriter);
                when(mockWriter.setTapLogger(any())).thenReturn(mockWriter);
                return mockWriter;
            }))) {
                assertDoesNotThrow(() -> {
                    mssqlConnector.writeRecord(connectorContext, null, null, null);
                });
            }
        }
    }

    @Test
    void testGetCdcTables() throws SQLException {
        MssqlConnector mssqlConnector = new MssqlConnector();

        MssqlJdbcRunner jdbcRunner = mock(MssqlJdbcRunner.class);
        CommonDbConfig config = new MssqlConfig();
        config.setSchema("dbo");
        when(jdbcRunner.getConfig()).thenReturn(config);

        doAnswer(answer -> {
            ResultSetConsumer consumer = answer.getArgument(1);


            ResultSet resultSet = mock(ResultSet.class);
            when(resultSet.next()).thenReturn(true, true, false);
            when(resultSet.getString(anyString()))
                    .thenReturn("dbo", "test", "dbo", "test1");
            consumer.accept(resultSet);
            return null;
        }).when(jdbcRunner).query(anyString(), any(ResultSetConsumer.class));

        Set<String> result = mssqlConnector.getCdcTables(jdbcRunner);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(2, result.size());
    }

    @Test
    void testBuildCT() throws SQLException {
        MssqlConnector mssqlConnector = new MssqlConnector();

        MssqlConfig config = new MssqlConfig();
        config.setSchema("dbo");

        MssqlJdbcRunner jdbcRunner = mock(MssqlJdbcRunner.class);
        Log tapLogger = mock(Log.class);

        ReflectionTestUtils.setField(mssqlConnector, "tapLogger", tapLogger);
        ReflectionTestUtils.setField(mssqlConnector, "jdbcRunner", jdbcRunner);
        ReflectionTestUtils.setField(mssqlConnector, "config", config);

        TapTable table = new TapTable();
        table.setId("test");
        table.setNameFieldMap(new LinkedHashMap<>());
        TapField field = new TapField();
        field.setName("id");
        field.setPrimaryKeyPos(1);
        field.setPrimaryKey(true);
        table.getNameFieldMap().put("id", field);
        ReflectionTestUtils.invokeMethod(mssqlConnector, "buildCT", table);

        verify(jdbcRunner, times(1)).execute(anyString());
    }

    @Test
    void testGetBatchReadSelectSql() {
        MssqlConnector mssqlConnector = new MssqlConnector();
        MssqlConfig config = new MssqlConfig();
        config.setSchema("dbo");
        ReflectionTestUtils.setField(mssqlConnector, "config", config);

        TapTable tapTable = new TapTable();
        tapTable.setId("test");

        tapTable.setNameFieldMap(new LinkedHashMap<>());
        Stream.generate(() -> {
            return new TapField("id", "string");
        }).limit(1).forEach(r -> {
            tapTable.getNameFieldMap().put(r.getName(), r);
        });

        String result = mssqlConnector.getBatchReadSelectSql(tapTable);
        Assertions.assertEquals("SELECT [id] FROM [dbo].[test]", result);

        tapTable.setNameFieldMap(new LinkedHashMap<>());
        tapTable.getNameFieldMap().put("id", new TapField("id", "int"));
        AtomicInteger counter = new AtomicInteger(0);
        Stream.generate(() -> {
            return new TapField("name_" + counter.incrementAndGet(), "string");
        }).limit(60).forEach(r -> {
            tapTable.getNameFieldMap().put(r.getName(), r);
        });
        result = mssqlConnector.getBatchReadSelectSql(tapTable);
        Assertions.assertEquals("SELECT * FROM [dbo].[test]", result);
    }

}
