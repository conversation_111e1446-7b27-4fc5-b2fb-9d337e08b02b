package io.tapdata.oceanbase.connector;

import com.oceanbase.jdbc.extend.datatype.INTERVALDS;
import com.oceanbase.jdbc.extend.datatype.INTERVALYM;
import com.oceanbase.jdbc.extend.datatype.TIMESTAMP;
import com.oceanbase.jdbc.extend.datatype.TIMESTAMPTZ;
import io.tapdata.common.SqlExecuteCommandFunction;
import io.tapdata.connector.oracle.OracleConnector;
import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.OracleSqlMaker;
import io.tapdata.connector.oracle.ddl.OracleDDLSqlGenerator;
import io.tapdata.connector.oracle.dml.OracleRecordWriter;
import io.tapdata.connector.oracle.exception.OracleExceptionCollector;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.ddl.table.TapAlterFieldAttributesEvent;
import io.tapdata.entity.event.ddl.table.TapAlterFieldNameEvent;
import io.tapdata.entity.event.ddl.table.TapDropFieldEvent;
import io.tapdata.entity.event.ddl.table.TapNewFieldEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.value.*;
import io.tapdata.entity.simplify.pretty.BiClassHandlers;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.StringKit;
import io.tapdata.oceanbase.OceanbaseOracleTest;
import io.tapdata.oceanbase.bean.OceanbaseOracleConfig;
import io.tapdata.oceanbase.cdc.OceanbaseOracleReader;
import io.tapdata.pdk.apis.annotations.TapConnectorClass;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.context.TapConnectionContext;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.entity.WriteListResult;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.connection.TableInfo;
import oracle.sql.BLOB;
import oracle.sql.CLOB;
import oracle.xdb.XMLType;

import java.io.IOException;
import java.sql.SQLException;
import java.sql.SQLRecoverableException;
import java.sql.Timestamp;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/23 15:56
 */
@TapConnectorClass("oceanbase-oracle-spec.json")
public class OceanbaseOracleConnector extends OracleConnector {

    private String connectionTimezone;
    private OceanbaseOracleConfig oceanbaseOracleConfig;
    private OceanbaseOracleReader oceanbaseOracleReader;
    private ArrayList<String> prepareSqlBeforeQuery;

    /**
     * The method invocation life circle is below,
     * initiated -> connectionTest -> destroy -> ended
     * <p>
     * You need to create the connection to your data source and release the connection in destroy method.
     * In connectionContext, you can get the connection config which is the user input for your connection application, described in your json file.
     * <p>
     * consumer can call accept method multiple times to test different items
     *
     * @param connectionContext
     * @return
     */
    @Override
    public ConnectionOptions connectionTest(TapConnectionContext connectionContext, Consumer<TestItem> consumer) {
        //Assume below tests are successfully, below tests are recommended, but not required.
        //Connection test
        oceanbaseOracleConfig = new OceanbaseOracleConfig().load(connectionContext.getConnectionConfig());
        ConnectionOptions connectionOptions = ConnectionOptions.create();
        try (
                OceanbaseOracleTest oceanbaseOracleTest = new OceanbaseOracleTest(oceanbaseOracleConfig, consumer, connectionOptions)
        ) {
            oceanbaseOracleTest.testOneByOne();
            return connectionOptions;
        }
    }

    @Override
    public void registerCapabilities(ConnectorFunctions connectorFunctions, TapCodecsRegistry codecRegistry) {
        connectorFunctions.supportErrorHandleFunction(this::errorHandle);
        connectorFunctions.supportWriteRecord(this::writeRecord);
        connectorFunctions.supportQueryByFilter(this::queryByFilter);
        connectorFunctions.supportGetTableInfoFunction(this::getTableInfo);
        connectorFunctions.supportBatchCount(this::batchCount);
        connectorFunctions.supportBatchRead(this::batchReadWithoutOffset);
        connectorFunctions.supportQueryByAdvanceFilter(this::queryByAdvanceFilterWithOffsetV2);
        connectorFunctions.supportTimestampToStreamOffset(this::timestampToStreamOffset);
        connectorFunctions.supportStreamRead(this::streamRead);
        //If database need insert record before table created, then please implement the below two methods.
        connectorFunctions.supportCreateTableV2(this::createTableV2);
        connectorFunctions.supportDropTable(this::dropTable);

        codecRegistry.registerFromTapValue(TapRawValue.class, "CLOB", tapRawValue -> {
            if (tapRawValue != null && tapRawValue.getValue() != null) return tapRawValue.getValue().toString();
            return "null";
        });
        codecRegistry.registerFromTapValue(TapMapValue.class, "CLOB", tapMapValue -> {
            if (tapMapValue != null && tapMapValue.getValue() != null) return toJson(tapMapValue.getValue());
            return "null";
        });
        codecRegistry.registerFromTapValue(TapArrayValue.class, "CLOB", tapValue -> {
            if (tapValue != null && tapValue.getValue() != null) return toJson(tapValue.getValue());
            return "null";
        });
        codecRegistry.registerFromTapValue(TapBooleanValue.class, "INTEGER", tapValue -> {
            if (tapValue != null && tapValue.getValue() != null)
                return Boolean.TRUE.equals(tapValue.getValue()) ? 1 : 0;
            return 0;
        });
        codecRegistry.registerToTapValue(XMLType.class, (value, tapType) -> {
            try {
                return new TapStringValue(((XMLType) value).getString());
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
        codecRegistry.registerToTapValue(TIMESTAMPTZ.class, (value, tapType) -> {
            try {
                return new TapDateTimeValue(new DateTime(((TIMESTAMPTZ) value).timestampValue().toLocalDateTime().atZone(ZoneOffset.UTC)));
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
//        codecRegistry.registerToTapValue(TIMESTAMPLTZ.class, (value, tapType) -> {
//            try {
//                return new TapDateTimeValue(new DateTime(((TIMESTAMPLTZ) value).toLocalDateTime(oracleProxyConnection.getOracleConnection())));
//            } catch (SQLException e) {
//                throw new RuntimeException(e);
//            }
//        });
        codecRegistry.registerToTapValue(INTERVALDS.class, (value, tapType) -> new TapStringValue(((INTERVALDS) value).stringValue()));
        codecRegistry.registerToTapValue(INTERVALYM.class, (value, tapType) -> new TapStringValue(((INTERVALYM) value).stringValue()));
//        codecRegistry.registerToTapValue(CLOB.class, (value, tapType) -> {
//            try {
//                return new TapStringValue(((CLOB) value).stringValue());
//            } catch (SQLException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        codecRegistry.registerToTapValue(NCLOB.class, (value, tapType) -> {
//            try {
//                return new TapStringValue(((NCLOB) value).stringValue());
//            } catch (SQLException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        codecRegistry.registerToTapValue(BLOB.class, (value, tapType) -> new TapBinaryValue(DbKit.blobToBytes((BLOB) value)));
        //TapTimeValue, TapDateTimeValue and TapDateValue's value is DateTime, need convert into Date object.
        codecRegistry.registerFromTapValue(TapTimeValue.class, "CHAR(8)", tapTimeValue -> formatTapDateTime(tapTimeValue.getValue(), "HH:mm:ss"));
        codecRegistry.registerFromTapValue(TapDateTimeValue.class, tapDateTimeValue -> {
            if (EmptyKit.isNotNull(tapDateTimeValue.getValue().getTimeZone())) {
                return tapDateTimeValue.getValue().toInstant().atZone(ZoneOffset.UTC).toLocalDateTime().atZone(ZoneId.systemDefault());
            } else {
                if (oceanbaseOracleConfig.getOldVersionTimezone()) {
                    return tapDateTimeValue.getValue().toTimestamp();
                } else {
                    return tapDateTimeValue.getValue().toInstant().atZone(oceanbaseOracleConfig.getZoneId());
                }
            }
        });
        codecRegistry.registerFromTapValue(TapYearValue.class, "CHAR(4)", TapValue::getOriginValue);
        codecRegistry.registerFromTapValue(TapDateValue.class, "CHAR(10)", tapDateValue -> formatTapDateTime(tapDateValue.getValue(), "yyyy-MM-dd"));
        connectorFunctions.supportExecuteCommandFunction((a, b, c) -> SqlExecuteCommandFunction.executeCommand(a, b, () -> jdbcContext.getConnection(), this::isAlive, c));
        connectorFunctions.supportRunRawCommandFunction(this::runRawCommand);
        //ddl
        connectorFunctions.supportNewFieldFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldNameFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldAttributesFunction(this::fieldDDLHandler);
        connectorFunctions.supportDropFieldFunction(this::fieldDDLHandler);
    }

    /**
     * @param tapConnectorContext
     * @param tapRecordEvents
     * @param tapTable
     * @param writeListResultConsumer
     */
    private void writeRecord(TapConnectorContext tapConnectorContext, List<TapRecordEvent> tapRecordEvents, TapTable tapTable, Consumer<WriteListResult<TapRecordEvent>> writeListResultConsumer) throws Throwable {
        String insertDmlPolicy = tapConnectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_INSERT_POLICY);
        if (insertDmlPolicy == null) {
            insertDmlPolicy = ConnectionOptions.DML_INSERT_POLICY_UPDATE_ON_EXISTS;
        }
        String updateDmlPolicy = tapConnectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_UPDATE_POLICY);
        if (updateDmlPolicy == null) {
            updateDmlPolicy = ConnectionOptions.DML_UPDATE_POLICY_IGNORE_ON_NON_EXISTS;
        }
        new OracleRecordWriter(oracleJdbcContext, tapTable)
                .setInsertPolicy(insertDmlPolicy)
                .setUpdatePolicy(updateDmlPolicy)
                .setTapLogger(tapLogger)
                .write(tapRecordEvents, writeListResultConsumer, this::isAlive);
    }

    @Override
    public void onStart(TapConnectionContext tapConnectionContext) {
        prepareSqlBeforeQuery = new ArrayList<>();
        long queryTimeout = 86400L * 30 * 1000 * 1000;
        prepareSqlBeforeQuery.add("SET @@ob_trx_timeout = " + queryTimeout);
        prepareSqlBeforeQuery.add("SET @@ob_query_timeout = " + queryTimeout);

        oceanbaseOracleConfig = new OceanbaseOracleConfig().load(tapConnectionContext.getConnectionConfig());
        oceanbaseOracleConfig.load(tapConnectionContext.getNodeConfig());
        isConnectorStarted(tapConnectionContext, connectorContext -> {
            firstConnectorId = (String) connectorContext.getStateMap().get("firstConnectorId");
            if (EmptyKit.isNull(firstConnectorId)) {
                firstConnectorId = UUID.randomUUID().toString().replace("-", "");
                connectorContext.getStateMap().put("firstConnectorId", firstConnectorId);
            }
        });
        oracleJdbcContext = new OracleJdbcContext(oceanbaseOracleConfig);
        commonDbConfig = oceanbaseOracleConfig;
        jdbcContext = oracleJdbcContext;
        tapLogger = tapConnectionContext.getLog();
        commonSqlMaker = new OracleSqlMaker().closeNotNull(oceanbaseOracleConfig.getCloseNotNull());
        exceptionCollector = new OracleExceptionCollector();
        ddlSqlGenerator = new OracleDDLSqlGenerator();
        fieldDDLHandlers = new BiClassHandlers<>();
        fieldDDLHandlers.register(TapNewFieldEvent.class, this::newField);
        fieldDDLHandlers.register(TapAlterFieldAttributesEvent.class, this::alterFieldAttr);
        fieldDDLHandlers.register(TapAlterFieldNameEvent.class, this::alterFieldName);
        fieldDDLHandlers.register(TapDropFieldEvent.class, this::dropField);
    }

    @Override
    public void onStop(TapConnectionContext connectionContext) {
        EmptyKit.closeQuietly(oracleJdbcContext);
        if (oceanbaseOracleReader != null) {
            oceanbaseOracleReader.stop();
            oceanbaseOracleReader = null;
        }
    }

    private TableInfo getTableInfo(TapConnectionContext tapConnectorContext, String tableName) throws Throwable {
        DataMap dataMap = oracleJdbcContext.getTableInfo(tableName);
        TableInfo tableInfo = TableInfo.create();
        tableInfo.setNumOfRows(Long.valueOf(dataMap.getString("TABLE_ROWS")));
        tableInfo.setStorageSize(Long.valueOf(dataMap.getString("DATA_LENGTH")));
        return tableInfo;
    }

    private Object timestampToStreamOffset(TapConnectorContext connectorContext, Long offsetStartTime) throws SQLException {
        if (EmptyKit.isNotNull(offsetStartTime)) {
            return offsetStartTime / 1000L;
        }
        AtomicLong offset = new AtomicLong(0);
        oracleJdbcContext.queryWithNext("select CURRENT_TIMESTAMP FROM dual", resultSet -> {
            offset.set(resultSet.getTimestamp(1).getTime() / 1000L);
        });
        return offset.get();
    }

    private void streamRead(TapConnectorContext nodeContext, List<String> tableList, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        oceanbaseOracleReader = new OceanbaseOracleReader(oceanbaseOracleConfig, firstConnectorId);
        oceanbaseOracleReader.init(tableList, nodeContext.getTableMap(), offsetState, recordSize, consumer);
        oceanbaseOracleReader.start(this::isAlive);
    }

    protected void batchReadWithoutOffset(TapConnectorContext tapConnectorContext, TapTable tapTable, Object offsetState, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) throws Throwable {
        if (Boolean.TRUE.equals(commonDbConfig.getHashSplit())) {
            batchReadWithHashSplit(tapConnectorContext, tapTable, offsetState, eventBatchSize, eventsOffsetConsumer);
        } else {
            batchReadWithoutHashSplit(tapConnectorContext, tapTable, offsetState, eventBatchSize, eventsOffsetConsumer);
        }
    }

    private List<String> getTablePartitions(TapConnectorContext tapConnectorContext, TapTable tapTable) {
        String sql = String.format("SELECT TABLE_NAME, PARTITION_NAME, PARTITION_ORDINAL_POSITION FROM INFORMATION_SCHEMA.PARTITIONS WHERE TABLE_NAME = '%s'", tapTable.getId());
        List<String> partitions = new ArrayList<>();
        try {
            jdbcContext.queryWithTimeout(sql, resultSet -> {
                while (resultSet.next()) {
                    partitions.add(resultSet.getString(2));
                }
            }, prepareSqlBeforeQuery);
        } catch (SQLException e) {
            exceptionCollector.collectReadPrivileges("getTablePartitions", Collections.emptyList(), e);
        }
        return partitions;
    }

    private void batchReadWorker(String sql, TapTable tapTable, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) throws Exception {
        int retry = 20;
        while (retry-- > 0 && isAlive()) {
            try {
                tapLogger.info("batchRead, sql: {}", sql);
                jdbcContext.streamQueryWithTimeout(sql, resultSet -> {
                    List<TapEvent> tapEvents = list();
                    //get all column names
                    List<String> columnNames = DbKit.getColumnsFromResultSet(resultSet);
                    while (isAlive() && resultSet.next()) {
                        DataMap dataMap = DbKit.getRowFromResultSet(resultSet, columnNames);
                        processDataMap(dataMap, tapTable);
                        tapEvents.add(insertRecordEvent(dataMap, tapTable.getId()));
                        if (tapEvents.size() == eventBatchSize) {
                            syncEventSubmit(tapEvents, eventsOffsetConsumer);
                            tapEvents = list();
                        }
                    }
                    //last events those less than eventBatchSize
                    if (EmptyKit.isNotEmpty(tapEvents)) {
                        syncEventSubmit(tapEvents, eventsOffsetConsumer);
                    }
                }, prepareSqlBeforeQuery, 2000);
                break;
            } catch (Exception e) {
                if (retry == 0 || !(e instanceof SQLRecoverableException || e instanceof IOException)) {
                    throw e;
                }
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        }
    }

    protected void batchReadWithPartition(TapConnectorContext tapConnectorContext, TapTable tapTable, Object offsetState, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) throws Throwable {
        String sql = getBatchReadSelectSql(tapTable);
        AtomicReference<Throwable> throwable = new AtomicReference<>();
        CountDownLatch countDownLatch = new CountDownLatch(commonDbConfig.getBatchReadThreadSize());
        ExecutorService executorService = Executors.newFixedThreadPool(commonDbConfig.getBatchReadThreadSize());
        List<String> tablePartitions = getTablePartitions(tapConnectorContext, tapTable);
        Integer threadSize = commonDbConfig.getBatchReadThreadSize();
        if (threadSize > tablePartitions.size()) {
            threadSize = tablePartitions.size();
        }

        // 计算每个线程应该处理的分区数
        int partitionsPerThread = tablePartitions.size() / threadSize;
        int remainingPartitions = tablePartitions.size() % threadSize;

        try {
            for (int i = 0; i < threadSize; i++) {
                final int threadIndex = i;
                // 计算每个线程的起始和结束索引
                int startIndex = threadIndex * partitionsPerThread + Math.min(threadIndex, remainingPartitions);
                int endIndex = startIndex + partitionsPerThread + (threadIndex < remainingPartitions ? 1 : 0);

                // 提交任务给线程池
                executorService.submit(() -> {
                    try {
                        // 获取当前线程要处理的分区子列表
                        List<String> threadPartitions = tablePartitions.subList(startIndex, endIndex);

                        // 打印线程信息和对应的分区
                        System.out.println("Thread " + threadIndex + " processing partitions: " + threadPartitions);

                        // 这里可以添加处理每个分区的具体逻辑
                        for (String partition : threadPartitions) {
                            String splitSql = sql + " partition(" + partition + ")";
                            try {
                                batchReadWorker(splitSql, tapTable, eventBatchSize, eventsOffsetConsumer);
                            } catch (Exception e) {
                                throwable.set(e);
                            }
                            if (EmptyKit.isNotNull(throwable.get())) {
                                exceptionCollector.collectTerminateByServer(throwable.get());
                                exceptionCollector.collectReadPrivileges("batchReadWithoutOffset", Collections.emptyList(), throwable.get());
                                exceptionCollector.revealException(throwable.get());
                                try {
                                    throw throwable.get();
                                } catch (Throwable e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        }
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            if (EmptyKit.isNotNull(throwable.get())) {
                exceptionCollector.collectTerminateByServer(throwable.get());
                exceptionCollector.collectReadPrivileges("batchReadWithoutOffset", Collections.emptyList(), throwable.get());
                exceptionCollector.revealException(throwable.get());
                throw throwable.get();
            }
        } catch (Exception e) {
            e.printStackTrace();
            executorService.shutdown();
        }
    }

    protected void batchReadWithHashSplit(TapConnectorContext tapConnectorContext, TapTable tapTable, Object offsetState, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) throws Throwable {
        List<String> tablePartitions = getTablePartitions(tapConnectorContext, tapTable);
        if (tablePartitions.size() >= 4) {
            batchReadWithPartition(tapConnectorContext, tapTable, offsetState, eventBatchSize, eventsOffsetConsumer);
            return;
        }

        String sql = getBatchReadSelectSql(tapTable);
        AtomicReference<Throwable> throwable = new AtomicReference<>();
        CountDownLatch countDownLatch = new CountDownLatch(commonDbConfig.getBatchReadThreadSize());
        ExecutorService executorService = Executors.newFixedThreadPool(commonDbConfig.getBatchReadThreadSize());
        Integer threadSize = commonDbConfig.getBatchReadThreadSize();

        try {
            for (int i = 0; i < threadSize; i++) {
                final int threadIndex = i;
                executorService.submit(() -> {
                    try {
                        for (int ii = threadIndex; ii < commonDbConfig.getMaxSplit(); ii += commonDbConfig.getBatchReadThreadSize()) {
                            String splitSql = sql + " WHERE " + getHashSplitModConditions(tapTable, commonDbConfig.getMaxSplit(), ii);
                            tapLogger.info("batchRead, splitSql[{}]: {}", ii + 1, splitSql);
                            batchReadWorker(splitSql, tapTable, eventBatchSize, eventsOffsetConsumer);
                        }
                    } catch (Exception e) {
                        throwable.set(e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            if (EmptyKit.isNotNull(throwable.get())) {
                exceptionCollector.collectTerminateByServer(throwable.get());
                exceptionCollector.collectReadPrivileges("batchReadWithoutOffset", Collections.emptyList(), throwable.get());
                exceptionCollector.revealException(throwable.get());
                throw throwable.get();
            }
        } finally {
            executorService.shutdown();
        }
    }

    @Override
    protected String getBatchReadSelectSql(TapTable tapTable) {
        String columns = tapTable.getNameFieldMap().keySet().stream().map(c -> commonDbConfig.getEscapeChar() + StringKit.escape(c, commonDbConfig.getEscapeChar()) + commonDbConfig.getEscapeChar()).collect(Collectors.joining(","));
        return String.format("SELECT /*+ PARALLEL(8) */ %s FROM " + getSchemaAndTable(tapTable.getId()), columns);
    }

    protected void batchReadWithoutHashSplit(TapConnectorContext tapConnectorContext, TapTable tapTable, Object offsetState, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) throws Throwable {
        String sql = getBatchReadSelectSql(tapTable);
        jdbcContext.streamQueryWithTimeout(sql, resultSet -> {
            List<TapEvent> tapEvents = list();
            //get all column names
            List<String> columnNames = DbKit.getColumnsFromResultSet(resultSet);
            try {
                while (isAlive() && resultSet.next()) {
                    DataMap dataMap = DbKit.getRowFromResultSet(resultSet, columnNames);
                    processDataMap(dataMap, tapTable);
                    tapEvents.add(insertRecordEvent(dataMap, tapTable.getId()));
                    if (tapEvents.size() == eventBatchSize) {
                        eventsOffsetConsumer.accept(tapEvents, new HashMap<>());
                        tapEvents = list();
                    }
                }
            } catch (SQLException e) {
                exceptionCollector.collectTerminateByServer(e);
                exceptionCollector.collectReadPrivileges("batchReadWithoutOffset", Collections.emptyList(), e);
                exceptionCollector.revealException(e);
                throw e;
            }
            //last events those less than eventBatchSize
            if (EmptyKit.isNotEmpty(tapEvents)) {
                eventsOffsetConsumer.accept(tapEvents, new HashMap<>());
            }
        }, prepareSqlBeforeQuery, 1000);
    }

    @Override
    protected void processDataMap(DataMap dataMap, TapTable tapTable) throws RuntimeException {
        try {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                Object value = entry.getValue();
                if (value instanceof Timestamp) {
                    entry.setValue(((Timestamp) value).toLocalDateTime().minusHours(oceanbaseOracleConfig.getZoneOffsetHour()));
                } else if (value instanceof CLOB) {
                    // CLOB or NCLOB
                    entry.setValue(((CLOB) value).stringValue());
                } else if (value instanceof BLOB) {
                    entry.setValue(DbKit.blobToBytes((BLOB) value));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}
