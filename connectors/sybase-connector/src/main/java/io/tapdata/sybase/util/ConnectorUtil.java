package io.tapdata.sybase.util;

import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapIndex;
import io.tapdata.entity.schema.TapIndexField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.type.TapType;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.entity.utils.cache.Entry;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;
import io.tapdata.sybase.filter.ReadFilter;

import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description ConnectorUtil create by Gavin
 * @create 2023/8/8 14:35
 **/
public class ConnectorUtil {

    /**
     * 从表模型中获取date和 datetime 类型的字段
     */
    public static Set<String> dateFields(TapTable tapTable) {
        Set<String> dateTypeSet = new HashSet<>();
        tapTable.getNameFieldMap().forEach((n, v) -> {
            switch (v.getTapType().getType()) {
                case TapType.TYPE_TIME:
                case TapType.TYPE_DATE:
                case TapType.TYPE_DATETIME:
                    dateTypeSet.add(n);
                    break;
                default:
                    break;
            }
        });
        return dateTypeSet;
    }
    public static Map<String,Byte> getDateTypeFields(TapTable tapTable) {
        Map<String,Byte> dateTypeSet = new HashMap<>();
        tapTable.getNameFieldMap().forEach((n, v) -> {
            switch (v.getTapType().getType()) {
                case TapType.TYPE_TIME:
                case TapType.TYPE_DATE:
                case TapType.TYPE_DATETIME:
                    dateTypeSet.put(n,v.getTapType().getType());
                    break;
                default:
                    break;
            }
        });
        return dateTypeSet;
    }

    /**
     * 获取表索引
     */
    public static void makePrimaryKeyAndIndex(List<DataMap> indexList, String table, Set<String> primaryKey, List<TapIndex> tapIndexList) {
        Map<String, List<DataMap>> indexMap = indexList.stream().filter(idx -> table.equals(idx.getString("tableName")))
                .collect(Collectors.groupingBy(idx -> idx.getString("index_name"), LinkedHashMap::new, Collectors.toList()));
        indexMap.forEach((key, value) -> {
            if (value.stream().anyMatch(v -> ("clustered, unique".equals(v.getString("index_description"))))) {
                for (String keys : value.stream().filter(v -> Objects.nonNull(v) && ("clustered, unique".equals(v.getString("index_description")))).filter(v -> null != v.get("index_keys")).map(v -> v.getString("index_keys")).collect(Collectors.toList())) {
                    if (EmptyKit.isBlank(keys)) {
                        continue;
                    }
                    for (String k : keys.split(",")) {
                        primaryKey.add(k.trim());
                    }
                }
            }
            tapIndexList.add(makeTapIndex(key, value));
        });
    }

    /**
     * 获取表索引
     */
    public static void makePrimaryKeyAndIndexV2(TapTable tapTable, List<DataMap> indexList, List<DataMap> pkList, List<String> primaryKeys) {
        if (EmptyKit.isNotEmpty(pkList)) {
            pkList.stream().sorted(Comparator.comparing(pk -> Integer.valueOf(pk.getString("key_seq").trim()))).map(pk -> pk.getString("column_name")).forEach(primaryKeys::add);
        }
        if (EmptyKit.isNotEmpty(indexList)) {
            Map<String, List<DataMap>> indexMap = indexList.stream().collect(Collectors.groupingBy(idx -> idx.getString("index_name"), LinkedHashMap::new, Collectors.toList()));
            indexMap.forEach((key, value) -> {
                TapIndex tapIndex = makeTapIndexV2(key, value);
                tapIndex.primary(tapIndex.getIndexFields().stream().map(TapIndexField::getName).collect(Collectors.toList()).equals(primaryKeys));
                tapTable.add(tapIndex);
            });
        }
    }

    public static TapIndex makeTapIndexV2(String key, List<DataMap> value) {
        String description = value.stream().findFirst().map(v -> v.getString("index_description")).orElse("");
        String keys = value.stream().findFirst().map(v -> v.getString("index_keys")).orElse("");
        TapIndex tapIndex = new TapIndex().name(key).unique(description.contains("unique")).cluster(!description.contains("nonclustered"));
        Arrays.stream(keys.split(",")).forEach(k -> {
            if (EmptyKit.isNotBlank(k)) {
                if (k.endsWith(" DESC")) {
                    tapIndex.indexField(new TapIndexField().name(k.replace(" DESC", "").trim()).fieldAsc(false));
                } else {
                    tapIndex.indexField(new TapIndexField().name(k.trim()).fieldAsc(true));
                }
            }
        });
        return tapIndex;
    }

    /**
     * 获取表索引
     */
    public static TapIndex makeTapIndex(String key, List<DataMap> value) {
        TapIndex index = new TapIndex();
        index.setName(key);
        value.forEach(v -> {
            String indexKeys = v.getString("index_keys");
            String[] keyNames = indexKeys.split(",");
            String indexDescription = v.getString("index_description");
            List<TapIndexField> fieldList = TapSimplify.list();
            for (String keyName : keyNames) {
                if (null == keyName || "".equals(keyName.trim())) continue;
                TapIndexField field = new TapIndexField();
                //field.setFieldAsc("1".equals(v.getString("isAsc")));
                field.setName(keyName.trim());
                fieldList.add(field);
            }
            index.setUnique(indexDescription.contains("unique"));
            index.setPrimary(indexDescription.contains("clustered, unique"));
            index.setIndexFields(fieldList);

        });
        return index;
    }

    /**
     * 移除第一张表
     */
    public static synchronized List<String> getOutTableList(CopyOnWriteArraySet<List<String>> tableLists) {
        if (EmptyKit.isNotEmpty(tableLists)) {
            List<String> list = tableLists.stream().findFirst().orElseGet(ArrayList::new);
            tableLists.remove(list);
            return list;
        }
        return null;
    }

    public static Map<String, TapTable> getTapTableMap(KVReadOnlyMap<TapTable> tableMap) {
        Map<String, TapTable> tapTableMap = new HashMap<>();
        if (null == tableMap) return tapTableMap;
        io.tapdata.entity.utils.cache.Iterator<Entry<TapTable>> iterator = tableMap.iterator();
        while (iterator.hasNext()) {
            Entry<TapTable> next = iterator.next();
            tapTableMap.put(next.getKey(), next.getValue());
        }
        return tapTableMap;
    }

    public static Map<String, List<ConnectionConfigWithTables>> groupConnectionConfigWithTables(List<ConnectionConfigWithTables> connectionConfigWithTables) {
        if (null == connectionConfigWithTables) return new HashMap<>();
        return connectionConfigWithTables.stream()
                .filter(f -> null != f && null != f.getConnectionConfig() && null != f.getTables())
                .collect(Collectors.groupingBy(f -> {
                    DataMap connectionConfig = f.getConnectionConfig();
                    return connectionConfig.getString("schema");
                }));
    }

    public static Map<String, Set<String>> getBlockFieldsMap(Integer logCdcQuery, Map<String, TapTable> tapTableMap, String database, String schema, List<String> tables, Log log) {
        Map<String, Set<String>> blockFields = new HashMap<>();
        if (ReadFilter.TYPE.LOG_CDC_QUERY_READ_SOURCE.getType() == logCdcQuery) {
            for (String tableName : tables) {
                final String tableFullName = String.format("%s.%s", schema, tableName);
                blockFields(tapTableMap, tableFullName, blockFields, log);
            }
        }
        return blockFields;
    }

    private static void blockFields(Map<String, TapTable> tapTableMap, String tableFullName, Map<String, Set<String>> blockFields, Log log) {
        Optional.ofNullable(ReadFilter.getTapTable(tableFullName, tapTableMap)).ifPresent(tapTable -> {
            Set<String> blockFieldsSet = new HashSet<>();
            Collection<String> primaryKeys = tapTable.primaryKeys(true);
            if (null != primaryKeys && !primaryKeys.isEmpty()) {
                LinkedHashMap<String, TapField> nameFieldMap = tapTable.getNameFieldMap();
                if (null != nameFieldMap && !nameFieldMap.isEmpty()) {
                    nameFieldMap.entrySet().stream()
                            .filter(Objects::nonNull)
                            .filter(f -> {
                                TapField tapField = f.getValue();
                                return !primaryKeys.contains(tapField.getName())
                                        && ReadFilter.isBolField(tapField.getDataType());
                            }).forEach(f -> {
                                String fieldName = f.getKey();
                                blockFieldsSet.add(fieldName);
                            });
                    if (!blockFieldsSet.isEmpty()) {
                        blockFields.put(tableFullName, blockFieldsSet);
                    }
                }
            } else {
                log.debug("Not fund any primary key in table {} when config sybase filter yaml and has open read bol value from source, " +
                                "it's mean can not read from source of this table, auto read from log of this table now",
                        tableFullName);
            }
        });
    }

    /**
     * @param logCdcQuery see io.tapdata.sybase.filter.ReadFilter.TYPE
     * @param tapTableMap Map<'schema.table', set<fieldName>>
     */
    public static Map<String, Set<String>> getBlockFieldsMap(Integer logCdcQuery, Map<String, TapTable> tapTableMap, List<ConnectionConfigWithTables> connectionConfigWithTables, Log log) {
        Map<String, Set<String>> blockFields = new HashMap<>();
        if (ReadFilter.TYPE.LOG_CDC_QUERY_READ_SOURCE.getType() == logCdcQuery) {
            Map<String, List<ConnectionConfigWithTables>> listMap = groupConnectionConfigWithTables(connectionConfigWithTables);
            listMap.forEach((schema, config) -> {
                for (ConnectionConfigWithTables tablesConfig : config) {
                    List<String> tables = tablesConfig.getTables();
                    for (String tableName : tables) {
                        final String tableFullName = String.format("%s.%s", schema, tableName);
                        blockFields(tapTableMap, tableFullName, blockFields, log);
                    }
                }
            });
        }
        return blockFields;
    }
}
