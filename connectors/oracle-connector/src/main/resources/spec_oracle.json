{"properties": {"name": "Oracle", "icon": "icons/oracle.png", "doc": "${doc}", "id": "oracle", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists", "just_insert"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists", "log_on_nonexists"]}, {"id": "source_support_exactly_once"}, {"id": "batch_read_hash_split"}], "supportDDL": {"events": ["new_field_event", "alter_field_name_event", "alter_field_attributes_event", "drop_field_event"]}, "connection": {"type": "object", "properties": {"thinType": {"type": "string", "title": "${thinType}", "enum": [{"label": "SID", "value": "SID", "disabled": false}, {"label": "SERVICE NAME", "value": "SERVICE_NAME", "disabled": false}], "default": "SID", "required": true, "x-decorator": "FormItem", "x-component": "Radio.Group", "x-component-props": {"optionType": "button"}, "x-decorator-props": {"tooltip": "${thinTypeTip}"}, "x-reactions": [{"target": "*(host,port)", "fulfill": {"state": {"visible": "{{$self.value==='SID' || $self.value==='SERVICE_NAME'}}"}}}, {"target": "*(sid)", "fulfill": {"state": {"visible": "{{$self.value==='SID'}}"}}}, {"target": "*(database)", "fulfill": {"state": {"visible": "{{$self.value==='SERVICE_NAME'}}"}}}, {"target": "*(tnsName)", "fulfill": {"state": {"visible": "{{$self.value==='TNS_NAME'}}"}}}], "x-index": 10}, "host": {"required": true, "type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-decorator-props": {"tooltip": "${hostTip}"}, "x-index": 20}, "port": {"required": true, "type": "string", "title": "${port}", "default": 1521, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-decorator-props": {"tooltip": "${portTip}"}, "x-index": 30}, "sid": {"required": true, "type": "string", "title": "${sid}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_sid", "x-index": 40}, "database": {"required": true, "type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 50}, "tnsName": {"required": true, "type": "string", "title": "${tnsName}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_tnsName", "x-index": 60}, "schema": {"required": true, "type": "string", "title": "${schema}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_owner", "x-decorator-props": {"tooltip": "${schemaTip}"}, "x-index": 70}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 90}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 100}, "logPluginName": {"required": true, "type": "string", "title": "${logPluginName}", "default": "logMiner", "x-decorator": "FormItem", "x-component": "Select", "apiServerKey": "logPlugin", "x-decorator-props": {"tooltip": "${logPluginNameTip}"}, "x-index": 130, "enum": [{"label": "logMiner", "value": "logMiner"}, {"label": "bridge", "value": "bridge"}], "x-reactions": [{"target": "*(standBy)", "fulfill": {"state": {"visible": "{{$self.value==='grpc'}}"}}}, {"target": "*(rawLogServerHost,rawLogServerPort)", "fulfill": {"state": {"visible": "{{$self.value!=='logMiner'}}"}}}]}, "rawLogServerHost": {"required": true, "type": "string", "title": "${rawLogServerHost}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "rawLogServerHost", "x-index": 140}, "rawLogServerPort": {"required": true, "type": "string", "default": 8190, "title": "${rawLogServerPort}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "rawLogServerPort", "x-index": 150}, "OPTIONAL_FIELDS": {"type": "void", "properties": {"extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "additionalString", "x-decorator-props": {"tooltip": "${extParamsTip}"}, "x-index": 80}, "loadTableComment": {"type": "boolean", "title": "${loadTableComment}", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "x-index": 90}, "multiTenant": {"type": "boolean", "title": "${multiTenant}", "x-decorator": "FormItem", "x-component": "Switch", "x-reactions": [{"target": "pdb", "fulfill": {"state": {"visible": "{{$self.value===true}}"}}}], "x-decorator-props": {"tooltip": "${multiTenantTip}"}, "x-index": 110}, "pdb": {"type": "string", "title": "PDB", "required": true, "x-decorator": "FormItem", "x-component": "Input", "x-index": 120}, "standBy": {"type": "boolean", "title": "${standBy}", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "x-index": 130}, "useSSL": {"type": "boolean", "title": "${useSSL}", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "x-index": 130, "x-reactions": [{"target": "*(ssl<PERSON><PERSON>,sslKeyPassword)", "fulfill": {"state": {"visible": "{{$self.value===true}}"}}}]}, "sslCert": {"type": "string", "title": "${sslCert}", "x-decorator": "FormItem", "x-component": "TextFileReader", "x-component-props": {"base64": true}, "x-decorator-props": {"tooltip": "${sslCertTip}"}, "apiServerKey": "sslCert", "fileNameField": "sslCertFile", "required": true, "x-index": 140}, "sslKeyPassword": {"type": "string", "title": "${sslKeyPassword}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "sslKeyPassword", "x-decorator-props": {"tooltip": "${sslKeyPasswordTip}"}, "required": true, "x-index": 150}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}], "x-decorator-props": {"tooltip": "${timezoneTip}"}, "x-index": 170}, "socketReadTimeOut": {"type": "string", "title": "${socketReadTimeOut}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_socketReadTimeOut", "default": 0, "x-decorator-props": {"tooltip": "${socketReadTimeOutTip}"}, "x-component-props": {"min": 0, "max": 600}, "x-index": 180}}}}}, "node": {"type": "object", "properties": {"closeNotNull": {"type": "boolean", "title": "${closeNotNull}", "default": true, "x-index": 1, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${closeNotNullTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "autoLog": {"type": "boolean", "title": "${autoLog}", "default": "{{$values.attrs.db_version === undefined || parseInt($values.attrs.db_version.match(/\\d+/)[0]) < 19 ? true:false}}", "x-index": 2, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${autoLogTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length && ($values.attrs.db_version === undefined || parseInt($values.attrs.db_version.match(/\\d+/)[0]) < 19 ? \"visible\":\"hidden\")}}"}}}], "apiServerKey": "node_autoLog"}, "fetchSize": {"required": true, "type": "string", "title": "${fetchSize}", "default": 100, "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "node_fetchSize", "x-index": 3, "x-decorator-props": {"tooltip": "${fetchSizeTooltip}", "min": 1}, "x-reactions": {"dependencies": ["$inputs", ".autoLog"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}}, "pollingInterval": {"required": true, "type": "string", "title": "${pollingInterval}", "default": 500, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 0, "max": 100000}, "x-index": 4, "x-decorator-props": {"tooltip": "${pollingIntervalTooltip}"}, "x-reactions": {"dependencies": ["$inputs", ".autoLog"], "fulfill": {"state": {"display": "{{!$deps[0].length && !$deps[1] ? \"visible\":\"hidden\"}}"}}}}, "racExactlyRead": {"type": "boolean", "title": "${racExactlyRead}", "default": false, "x-index": 5, "x-decorator": "FormItem", "x-component": "Switch", "x-reactions": {"dependencies": ["$inputs", ".autoLog"], "fulfill": {"state": {"display": "{{!$deps[0].length && !$deps[1] ? \"visible\":\"hidden\"}}"}}}}, "enableSyncLob": {"type": "boolean", "title": "${enableSyncLob}", "default": true, "x-index": 6, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${enableSyncLobTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "enableUniqueUpdate": {"type": "boolean", "title": "${enableUniqueUpdate}", "default": true, "x-index": 7, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${enableUniqueUpdateTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "bigTransactionSize": {"required": true, "type": "string", "title": "${bigTransactionSize}", "default": 10000, "x-index": 8, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 100, "max": 100000}, "x-decorator-props": {"tooltip": "${bigTransactionSizeTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "transactionAliveMinutes": {"required": true, "type": "string", "title": "${transactionAliveMinutes}", "default": 720, "x-index": 9, "x-decorator": "FormItem", "x-component": "InputNumber", "x-decorator-props": {"tooltip": "${transactionAliveMinutesTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "hashSplit": {"type": "boolean", "title": "${hashSplit}", "default": false, "x-index": 10, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${hashSplitTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "maxSplit": {"required": true, "type": "string", "title": "${maxSplit}", "default": 20, "x-index": 11, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 2, "max": 10000}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "batchReadThreadSize": {"required": true, "type": "string", "title": "${batchReadThreadSize}", "default": 4, "x-index": 12, "x-decorator": "FormItem", "x-component": "InputNumber", "x-component-props": {"min": 1, "max": 16}, "x-reactions": [{"dependencies": ["$inputs", ".hashSplit"], "fulfill": {"state": {"display": "{{!$deps[0].length && $deps[1] ? \"visible\":\"hidden\"}}"}}}]}, "enableFzsZip": {"type": "boolean", "title": "${enableFzsZip}", "default": false, "x-index": 13, "x-decorator": "FormItem", "x-component": "Switch", "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "fzsSocketTimeout": {"required": true, "type": "string", "title": "${fzsSocketTimeout}", "default": 100, "x-index": 14, "x-decorator": "FormItem", "x-component": "InputNumber", "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}}}}, "messages": {"default": "en_US", "en_US": {"doc": "docs/oracle_en_US.md", "thinType": "Connection mode", "thinTypeTip": "SID is mainly a unique identifier for a single database instance, and a cluster may consist of multiple different SID nodes. \nIt is recommended to use the SERVICE NAME, and specific information can be found in tnsname.ora", "sid": "SID", "host": "DB Address", "hostTip": "The address of the database, it can be an IP address or a domain name, for example: *************", "port": "Port", "portTip": "The port number of the database, the default port of Oracle is 1521", "database": "Service Name", "tnsName": "TNS Name", "schema": "<PERSON><PERSON><PERSON>", "schemaTip": "Each Oracle user corresponds to a default schema, which has the same name as the username and is capitalized by default", "extParams": "Connection String Params", "extParamsTip": "Additional connection parameters in URI, you can write according to personalized scenarios", "user": "user", "password": "password", "timezone": "Timezone for Datetime", "timezoneTip": "Specify the time zone, otherwise no time zone processing will be done", "multiTenant": "Multi-tenant", "multiTenantTip": "The multi tenant architecture allows Oracle databases to run as multi tenant container databases (CDBs). \nCDB includes pluggable databases (PDBs) created by zero, one, or more customers", "logPluginName": "logPluginName", "logPluginNameTip": "Oracle implements incremental collection, and currently available plugins mainly include the built-in LogMiner with limited performance, \nas well as those based on raw log mining, but additional deployment of raw log services is required", "rawLogServerHost": "grpc server host", "rawLogServerPort": "grpc server port", "loadTableComment": "Load Table Comment", "standBy": "Read log from slave", "useSSL": "Use SSL", "sslCert": "SSL Certificate(PEM)", "sslCertTip": "Oracle's Wallet generates certificates in P12 format, which need to be converted through openssl pkcs12 -clcerts -nokeys -out oracle_cert.pem -in ewallet.p12", "sslKeyPassword": "SSL Certificate Password", "sslKeyPasswordTip": "The password when generating the certificate of the wallet of Oracle is filled in the key", "socketReadTimeOut": "Socket ReadTimeOut (minutes)", "autoLog": "Continuous Miner", "autoLogTooltip": "Turn on when Oracle Version < 19c, latency is lower; Must Turn off when Oracle Version >= 19c", "fetchSize": "<PERSON>tch Size", "fetchSizeTooltip": "default 100, only available for Continuous Mine<PERSON>, if your database change frequently, make it bigger, else make smaller, recommend QPS/10, no more than 1000", "pollingInterval": "Polling Interval (ms)", "pollingIntervalTooltip": "only available for Manual Miner、 Raw Miner, set too small will cause a lot of LogMiner audit logs, set too large will cause a certain fixed latency", "racExactlyRead": "RAC Exactly Read", "concurrencyTooltip": "if your database qps is more than 10k and latency is increasing, make it bigger, no more than 8", "concurrency": "Concurrency", "enableSyncLob": "Enable sync LOB type(BLOB,CLOB,NCLOB)", "enableSyncLobTooltip": "Turning off the switch can improve performance, but LOB type resolution will be unreliable", "enableUniqueUpdate": "Association key update", "enableUniqueUpdateTooltip": "Turn off the switch to improve performance, but the update of the associated key will be ignored", "bigTransactionSize": "Large transaction event boundary", "bigTransactionSizeTooltip": "When the number of transaction DML events exceeds this value, it will enter the large transaction logic. Setting it too large may affect memory. After the large transaction, there will be local disk cache. When the task has an exception, pay attention to disk cleaning", "transactionAliveMinutes": "Uncommitted transaction lifetime (minutes)", "transactionAliveMinutesTooltip": "Transactions that have not been committed for a long time will cause each start/stop task to start mining from this transaction, which will affect performance. Therefore, transactions that have not been committed for a long time will be cleaned up", "closeNotNull": "Ignore <PERSON>", "closeNotNullTooltip": "When the switch is turned on, non empty restrictions of string type are discarded", "fzsFileAliveMinutes": "FZS file lifetime (minutes)", "fzsFileAliveMinutesTooltip": "When using the bridge bare log plugin, a large number of FZS files will be cached, and a retention time suitable for server and user needs needs needs to be set", "socketReadTimeOutTip": "Setting this parameter prevents unrestricted waiting for the database to return the result and prevents the database zombie connection. Generally, this parameter is not set. If Tapdata fails to read the database result and waits for it without limit, you can try to set this parameter. The default value is 0, indicating that this parameter is not set", "ddlSwitch": "DDL switch", "ddlSwitchTooltip": "When the switch is turned on, it can effectively prevent the problem of DDL synchronization when the LogMiner dictionary is not recognized", "hashSplit": "Hash split", "hashSplitTooltip": "When the switch is turned on, it can be sharded according to the hash value, suitable for large table full-stage sharded synchronization", "maxSplit": "Maximum number of splits", "batchReadThreadSize": "Batch read thread size", "fzsSocketTimeout": "Fzs Socket Timeout (seconds)", "enableFzsZip": "Enable Fzs Compression"}, "zh_CN": {"doc": "docs/oracle_zh_CN.md", "thinType": "连接方式", "thinTypeTip": "SID主要为单个数据库实例唯一标识，集群可能由多个不同SID节点组成，推荐使用SERVICE NAME，具体信息可以参照tnsname.ora", "sid": "SID", "host": "数据库地址", "hostTip": "数据库的地址，它可以是IP地址或者域名，例如：*************", "port": "端口", "portTip": "数据库的端口号，Oracle默认端口1521", "database": "Service Name", "tnsName": "实例名", "schema": "<PERSON><PERSON><PERSON>", "schemaTip": "Oracle一个用户对应一个缺省Schema，该Schema名和用户名相同，默认大写", "extParams": "其他连接串参数", "extParamsTip": "URI额外的连接参数，可以根据个性化场景书写", "user": "账号", "password": "密码", "timezone": "时间类型的时区", "timezoneTip": "指定时区，否则不做时区处理", "multiTenant": "多租户模式", "multiTenantTip": "多租户体系结构允许Oracle数据库作为多租户容器数据库(CDB)运行。 CDB包括零个、一个或多个客户创建的可插拔数据库(PDBs)", "logPluginName": "日志插件", "logPluginNameTip": "Oracle实现增量采集，目前可用插件主要有自带的性能受限的LogMiner，还有基于裸日志挖掘的，但需要另外部署裸日志服务", "rawLogServerHost": "裸日志服务器地址", "rawLogServerPort": "裸日志服务器端口", "loadTableComment": "加载表注释", "standBy": "从库只读日志", "useSSL": "使用SSL", "sslCert": "SSL证书(PEM)", "sslCertTip": "Oracle的Wallet生成证书是P12格式的，需要通过openssl pkcs12 -clcerts -nokeys -out oracle_cert.pem -in ewallet.p12转换", "sslKeyPassword": "SSL证书密码", "sslKeyPasswordTip": "SSL证书密码，Oracle的Wallet生成证书时填写的密钥", "socketReadTimeOut": "套接字超时时长（分钟）", "autoLog": "日志连续挖掘", "autoLogTooltip": "在 Oracle 版本低于 19c 时, 建议开启此开关, 延迟更低; 在 Oracle 版本大于等于 19c 时, 必须关闭此开关", "fetchSize": "日志积压数量", "fetchSizeTooltip": "仅对连续挖掘生效, 源端更新频率较低时, 选择较低的值, 延迟更低, 源端更新频率较高时, 选择较高的值, 吞吐更高, 一般可以按照源端数据变化 QPS/10 计算, 经验值在 1-1000 之间", "pollingInterval": "轮询间隔（毫秒）", "pollingIntervalTooltip": "仅对手动挖掘、裸日志生效, 轮询间隔设置过小会导致大量LogMiner审计日志，设置过大会导致一定的固定延迟", "racExactlyRead": "RAC精确一次读取", "concurrencyTooltip": "在关闭日志连续挖掘时生效, 以占用数据库 CPU 资源为代价, 提高日志挖掘速度, 建议在源端变更频率超过 1w 时打开, 最大不建议超过 8", "concurrency": "挖掘线程数", "enableSyncLob": "开启同步LOB类型(BLOB,CLOB,NCLOB)", "enableSyncLobTooltip": "关闭开关可以提升性能，但LOB类型解析将会不可靠", "enableUniqueUpdate": "关联键更新", "enableUniqueUpdateTooltip": "关闭开关可以提升性能，但关联键的更新会被忽略", "bigTransactionSize": "大事务事件界限", "bigTransactionSizeTooltip": "当事务DML事件数超过该数值会进入大事务逻辑，设置过大有可能会影响内存，大事务后会有本地磁盘缓存，在任务出现异常时需要注意磁盘清理", "transactionAliveMinutes": "未提交事务生命时长（分钟）", "transactionAliveMinutesTooltip": "长时间未提交的事务会导致每次启停任务将从该事务开始挖掘，会影响性能，因此超过该时长未提交的事务将被清理", "closeNotNull": "忽略NotNull", "closeNotNullTooltip": "开关打开时会将字符串类型的非空限制丢弃", "fzsFileAliveMinutes": "FZS文件生命时长（分钟）", "fzsFileAliveMinutesTooltip": "当使用bridge裸日志插件时，会缓存大量FZS文件，需要设置适合服务器和用户需求的留存时间", "socketReadTimeOutTip": "设置此参数可以预防无限制等待数据库返回结果，避免数据库僵尸连接。一般不进行设置，若出现Tapdata读取不了数据库结果并无限制等待，可以尝试设置此参数，默认值为0代表不设置", "ddlSwitch": "DDL开关", "ddlSwitchTooltip": "开关打开时可以有效防止DDL同步时LogMiner字典未识别问题", "hashSplit": "哈希分片", "hashSplitTooltip": "开关打开时，可以根据哈希值进行分片，适用于大表全量阶段分片同步", "maxSplit": "最大分片数", "batchReadThreadSize": "批量读取线程数", "fzsSocketTimeout": "Fzs Socket超时时间（秒）", "enableFzsZip": "启用Fzs压缩"}, "zh_TW": {"doc": "docs/oracle_zh_TW.md", "thinType": "連接方式", "thinTypeTip": "SID主要為單個數據庫實例唯一標識，集群可能由多個不同SID節點組成，推薦使用SERVICE NAME，具體信息可以參照tnsname.ora", "sid": "SID", "host": "數據庫地址", "hostTip": "數據庫的地址，它可以是IP地址或者域名，例如：*************", "port": "端口", "portTip": "數據庫的端口號，Oracle默認端口1521", "database": "Service Name", "tnsName": "實例名", "schema": "<PERSON><PERSON><PERSON>", "schemaTip": "Oracle一個用戶對應一個缺省Schema，該Schema名和用戶名相同，默認大寫", "extParams": "其他連接串參數", "extParamsTip": "URI額外的連接參數，可以根據個性化場景書寫", "user": "賬號", "password": "密碼", "timezone": "时间类型的时区", "timezoneTip": "指定時區，否則不做時區處理", "multiTenant": "多租戶模式", "multiTenantTip": "多租戶體系結構允許Oracle數據庫作為多租戶容器數據庫(CDB)運行。 CDB包括零個、一個或多個客戶創建的可插拔數據庫(PDBs)", "logPluginName": "日誌插件", "logPluginNameTip": "Oracle實現增量採集，現時可用挿件主要有自帶的效能受限的LogMiner，還有基於裸日誌挖掘的，但需要另外部署裸日誌服務", "rawLogServerHost": "裸日誌服務器信息", "rawLogServerPort": "裸日誌服務器端口", "loadTableComment": "加載表註釋", "standBy": "從庫只讀日誌", "useSSL": "使用SSL", "sslCert": "SSL證書(PEM)", "sslCertTip": "Oracle的Wallet生成證書是P12格式的，需要通過openssl pkcs12 -clcerts -nokeys -out oracle_cert.pem -in ewallet.p12轉換", "sslKeyPassword": "SSL證書密碼", "sslKeyPasswordTip": "SSL證書密碼，Oracle的Wallet生成證書時填寫的密鑰", "socketReadTimeOut": "套接字超時时长（分鐘）", "autoLog": "連續挖掘", "autoLogTooltip": "在 Oracle 版本低於 19c 時, 建議開啟此開關, 延遲更低; 在 Oracle 版本大於等於 19c 時, 必須關閉此開關", "fetchSize": "挖掘積壓數量", "fetchSizeTooltip": "僅對連續挖掘生效, 源端更新頻率較低時, 選擇較低的值, 延遲更低, 源端更新頻率較高時, 選擇較高的值, 吞吐更高, 一般可以按照源端數據變化 QPS/10 計算, 經驗值在 1-1000 之間", "pollingInterval": "輪詢間隔（毫秒）", "pollingIntervalTooltip": "僅對手動挖掘生效、裸日志, 輪詢間隔設置過小會導致大量LogMiner審計日誌，設置過大會導致一定的固定延遲", "racExactlyRead": "RAC精確一次讀取", "concurrencyTooltip": "在關閉日誌連續挖掘時生效, 以佔用數據庫 CPU 資源為代價, 提高日誌挖掘速度, 建議在源端變更頻率超過 1w 時打開, 最大不建議超過 8", "concurrency": "挖掘線程數", "enableSyncLob": "開啟同步LOB類型(BLOB,CLOB,NCLOB)", "enableSyncLobTooltip": "關閉開關可以提升效能，但LOB類型解析將會不可靠", "enableUniqueUpdate": "關聯鍵更新", "enableUniqueUpdateTooltip": "關閉開關可以提升效能，但關聯鍵的更新會被忽略", "bigTransactionSize": "大事務事件界限", "bigTransactionSizeTooltip": "當事務DML事件數超過該數值會進入大事務邏輯，設置過大有可能會影響內存，大事務後會有本地磁盤緩存，在任務出現異常時需要注意磁盤清理", "transactionAliveMinutes": "未提交事務生命時長（分鐘）", "transactionAliveMinutesTooltip": "長時間未提交的事務會導致每次啟停任務將從該事務開始挖掘，會影響效能，囙此超過該時長未提交的事務將被清理", "closeNotNull": "忽略NotNull", "closeNotNullTooltip": "開關打開時會將字串類型的非空限制丟棄", "fzsFileAliveMinutes": "FZS文件生命時長（分鐘）", "fzsFileAliveMinutesTooltip": "當使用bridge裸日誌插件時，會緩存大量FZS文件，需要設定適合伺服器和使用者需求的留存時間", "socketReadTimeOutTip": "設置此參數可以預防無限制等待數據庫返回結果，避免數據庫僵尸連接。一般不進行設置，若出現Tapdata讀取不了數據庫結果並無限制等待，可以嘗試設置此參數，默認值為0代表不設置", "ddlSwitch": "DDL開關", "ddlSwitchTooltip": "開關打開時可以有效防止DDL同步時LogMiner字典未識別問題", "hashSplit": "哈希分片", "hashSplitTooltip": "開關打開時，可以根據哈希值進行分片，適用於大表全量階段分片同步", "maxSplit": "最大分片數", "batchReadThreadSize": "批量讀取線程數", "fzsSocketTimeout": "Fzs Socket超時時間（秒）", "enableFzsZip": "啟用Fzs壓縮"}}, "dataTypes": {"CHAR[($byte)]": {"byte": 2000, "priority": 1, "defaultByte": 1, "fixed": true, "to": "TapString"}, "NCHAR[($byte)]": {"byte": 1000, "priority": 2, "defaultByte": 1, "byteRatio": 3, "queryOnly": true, "fixed": true, "to": "TapString"}, "VARCHAR2[($byte)]": {"byte": 4000, "priority": 1, "preferByte": 2000, "to": "TapString"}, "NVARCHAR2[($byte)]": {"byte": 2000, "priority": 2, "queryOnly": true, "byteRatio": 3, "to": "TapString"}, "INTEGER": {"defaultPrecision": 38, "priority": 1, "to": "TapNumber"}, "NUMBER(*,$scale)": {"scale": [-84, 127], "precision": [1, 38], "fixed": true, "preferPrecision": 20, "defaultPrecision": 38, "preferScale": 8, "defaultScale": 0, "priority": 1, "to": "TapNumber"}, "NUMBER[($precision,$scale)]": {"precision": [1, 38], "scale": [-84, 127], "fixed": true, "preferPrecision": 20, "defaultPrecision": 38, "preferScale": 8, "defaultScale": 0, "priority": 1, "to": "TapNumber"}, "NUMBER($precision)": {"precision": [1, 38], "fixed": true, "preferPrecision": 20, "defaultPrecision": 38, "priority": 1, "to": "TapNumber"}, "FLOAT[($precision)]": {"precision": [1, 126], "scale": 125, "preferScale": 8, "fixed": false, "defaultPrecision": 126, "priority": 2, "to": "TapNumber"}, "BINARY_FLOAT": {"value": ["-3.402823466E+38", "3.402823466E+38"], "scale": 37, "preferScale": 8, "preferPrecision": 12, "priority": 3, "queryOnly": true, "to": "TapNumber"}, "BINARY_DOUBLE": {"value": ["-1.7976931348623157E+308", "1.7976931348623157E+308"], "preferPrecision": 20, "preferScale": 8, "scale": 307, "priority": 3, "to": "TapNumber"}, "DATE": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "defaultFraction": 0, "pattern": "yyyy-MM-dd HH:mm:ss", "priority": 1, "to": "TapDateTime"}, "TIMESTAMP[($fraction)]": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 9], "defaultFraction": 6, "withTimeZone": false, "priority": 2, "to": "TapDateTime"}, "TIMESTAMP[($fraction)] WITH TIME ZONE": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 9], "defaultFraction": 6, "priority": 2, "withTimeZone": true, "to": "TapDateTime"}, "TIMESTAMP[($fraction)] WITH LOCAL TIME ZONE": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss", "fraction": [0, 9], "withTimeZone": true, "defaultFraction": 6, "priority": 2, "to": "TapDateTime"}, "INTERVAL YEAR[($num)] TO MONTH": {"queryOnly": true, "to": "TapString"}, "INTERVAL DAY[($num)] TO SECOND[($num)]": {"queryOnly": true, "to": "TapString"}, "CLOB": {"byte": "4g", "pkEnablement": false, "priority": 2, "to": "TapString"}, "NCLOB": {"byte": "4g", "queryOnly": true, "priority": 3, "to": "TapString"}, "BLOB": {"byte": "4g", "pkEnablement": false, "priority": 2, "to": "TapBinary"}, "BFILE": {"queryOnly": true, "pkEnablement": false, "to": "TapBinary"}, "RAW($byte)": {"queryOnly": true, "to": "TapBinary"}, "LONG RAW": {"queryOnly": true, "to": "TapBinary"}, "LONG": {"queryOnly": true, "byte": "2g", "to": "TapString"}, "XMLTYPE": {"to": "TapXml"}}}