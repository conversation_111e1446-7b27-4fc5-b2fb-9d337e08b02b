package io.tapdata.connector.oracle.cdc.logminer;

import com.google.common.collect.Lists;
import io.tapdata.common.cdc.*;
import io.tapdata.common.ddl.DDLFactory;
import io.tapdata.common.ddl.type.DDLParserType;
import io.tapdata.common.ddl.wrapper.DDLWrapper;
import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.cdc.logminer.bean.RedoLog;
import io.tapdata.connector.oracle.cdc.logminer.handler.RawTypeHandler;
import io.tapdata.connector.oracle.cdc.logminer.handler.UnicodeStringColumnHandler;
import io.tapdata.connector.oracle.cdc.logminer.parser.CustomSQLRedoLogParser;
import io.tapdata.connector.oracle.cdc.logminer.parser.ParseSQLRedoLogParser;
import io.tapdata.connector.oracle.cdc.offset.OracleOffset;
import io.tapdata.connector.oracle.config.OracleConfig;
import io.tapdata.connector.oracle.ddl.ccj.OracleDDLWrapper;
import io.tapdata.constant.SqlConstant;
import io.tapdata.constant.TapLog;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.control.HeartbeatEvent;
import io.tapdata.entity.event.ddl.TapDDLEvent;
import io.tapdata.entity.event.ddl.TapDDLUnknownEvent;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;
import net.sf.jsqlparser.statement.alter.Alter;
import oracle.sql.BLOB;
import oracle.sql.CLOB;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.*;

public abstract class OracleLogMiner extends LogMiner implements ILogMiner {

    //oracle-timestamp
    // https://docs.oracle.com/cd/E16338_01/appdev.112/e13995/constant-values.html#oracle_jdbc_OracleTypes_TIMESTAMPTZ
    protected final static int TIMESTAMP_TZ_TYPE = -101;
    // https://docs.oracle.com/cd/E16338_01/appdev.112/e13995/constant-values.html#oracle_jdbc_OracleTypes_TIMESTAMPLTZ
    protected final static int TIMESTAMP_LTZ_TYPE = -102;
    protected final static int INTERVAL_YM_TYPE = -103;
    protected final static int INTERVAL_DS_TYPE = -104;

    protected static final String NLS_DATE_FORMAT = "ALTER SESSION SET NLS_DATE_FORMAT = 'DD-MM-YYYY HH24:MI:SS'";
    protected static final String NLS_NUMERIC_FORMAT = "ALTER SESSION SET NLS_NUMERIC_CHARACTERS = '.,'";
    protected static final String NLS_TIMESTAMP_FORMAT = "ALTER SESSION SET NLS_TIMESTAMP_FORMAT = 'YYYY-MM-DD HH24:MI:SS.FF'";
    protected static final String NLS_TIMESTAMP_TZ_FORMAT = "ALTER SESSION SET NLS_TIMESTAMP_TZ_FORMAT = 'YYYY-MM-DD HH24:MI:SS.FF TZH:TZM'";

    protected final DateTimeColumnHandler dateTimeColumnHandler;
    protected final OracleJdbcContext oracleJdbcContext;
    protected final Connection connection;
    protected final OracleConfig oracleConfig;
    protected Statement statement;
    protected PreparedStatement preparedStatement;
    protected ResultSet resultSet;
    protected final String version;
    protected long referenceTime = 0L;

    protected Map<String, Integer> columnTypeMap = new HashMap<>(); //Map<table.column, java.dataType>
    protected Map<String, String> dateTimeTypeMap = new HashMap<>(); //Map<table.column, db.dataType>
    protected final Map<Long, String> tableObjectId = new HashMap<>(); //for oracle-9i
    protected Map<String, Integer> timestampMap = new HashMap<>();

    protected OracleOffset oracleOffset;
    private Connection backFindLobConnection;
    protected final Map<String, String> cacheRedoLogContent = new HashMap<>();
    private final Map<String, PreparedStatement> backFindLobPsmtMap = new HashMap<>();
    protected Long lastEventTimestamp = 0L;
    protected ParseSQLRedoLogParser sqlParser;

    public OracleLogMiner(OracleJdbcContext oracleJdbcContext, String connectorId, Log tapLogger) throws SQLException {
        this.oracleJdbcContext = oracleJdbcContext;
        this.connection = oracleJdbcContext.getConnection();
        connection.setAutoCommit(true);
        this.connectorId = connectorId;
        this.tapLogger = tapLogger;
        oracleConfig = (OracleConfig) oracleJdbcContext.getConfig();
        this.setLargeTransactionUpperLimit(oracleConfig.getBigTransactionSize());
        this.sqlParser = new CustomSQLRedoLogParser();
        dateTimeColumnHandler = new DateTimeColumnHandler(TimeZone.getTimeZone("GMT" + oracleConfig.getTimezone()));
        version = oracleJdbcContext.queryVersion();
        ddlParserType = DDLParserType.ORACLE_CCJ_SQL_PARSER;
    }

    //init with pdk params
    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        makeOffset(offsetState);
        getColumnType();
    }

    //multiInit with pdk params
    @Override
    public void multiInit(List<ConnectionConfigWithTables> connectionConfigWithTables, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.multiInit(connectionConfigWithTables, tableMap, offsetState, recordSize, consumer);
        makeOffset(offsetState);
        multiGetColumnType();
    }

    //store dataType in Map
    protected void getColumnType() {
        columnTypeMap.clear();
        dateTimeTypeMap.clear();
        tableList.forEach(table -> {
            try {
                oracleJdbcContext.queryWithNext("SELECT * FROM \"" + oracleConfig.getSchema() + "\".\"" + table + "\"", resultSet -> {
                    ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
                    for (int i = 1; i <= resultSetMetaData.getColumnCount(); i++) {
                        int colType = resultSetMetaData.getColumnType(i);
                        columnTypeMap.put(table + "." + resultSetMetaData.getColumnName(i), colType);
                        if (colType == Types.DATE || colType == Types.TIME || colType == Types.TIMESTAMP) {
                            dateTimeTypeMap.put(table + "." + resultSetMetaData.getColumnName(i), resultSetMetaData.getColumnTypeName(i));
                        }
                    }
                });
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        });
    }

    protected void multiGetColumnType() {
        columnTypeMap.clear();
        dateTimeTypeMap.clear();
        schemaTableMap.forEach((schema, tables) -> tables.forEach(table -> {
            try {
                oracleJdbcContext.queryWithNext("SELECT * FROM \"" + schema + "\".\"" + table + "\"", resultSet -> {
                    ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
                    for (int i = 1; i <= resultSetMetaData.getColumnCount(); i++) {
                        int colType = resultSetMetaData.getColumnType(i);
                        columnTypeMap.put(schema + "." + table + "." + resultSetMetaData.getColumnName(i), colType);
                        if (colType == Types.DATE || colType == Types.TIME || colType == Types.TIMESTAMP) {
                            dateTimeTypeMap.put(schema + "." + table + "." + resultSetMetaData.getColumnName(i), resultSetMetaData.getColumnTypeName(i));
                        }
                    }
                });
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        }));
    }

    /**
     * find current scn of database
     *
     * @return scn Long
     * @throws SQLException SQLException
     */
    protected long findCurrentScn() throws SQLException {
        AtomicLong currentScn = new AtomicLong();
        String sql = version.equals("9i") ? CHECK_CURRENT_SCN_9I : CHECK_CURRENT_SCN;
        oracleJdbcContext.queryWithNext(sql, resultSet -> currentScn.set(resultSet.getLong(1)));
        return currentScn.get();
    }

    protected long findScnFromTimestamp(long timestamp) throws SQLException {
        AtomicLong scn = new AtomicLong();
        oracleJdbcContext.queryWithNext(String.format(TIMESTAMP_TO_SCN, timestamp), resultSet -> scn.set(resultSet.getLong(1)));
        return scn.get();
    }

    protected void makeOffset(Object offsetState) throws SQLException {
        if (EmptyKit.isNull(offsetState)) {
            oracleOffset = new OracleOffset();
            long currentScn = findCurrentScn();
            oracleOffset.setLastScn(currentScn);
            oracleOffset.setPendingScn(currentScn);
        } else {
            oracleOffset = (OracleOffset) offsetState;
            if (EmptyKit.isNull(oracleOffset.getLastScn())) {
                if (EmptyKit.isNotNull(oracleOffset.getTimestamp())) {
                    if (System.currentTimeMillis() < oracleOffset.getTimestamp()) {
                        throw new RuntimeException("Timestamp can not be later than now!");
                    }
                    long scn = findScnFromTimestamp(oracleOffset.getTimestamp());
                    oracleOffset.setLastScn(scn);
                    oracleOffset.setPendingScn(scn);
                } else {
                    long currentScn = findCurrentScn();
                    oracleOffset.setLastScn(currentScn);
                    oracleOffset.setPendingScn(currentScn);
                }
            } else if (EmptyKit.isNull(oracleOffset.getPendingScn())) {
                oracleOffset.setPendingScn(oracleOffset.getLastScn());
            }
        }
    }

    protected RedoLog firstOnlineRedoLog(long scn) throws Throwable {
        AtomicReference<RedoLog> redoLog = new AtomicReference<>();
        boolean useOldVersionSql = StringUtils.equalsAnyIgnoreCase(version, "9i", "10g");
        String firstOnlineSQL = useOldVersionSql ? GET_FIRST_ONLINE_REDO_LOG_FILE_FOR_10G_AND_9I : GET_FIRST_ONLINE_REDO_LOG_FILE;
        if (scn > 0) {
            firstOnlineSQL = useOldVersionSql ? String.format(GET_FIRST_ONLINE_REDO_LOG_FILE_BY_SCN_FOR_10G_AND_9I, scn) : String.format(GET_FIRST_ONLINE_REDO_LOG_FILE_BY_SCN, scn);
        }
        oracleJdbcContext.queryWithNext(firstOnlineSQL, resultSet -> redoLog.set(RedoLog.onlineLog(resultSet)));
        return redoLog.get();
    }

    protected void setSession() throws SQLException {
        statement = connection.createStatement();
        if (EmptyKit.isNotBlank(oracleConfig.getPdb())) {
            tapLogger.info("database is containerised, switching...");
            statement.execute(SWITCH_TO_CDB_ROOT);
        }
        statement.execute(NLS_DATE_FORMAT);
        statement.execute(NLS_TIMESTAMP_FORMAT);
        statement.execute(NLS_TIMESTAMP_TZ_FORMAT);
        statement.execute(NLS_NUMERIC_FORMAT);
    }

    protected void initRedoLogQueueAndThread() {
        if (redoLogConsumerThreadPool == null) {
            redoLogConsumerThreadPool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
            redoLogConsumerThreadPool.submit(() -> {
                RedoLogContent redoLogContent = null;
                int commitCount = 0;
                long lastTimestamp = System.currentTimeMillis();
                while (isRunning.get()) {
                    while (ddlStop.get()) {
                        TapSimplify.sleep(1000);
                    }
                    try {
                        redoLogContent = logQueue.poll(1, TimeUnit.SECONDS);
                        if (redoLogContent == null) {
                            continue;
                        }
                        //commit事件过多导致延迟增加，每100个非追踪commit事件才会放行一次心跳
                        if (redoLogContent.getOperationCode() == 7 && !transactionBucket.containsKey(redoLogContent.getXid())) {
                            commitCount++;
                            if (commitCount > 100 || (System.currentTimeMillis() - lastTimestamp >= 1000 * 3)) {
                                commitCount = 0;
                                lastTimestamp = System.currentTimeMillis();
                            } else {
                                continue;
                            }
                        }
                    } catch (Exception e) {
                        threadException.set(e);
                    }
                    try {
                        // parse sql
                        if (canParse(redoLogContent)) {
                            RedoLogContent.OperationEnum originOperation = RedoLogContent.OperationEnum.fromOperationCode(redoLogContent.getOperationCode());
                            RedoLogContent.OperationEnum operationEnum = originOperation;
                            if (operationEnum == RedoLogContent.OperationEnum.DELETE) {
                                operationEnum = RedoLogContent.OperationEnum.INSERT;
                                try {
                                    redoLogContent.setRedoRecord(sqlParser.parseSQL(redoLogContent.getSqlUndo(), operationEnum));
                                } catch (Exception e) {
                                    threadException.set(new RuntimeException("parse failed, redoLogContent: " + redoLogContent, e));
                                }
                            } else {
                                try {
                                    redoLogContent.setRedoRecord(sqlParser.parseSQL(redoLogContent.getSqlRedo(), operationEnum));
                                } catch (Exception e) {
                                    threadException.set(new RuntimeException("parse failed, redoLogContent: " + redoLogContent, e));
                                }
                                if (oracleConfig.getEnableUniqueUpdate() && StringUtils.isNotBlank(redoLogContent.getSqlUndo()) && operationEnum == RedoLogContent.OperationEnum.UPDATE) {
                                    try {
                                        redoLogContent.setUndoRecord(sqlParser.parseSQL(redoLogContent.getSqlUndo(), operationEnum));
                                    } catch (Exception e) {
                                        threadException.set(new RuntimeException("parse failed, redoLogContent: " + redoLogContent, e));
                                    }
                                }
                            }
                            convertStringToObject(redoLogContent);
//                        String mongodbBefore = context.getSettingService().getString("mongodb.before");
//                        if (
//                                redoLogParser.needParseUndo(redoLogContent.getOperation(),
//                                        redoLogContent.getSqlUndo(),
//                                        mongodbBefore,
//                                        context.getJobTargetConn() != null && context.getJobTargetConn().isSupportUpdatePk(),
//                                        context.getJob().getNoPrimaryKey()
//                                )
//                        ) {
//                            redoLogContent.setUndoRecord(redoLogParser.parseSQL(redoLogContent.getSqlUndo(), RedoLogContent.OperationEnum.UPDATE));
//                        }
                        }
                        // process and callback
                        processOrBuffRedoLogContent(redoLogContent, this::sendTransaction);

                    } catch (Throwable e) {
                        threadException.set(e);
                        consumer.streamReadEnded();
                    }
                }
            });
            redoLogConsumerThreadPool.submit(() -> {
                try {
                    while (isRunning.get()) {
                        Iterator<String> iterator = transactionBucket.keySet().iterator();
                        while (iterator.hasNext()) {
                            String xid = iterator.next();
                            LogTransaction transaction = transactionBucket.get(xid);
                            if (lastEventTimestamp - transaction.getFirstTimestamp() < oracleConfig.getTransactionAliveMinutes() * 60 * 1000L) {
                                break;
                            } else {
                                tapLogger.warn("Uncommitted transaction {} with {} events will be dropped", xid, transaction.getSize());
                                transaction.clearRedoLogContents();
                                iterator.remove();
                            }
                        }
                        int sleep = 60;
                        try {
                            while (isRunning.get() && (sleep-- > 0)) {
                                TapSimplify.sleep(1000);
                            }
                        } catch (Exception ignore) {
                        }
                    }
                } catch (Exception e) {
                    threadException.set(e);
                }
            });
        }
    }

    protected void handlerGrpc(RedoLogContent redoLogContent) {
        if (EmptyKit.isEmpty(redoLogContent.getRedoRecord())) {
            return;
        }
        String table = redoLogContent.getTableName();
        if (withSchema) {
            table = redoLogContent.getSegOwner() + "." + table;
        }
        handleRecord(table, redoLogContent.getRedoRecord());
        handleRecord(table, redoLogContent.getUndoRecord());
    }

    private void handleRecord(String table, Map<String, Object> record) {
        for (Map.Entry<String, Object> objectEntry : record.entrySet()) {
            Integer columnType = columnTypeMap.get(table + "." + objectEntry.getKey());
            if (EmptyKit.isNotNull(columnType) && columnType == Types.NUMERIC && objectEntry.getValue() instanceof String) {
                objectEntry.setValue(new BigDecimal((String) objectEntry.getValue()));
            }
            if (timestampMap.containsKey(table + "." + objectEntry.getKey())) {
                Integer fraction = timestampMap.get(table + "." + objectEntry.getKey());
                if (EmptyKit.isNull(objectEntry.getValue())) {
                    continue;
                }
                if (objectEntry.getValue() instanceof String) {
                    String timestampWithTZ = (String) objectEntry.getValue();
                    objectEntry.setValue(Long.parseLong(timestampWithTZ.substring(0, timestampWithTZ.indexOf("::"))) / ((Double) Math.pow(10, 9 - fraction)).longValue());
                } else {
                    objectEntry.setValue((Long) objectEntry.getValue() / ((Double) Math.pow(10, 9 - fraction)).longValue());
                }
            }
        }
    }

    /**
     * convert log redo string to Object(jdbc)
     *
     * @param redoLogContent oracle log content
     */
    protected void convertStringToObject(RedoLogContent redoLogContent) {
        String table = redoLogContent.getTableName();
        String schema;
        if (withSchema) {
            schema = redoLogContent.getSegOwner();
            table = schema + "." + table;
        }
        //lob类型反查调整位置，需要收到commit后进行，ROWID
        if (EmptyKit.isNotNull(redoLogContent.getRedoRecord())) {
            for (Map.Entry<String, Object> stringObjectEntry : redoLogContent.getRedoRecord().entrySet()) {
                parseKeyValue(table, stringObjectEntry);
            }
        }
        if (EmptyKit.isNotNull(redoLogContent.getUndoRecord())) {
            for (Map.Entry<String, Object> stringObjectEntry : redoLogContent.getUndoRecord().entrySet()) {
                parseKeyValue(table, stringObjectEntry);
            }
        }
    }

    protected void parseKeyValue(String table, Map.Entry<String, Object> stringObjectEntry) {
        Object value = stringObjectEntry.getValue();
        String column = stringObjectEntry.getKey();
        String tableAndColumn = table + "." + column;
        Integer columnType = columnTypeMap.get(tableAndColumn);
        if (EmptyKit.isNull(value) || !(value instanceof String) || EmptyKit.isNull(columnType)) {
            return;
        }
        switch (columnType) {
            case Types.BIGINT:
                stringObjectEntry.setValue(new BigDecimal((String) value).longValue());
                break;
            case Types.BINARY:
            case Types.LONGVARBINARY:
            case Types.VARBINARY:
                try {
                    stringObjectEntry.setValue(RawTypeHandler.parseRaw((String) value));
                } catch (DecoderException e) {
                    tapLogger.warn(TapLog.W_CONN_LOG_0014.getMsg(), value, columnType, e.getMessage());
                }
                break;
            case Types.BIT:
            case Types.BOOLEAN:
                stringObjectEntry.setValue(Boolean.valueOf((String) value));
                break;
            case Types.CHAR:
            case Types.LONGNVARCHAR:
            case Types.LONGVARCHAR:
            case Types.VARCHAR:
            case Types.ROWID:
            case Types.ARRAY:
            case Types.DATALINK:
            case Types.DISTINCT:
            case Types.JAVA_OBJECT:
            case Types.NULL:
            case Types.OTHER:
            case Types.REF:
            case Types.REF_CURSOR:
            case Types.SQLXML:
            case Types.STRUCT:
            case Types.TIME_WITH_TIMEZONE:
                break;
            case Types.NCHAR:
            case Types.NVARCHAR:
                stringObjectEntry.setValue(UnicodeStringColumnHandler.getUnicdeoString((String) value));
                break;
            case Types.DECIMAL:
            case Types.NUMERIC:
            case Types.DOUBLE:
                stringObjectEntry.setValue(new BigDecimal((String) value));
                break;
            case Types.FLOAT:
            case Types.REAL:
                stringObjectEntry.setValue(new BigDecimal((String) value).floatValue());
                break;
            case Types.INTEGER:
                stringObjectEntry.setValue(new BigDecimal((String) value).intValue());
                break;
            case Types.SMALLINT:
            case Types.TINYINT:
                stringObjectEntry.setValue(new BigDecimal((String) value).shortValue());
                break;
            case Types.DATE:
            case Types.TIME:
            case Types.TIMESTAMP:
                String actualType = dateTimeTypeMap.get(tableAndColumn);
                if (StringUtils.contains((CharSequence) value, "::")) {
                    stringObjectEntry.setValue(dateTimeColumnHandler.getTimestamp(value, actualType));
                } else {
                    // For whatever reason, Oracle returns all the date/time/timestamp fields as the same type, so additional
                    // logic is required to accurately parse the type
                    stringObjectEntry.setValue(dateTimeColumnHandler.getDateTimeStampField((String) value, actualType, tableAndColumn));
                }
                break;
            case Types.TIMESTAMP_WITH_TIMEZONE:
            case TIMESTAMP_TZ_TYPE:
                String tzDateType = dateTimeTypeMap.get(tableAndColumn);
                if (StringUtils.contains((CharSequence) value, "::")) {
                    stringObjectEntry.setValue(dateTimeColumnHandler.getTimestamp(value, tzDateType));
                } else {
                    stringObjectEntry.setValue(dateTimeColumnHandler.getTimestampWithTimezoneField((String) value, tableAndColumn));
                }
                break;
            case TIMESTAMP_LTZ_TYPE:
                String ltzDateType = dateTimeTypeMap.get(tableAndColumn);
                if (StringUtils.contains((CharSequence) value, "::")) {
                    stringObjectEntry.setValue(dateTimeColumnHandler.getTimestamp(value, ltzDateType));
                } else {
                    stringObjectEntry.setValue(dateTimeColumnHandler.getTimestampWithLocalTimezone((String) value, tableAndColumn));
                }
                break;
            case INTERVAL_YM_TYPE:
                stringObjectEntry.setValue(unwrap((String) value, "TO_YMINTERVAL\\('(.*)'\\)"));
                break;
            case INTERVAL_DS_TYPE:
                stringObjectEntry.setValue(unwrap((String) value, "TO_DSINTERVAL\\('(.*)'\\)"));
                break;
            case Types.BLOB:
                if ("EMPTY_BLOB()".equals(value)) {
                    stringObjectEntry.setValue(null);
                }
                break;
            case Types.CLOB:
            case Types.NCLOB:
                if ("EMPTY_CLOB()".equals(value)) {
                    stringObjectEntry.setValue(null);
                }
                break;
        }
    }

    private String unwrap(String value, String regx) {
        if (value == null) {
            return null;
        }
        Matcher matcher = Pattern.compile(regx).matcher(value);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return value;
    }

    protected boolean canParse(RedoLogContent redoLogContent) {
        if (redoLogContent == null) {
            return false;
        }
        if (redoLogContent.getUndoRecord() != null || redoLogContent.getRedoRecord() != null) {
            return false;
        }
        switch (redoLogContent.getOperation()) {
            case SqlConstant.REDO_LOG_OPERATION_LOB_TRIM:
            case SqlConstant.REDO_LOG_OPERATION_LOB_WRITE:
            case SqlConstant.REDO_LOG_OPERATION_SEL_LOB_LOCATOR:
            case "INTERNAL":
                return false;
            default:
                break;
        }
        String sqlRedo = redoLogContent.getSqlRedo();
        String sqlUndo = redoLogContent.getSqlUndo();
        if (StringUtils.isAllBlank(sqlRedo, sqlUndo)) {
            return false;
        }
        String operation = redoLogContent.getOperation();
        if (!StringUtils.equalsAny(operation,
                SqlConstant.REDO_LOG_OPERATION_INSERT,
                SqlConstant.REDO_LOG_OPERATION_UPDATE,
                SqlConstant.REDO_LOG_OPERATION_DELETE)) {
            return false;
        }
        return !StringUtils.equalsAny(operation, SqlConstant.REDO_LOG_OPERATION_DELETE)
                || !StringUtils.isEmpty(redoLogContent.getSqlUndo());
    }

    protected void ddlFlush() {
        if (withSchema) {
            multiGetColumnType();
            multiMakeLobTables();
        } else {
            getColumnType();
            makeLobTables();
        }
    }

    @Override
    protected void submitEvent(RedoLogContent redoLogContent, List<TapEvent> eventList) {
        OracleOffset oracleOffset = new OracleOffset();
        if (EmptyKit.isNull(redoLogContent)) {
            return;
        }
        oracleOffset.setLastScn(redoLogContent.getScn());
        Iterator<LogTransaction> iterator = transactionBucket.values().iterator();
        if (iterator.hasNext()) {
            oracleOffset.setPendingScn(iterator.next().getScn());
        } else {
            oracleOffset.setPendingScn(redoLogContent.getScn());
        }
        oracleOffset.setTimestamp(redoLogContent.getTimestamp().getTime());
        if (eventList.size() > 0) {
            consumer.accept(eventList, oracleOffset);
        } else {
            consumer.accept(Collections.singletonList(new HeartbeatEvent().init().referenceTime(redoLogContent.getTimestamp().getTime())), oracleOffset);
        }
    }

    @Override
    protected void batchCreateEvents(List<RedoLogContent> redoLogContentList, AtomicReference<List<TapEvent>> eventList, AtomicReference<RedoLogContent> lastRedoLogContent, LogTransaction logTransaction) {
        boolean hasPartRollback = EmptyKit.isNotEmpty(logTransaction.getPartRollbackMap());
        for (RedoLogContent redoLogContent : redoLogContentList) {
            referenceTime = Math.max(redoLogContent.getTimestamp().getTime(), referenceTime);
            String exactlyOnceId = redoLogContent.getScn() + "_" + redoLogContent.getRsId() + "_" + redoLogContent.getSsn();
            if ("DDL".equals(Objects.requireNonNull(redoLogContent).getOperation())) {
                try {
                    ddlStop.set(true);
                    TapSimplify.sleep(5000);
                    ddlFlush();
                } catch (Throwable e) {
                    threadException.set(e);
                } finally {
                    ddlStop.set(false);
                }
                try {
                    DDLFactory.ddlToTapDDLEvent(ddlParserType, redoLogContent.getSqlRedo(),
                            DDL_WRAPPER_CONFIG,
                            tableMap,
                            tapDDLEvent -> {
                                tapDDLEvent.setTime(System.currentTimeMillis());
                                tapDDLEvent.setReferenceTime(referenceTime);
                                tapDDLEvent.setExactlyOnceId(exactlyOnceId);
                                tapDDLEvent.setOriginDDL(redoLogContent.getSqlRedo());
                                eventList.get().add(tapDDLEvent);
                            }, (ddl, wrapper) -> addSchemaForDDLEvent(redoLogContent, ddl, wrapper)
                    );
                } catch (Throwable e) {
                    TapDDLEvent tapDDLEvent = new TapDDLUnknownEvent();
                    tapDDLEvent.setTime(System.currentTimeMillis());
                    tapDDLEvent.setReferenceTime(referenceTime);
                    tapDDLEvent.setOriginDDL(redoLogContent.getSqlRedo());
                    eventList.get().add(tapDDLEvent);
//                    tapLogger.warn("DDL parse failed, [{}]", redoLogContent.getSqlRedo());
                }
            } else {
                if (EmptyKit.isNull(Objects.requireNonNull(redoLogContent).getRedoRecord())) {
                    lastRedoLogContent.set(redoLogContent);
                    continue;
                }
                if (hasPartRollback) {
                    String rollbackKey = redoLogContent.generateRollbackKey();
                    if (logTransaction.getPartRollbackMap().containsKey(rollbackKey)) {
                        logTransaction.decreasePartRollback(rollbackKey);
                        tapLogger.info("Found {} row was undo by row id {} on the same transaction, rollback key {} undo event {}", redoLogContent.getOperation(), redoLogContent.getRowId(), rollbackKey, redoLogContent);
                        lastRedoLogContent.set(redoLogContent);
                        continue;
                    }
                }
                String table = redoLogContent.getTableName();
                if (lobTables.containsKey(table)) {
                    //过滤lob的额外update操作
                    if (EmptyKit.isNotNull(lastRedoLogContent.get())
                            && lastRedoLogContent.get().getSequence() == 1
                            && lastRedoLogContent.get().getRowId().equals("AAAAAAAAAAAAAAAAAA")
                            && EmptyKit.isNotNull(lastRedoLogContent.get().getSqlRedo())
                            && lastRedoLogContent.get().getSqlRedo().contains("EMPTY_")
                            && redoLogContent.getOperationCode() == 3
                            && redoLogContent.getSequence() > 1) {
                        continue;
                    }
                    if (oracleConfig.getEnableSyncLob()) {
                        try {
                            Collection<String> primaryKeys = lobTables.get(table).primaryKeys(true);
                            Collection<String> unique = EmptyKit.isNotEmpty(primaryKeys) ? primaryKeys : Collections.singletonList("ROWID");
                            Map<String, Object> redo = redoLogContent.getRedoRecord();
                            if (EmptyKit.isNull(backFindLobConnection) || !backFindLobConnection.isValid(3)) {
                                if (EmptyKit.isNotNull(backFindLobConnection)) {
                                    backFindLobConnection.close();
                                }
                                backFindLobConnection = oracleJdbcContext.getConnection();
                            }
                            PreparedStatement backFindLobPsmt = backFindLobPsmtMap.get(table);
                            if (EmptyKit.isNull(backFindLobPsmt)) {
                                backFindLobPsmt = backFindLobConnection.prepareStatement(
                                        "SELECT * FROM \"" + redoLogContent.getSegOwner() + "\".\"" + redoLogContent.getTableName() + "\" WHERE " + unique.stream().map(v -> "\"" + v + "\"=?").collect(Collectors.joining(" AND "))
                                );
                                backFindLobPsmtMap.put(table, backFindLobPsmt);
                            } else {
                                backFindLobPsmt.clearParameters();
                            }
                            int pos = 1;
                            for (String field : unique) {
                                backFindLobPsmt.setObject(pos++, redo.get(field));
                            }
                            try (ResultSet rs = backFindLobPsmt.executeQuery()) {
                                if (rs.next()) {
                                    DataMap dataMap = DbKit.getRowFromResultSet(rs, DbKit.getColumnsFromResultSet(rs));
                                    if (EmptyKit.isNotEmpty(dataMap)) {
                                        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                                            Object o = entry.getValue();
                                            if (o instanceof Timestamp && !oracleConfig.getOldVersionTimezone()) {
                                                entry.setValue(((Timestamp) o).toLocalDateTime().minusHours(oracleConfig.getZoneOffsetHour()));
                                            } else if (o instanceof CLOB) {
                                                entry.setValue(((CLOB) entry.getValue()).stringValue());
                                            } else if (o instanceof BLOB) {
                                                entry.setValue(DbKit.blobToBytes((BLOB) o));
                                            }
                                        }
                                    }
                                    redoLogContent.setRedoRecord(dataMap);
                                } else {
                                    if (EmptyKit.isNotNull(redoLogContent.getRedoRecord())) {
                                        redoLogContent.getRedoRecord().remove("ROWID");
                                        for (Map.Entry<String, Object> stringObjectEntry : redoLogContent.getRedoRecord().entrySet()) {
                                            String column = stringObjectEntry.getKey();
                                            String tableAndColumn = table + "." + column;
                                            Integer columnType = columnTypeMap.get(tableAndColumn);
                                            if (EmptyKit.isNull(columnType)) {
                                                continue;
                                            }
                                            switch (columnType) {
                                                case Types.BLOB:
                                                case Types.CLOB:
                                                case Types.NCLOB:
                                                case Types.LONGVARBINARY:
                                                    stringObjectEntry.setValue(null);
                                                    continue;
                                            }
                                            parseKeyValue(table, stringObjectEntry);
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            threadException.set(e);
                        }
                    }
                }
                Optional.ofNullable(redoLogContent.getRedoRecord()).ifPresent(record -> record.remove("ROWID"));
                Optional.ofNullable(redoLogContent.getUndoRecord()).ifPresent(record -> record.remove("ROWID"));
                Map<String, Object> streamOffset = new HashMap<>();
                streamOffset.put("scn", redoLogContent.getScn());
                streamOffset.put("commitTime", redoLogContent.getTimestamp().getTime());
                TapRecordEvent recordEvent;
                switch (Objects.requireNonNull(redoLogContent).getOperation()) {
                    case "INSERT": {
                        recordEvent = new TapInsertRecordEvent().init()
                                .table(redoLogContent.getTableName())
                                .after(redoLogContent.getRedoRecord());
                        break;
                    }
                    case "UPDATE": {
                        recordEvent = new TapUpdateRecordEvent().init()
                                .table(redoLogContent.getTableName())
                                .after(redoLogContent.getRedoRecord())
                                .before(redoLogContent.getUndoRecord());
                        break;
                    }
                    case "DELETE": {
                        recordEvent = new TapDeleteRecordEvent().init()
                                .table(redoLogContent.getTableName())
                                .before(redoLogContent.getRedoRecord());
                        break;
                    }
                    default:
                        lastRedoLogContent.set(redoLogContent);
                        continue;
                }
                recordEvent.setReferenceTime(referenceTime);
                if (withSchema) {
                    recordEvent.setNamespaces(Lists.newArrayList(redoLogContent.getSegOwner(), redoLogContent.getTableName()));
                }
                recordEvent.addInfo("streamOffset", streamOffset);
                recordEvent.setExactlyOnceId(exactlyOnceId);
                eventList.get().add(recordEvent);
            }
            lastRedoLogContent.set(redoLogContent);
        }
    }

    protected boolean addSchemaForDDLEvent(RedoLogContent redoLogContent, Alter ddl, DDLWrapper<?> wrapper) {
        if (wrapper instanceof OracleDDLWrapper) {
            OracleDDLWrapper oracleDDLWrapper = (OracleDDLWrapper) wrapper;
            if (Boolean.TRUE.equals(oracleDDLWrapper.getConfig().getWithSchema()) && (null == oracleDDLWrapper.getSchemaName(ddl))) {
                if (null != redoLogContent.getSegOwner()) {
                    ddl.getTable().setSchemaName(redoLogContent.getSegOwner());
                } else {
                    this.tapLogger.warn("orinal DDL need schema ,but this {} without schema. will filter", redoLogContent.getSqlRedo());
                    return false;
                }
            }
        }
        return true;
    }

    private Set<String> getTableObjectIds(boolean needFilterLater) throws SQLException {
        String sql;
        Set<String> tableObjectIds = new HashSet<>();
        if (needFilterLater) {
            if (withSchema) {
                sql = String.format(GET_TABLE_OBJECT_ID_WITH_CLAUSE, "OWNER IN ('" + String.join("','", schemaTableMap.keySet()) + "')");
            } else {
                sql = String.format(GET_TABLE_OBJECT_ID_WITH_CLAUSE, "OWNER = '" + oracleConfig.getSchema() + "'");
            }
        } else {
            if (withSchema) {
                String ownerTablesWhere = "(" + schemaTableMap.entrySet().stream().map(v -> "(OWNER = '" + v.getKey() + "' AND OBJECT_NAME IN ('" + String.join("','", v.getValue()) + "')").collect(Collectors.joining(" OR ")) + ")";
                sql = String.format(GET_TABLE_OBJECT_ID_WITH_CLAUSE, ownerTablesWhere);
            } else {
                sql = String.format(GET_TABLE_OBJECT_ID_WITH_CLAUSE, "OWNER = '" + oracleConfig.getSchema() + "' AND OBJECT_NAME IN ('" + String.join("','", tableList) + "')");
            }
        }
        oracleJdbcContext.query(sql, resultSet -> {
            while (resultSet.next()) {
                tableObjectIds.add(resultSet.getString("OBJECT_ID"));
                tableObjectId.put(resultSet.getLong(2), resultSet.getString(1));
            }
        });
        return tableObjectIds;
    }

    protected String analyzeLogSql(Long scn) throws SQLException {
        return analyzeLogSql(scn, false);
    }

    protected String analyzeLogSql(Long scn, boolean filterLater) throws SQLException {
        boolean needFilterLater;
        if (EmptyKit.isEmpty(schemaTableMap)) {
            needFilterLater = tableList.size() > 300;
        } else {
            needFilterLater = schemaTableMap.values().stream().map(List::size).reduce(Integer::sum).orElse(0) > 300;
        }
        needFilterLater = needFilterLater || filterLater;
        String sql;
        Set<String> tableObjectIds = new HashSet<>();
        if (version.equals("9i")) {
            sql = GET_REDO_LOG_RESULT_ORACLE_LOG_COLLECT_SQL_9i;
            tableObjectIds.addAll(getTableObjectIds(needFilterLater));
            sql = String.format(sql, scn, " AND OBJECT_ID IN ('" + String.join("','", tableObjectIds) + "')");
        } else {
            sql = GET_REDO_LOG_RESULT_ORACLE_LOG_COLLECT_SQL;
            String location;
            if (needFilterLater) {
                if (withSchema) {
                    location = " AND SEG_OWNER IN ('" + String.join("','", schemaTableMap.keySet()) + "')";
                } else {
                    location = " AND SEG_OWNER='" + oracleConfig.getSchema() + "'";
                }
            } else {
                if (withSchema) {
                    location = " AND (" + schemaTableMap.entrySet().stream().map(v -> "SEG_OWNER = '" + v.getKey() + "' AND TABLE_NAME IN ('" + String.join("','", v.getValue()) + "')").collect(Collectors.joining(" OR ")) + ")";
                } else {
                    location = " AND SEG_OWNER='" + oracleConfig.getSchema() + "' AND TABLE_NAME IN ('" + String.join("','", tableList) + "')";
                }
            }
            sql = String.format(
                    sql,
                    "19c".equals(version) && StringUtils.isNotBlank(oracleConfig.getPdb()) ? " SRC_CON_NAME = UPPER('" + oracleConfig.getPdb() + "') AND " : "",
                    scn, location
            );
        }
        return sql;
    }

    protected void analyzeLog(Object logData) throws SQLException {
        RedoLogContent redoLogContent = wrapRedoLogContent(logData);
        String operation = redoLogContent.getOperation();
        switch (operation) {
            case SqlConstant.REDO_LOG_OPERATION_SELECT_FOR_UPDATE:
            case SqlConstant.REDO_LOG_OPERATION_LOB_TRIM:
            case SqlConstant.REDO_LOG_OPERATION_LOB_WRITE:
            case SqlConstant.REDO_LOG_OPERATION_UNSUPPORTED:
                return;
            case SqlConstant.REDO_LOG_OPERATION_ROLLBACK:
                if (logData instanceof Map) {
                    if ("0000000000000000".equals(((Map) logData).get("PXID"))) {
                        return;
                    }
                } else {
                    if ("0000000000000000".equals(((ResultSet) logData).getString("PXID"))) {
                        return;
                    }
                }
        }
        if (!validateRedoLogContent(redoLogContent)) {
            return;
        }
        if (csfRedoLogProcess(logData, redoLogContent)) {
            return;
        }
        lastEventTimestamp = redoLogContent.getTimestamp().getTime();
        enqueueRedoLogContent(redoLogContent);
    }

    protected RedoLogContent wrapRedoLogContent(Object logData) throws SQLException {
        if (csfLogContent == null) {
            return buildRedoLogContent(logData);
        } else {
            return appendRedoAndUndoSql(logData);
        }
    }

    protected RedoLogContent buildRedoLogContent(Object logData) throws SQLException {
        RedoLogContent redoLogContent;
        if (logData instanceof ResultSet) {
            if (version.equals("9i")) {
                redoLogContent = new RedoLogContent(resultSet, tableObjectId, oracleConfig.getSysZoneId());
            } else {
                redoLogContent = new RedoLogContent(resultSet, oracleConfig.getSysZoneId());
            }
        } else if (logData instanceof Map) {
            if (version.equals("9i")) {
                redoLogContent = new RedoLogContent((Map) logData, tableObjectId);
            } else {
                redoLogContent = new RedoLogContent((Map) logData);
            }
        } else {
            redoLogContent = null;
        }
        return redoLogContent;
    }

    protected RedoLogContent appendRedoAndUndoSql(Object logData) throws SQLException {
        if (logData == null) {
            return null;
        }

        String redoSql = "";
        String undoSql = "";

        if (logData instanceof ResultSet) {
            redoSql = ((ResultSet) logData).getString("SQL_REDO");
            undoSql = ((ResultSet) logData).getString("SQL_UNDO");
        } else if (logData instanceof Map) {
            Object sqlRedoObj = ((Map) logData).getOrDefault("SQL_REDO", "");
            if (sqlRedoObj != null) {
                redoSql = sqlRedoObj.toString();
            }
            final Object sqlUndoObj = ((Map) logData).getOrDefault("SQL_UNDO", "");
            if (sqlUndoObj != null) {
                undoSql = sqlUndoObj.toString();
            }
        }
        if (StringUtils.isNotBlank(redoSql)) {
            csfLogContent.setSqlRedo(csfLogContent.getSqlRedo() + redoSql);
        }

        if (StringUtils.isNotBlank(undoSql)) {
            csfLogContent.setSqlUndo(csfLogContent.getSqlUndo() + undoSql);
        }

        RedoLogContent redoLogContent = new RedoLogContent();
        beanUtils.copyProperties(csfLogContent, redoLogContent);

        return redoLogContent;
    }

    protected boolean validateRedoLogContent(RedoLogContent redoLogContent) {
        if (redoLogContent == null) {
            return false;
        }

        if (!StringUtils.equalsAnyIgnoreCase(redoLogContent.getOperation(),
                SqlConstant.REDO_LOG_OPERATION_COMMIT, SqlConstant.REDO_LOG_OPERATION_ROLLBACK)) {
            if (withSchema) {
                if (schemaTableMap.containsKey(redoLogContent.getSegOwner())) {
                    return schemaTableMap.get(redoLogContent.getSegOwner()).contains(redoLogContent.getTableName());
                } else {
                    return false;
                }
            } else {
                // check owner
                if (StringUtils.isNotBlank(redoLogContent.getSegOwner())
                        && !oracleConfig.getSchema().equals(redoLogContent.getSegOwner())) {
                    return false;
                }
                // check table name
                return !EmptyKit.isNotBlank(redoLogContent.getTableName()) || tableList.contains(redoLogContent.getTableName());
            }
        }

        return true;
    }

    protected void logDataProcess(boolean onlineRedo, Map<String, Object> logData) throws SQLException {
        if (onlineRedo) {
            String redoLogContentId = RedoLogContent.id(
                    ((BigDecimal) logData.get("THREAD#")).intValue(),
                    (String) logData.get("XID"),
                    ((BigDecimal) logData.get("SCN")).longValue(),
                    (String) logData.get("RS_ID"),
                    ((BigDecimal) logData.get("SSN")).longValue(),
                    ((BigDecimal) logData.get("CSF")).intValue(),
                    (String) logData.get("SQL_REDO")
            );
            if (cacheRedoLogContent.containsKey(redoLogContentId)) {
                return;
            }
            cacheRedoLogContent.put(redoLogContentId, "");
        }
        //解析日志
        analyzeLog(logData);
    }

    protected String generateLogContentId(Map<String, Object> logData) {
        return RedoLogContent.id(
                ((BigDecimal) logData.get("THREAD#")).intValue(),
                (String) logData.get("XID"),
                ((BigDecimal) logData.get("SCN")).longValue(),
                (String) logData.get("RS_ID"),
                ((BigDecimal) logData.get("SSN")).longValue(),
                ((BigDecimal) logData.get("CSF")).intValue(),
                (String) logData.get("SQL_REDO"));
    }

    protected boolean csfRedoLogProcess(Object logData, RedoLogContent redoLogContent) {
        // handle continuation redo/undo sql
        if (isCsf(logData)) {
            if (csfLogContent == null) {
                csfLogContent = new RedoLogContent();
                beanUtils.copyProperties(redoLogContent, csfLogContent);
            }
            return true;
        } else {
            csfLogContent = null;
        }
        return false;
    }

    protected static boolean isCsf(Object logData) {
        if (logData != null) {
            try {
                Integer csf = null;

                if (logData instanceof ResultSet) {
                    csf = ((ResultSet) logData).getInt("CSF");
                } else if (logData instanceof Map) {
                    csf = Integer.valueOf(((Map) logData).get("CSF").toString());
                }

                if (csf != null) {
                    return csf.equals(1);
                } else {
                    return false;
                }
            } catch (Exception e) {
                return false;
            }
        }

        return false;
    }

    public abstract void startMiner() throws Throwable;

    public void stopMiner() throws Throwable {
        super.stopMiner();
        if (EmptyKit.isNotNull(statement)) {
            ErrorKit.ignoreAnyError(() -> statement.execute(END_LOG_MINOR_SQL));
            tapLogger.info("Log Miner has been closed!");
            ErrorKit.ignoreAnyError(statement::close);
        }
        if (EmptyKit.isNotNull(resultSet)) {
            ErrorKit.ignoreAnyError(resultSet::close);
            resultSet = null;
        }
        if (EmptyKit.isNotNull(preparedStatement)) {
            ErrorKit.ignoreAnyError(preparedStatement::close);
        }
        if (EmptyKit.isNotNull(connection)) {
            ErrorKit.ignoreAnyError(connection::close);
        }
        backFindLobPsmtMap.forEach((key, value) -> ErrorKit.ignoreAnyError(value::close));
        if (EmptyKit.isNotNull(backFindLobConnection)) {
            ErrorKit.ignoreAnyError(backFindLobConnection::close);
        }
        if (EmptyKit.isNotNull(sqlParser)) {
            sqlParser.unload();
        }
    }
}
