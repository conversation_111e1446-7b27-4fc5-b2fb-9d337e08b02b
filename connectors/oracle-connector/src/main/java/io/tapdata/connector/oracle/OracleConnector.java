package io.tapdata.connector.oracle;

import io.tapdata.common.CommonDbConnector;
import io.tapdata.common.DefaultSqlExecutor;
import io.tapdata.common.SqlExecuteCommandFunction;
import io.tapdata.connector.oracle.bean.OracleColumn;
import io.tapdata.connector.oracle.bean.OracleProxyConnection;
import io.tapdata.connector.oracle.cdc.OracleCdcRunner;
import io.tapdata.connector.oracle.cdc.bridge.BridgeService;
import io.tapdata.connector.oracle.cdc.offset.OracleOffset;
import io.tapdata.connector.oracle.config.OracleConfig;
import io.tapdata.connector.oracle.ddl.OracleDDLSqlGenerator;
import io.tapdata.connector.oracle.dml.OracleRecordWriter;
import io.tapdata.connector.oracle.exception.OracleExceptionCollector;
import io.tapdata.connector.oracle.query.QueryTableHash;
import io.tapdata.connector.oracle.query.entity.QueryEntity;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.ddl.table.TapAlterFieldAttributesEvent;
import io.tapdata.entity.event.ddl.table.TapAlterFieldNameEvent;
import io.tapdata.entity.event.ddl.table.TapDropFieldEvent;
import io.tapdata.entity.event.ddl.table.TapNewFieldEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.value.*;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.simplify.pretty.BiClassHandlers;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import io.tapdata.kit.StringKit;
import io.tapdata.partition.DatabaseReadPartitionSplitter;
import io.tapdata.pdk.apis.annotations.TapConnectorClass;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.context.TapConnectionContext;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TapAdvanceFilter;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.entity.WriteListResult;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.connection.TableInfo;
import io.tapdata.pdk.apis.functions.connector.common.vo.TapHashResult;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;
import io.tapdata.pdk.apis.functions.connector.source.GetReadPartitionOptions;
import io.tapdata.pdk.apis.partition.FieldMinMaxValue;
import io.tapdata.pdk.apis.partition.splitter.StringCaseInsensitiveSplitter;
import io.tapdata.pdk.apis.partition.splitter.TypeSplitterMap;
import oracle.sql.*;
import oracle.xdb.XMLType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.FileSystemUtils;

import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.*;

@TapConnectorClass("spec_oracle.json")
public class OracleConnector extends CommonDbConnector {

    private OracleConfig oracleConfig;
    protected OracleJdbcContext oracleJdbcContext;
    private OracleCdcRunner cdcRunner;
    private String oracleVersion;
    private OracleProxyConnection oracleProxyConnection;

    private final static String ORACLE_CURRENT_TIMESTAMP = "select systimestamp from dual";

    private SqlExecuteCommandFunction sqlExecuteCommandFunction;

    //initialize jdbc context, version
    protected void initConnection(TapConnectionContext connectionContext) throws SQLException {
        tapLogger = connectionContext.getLog();
        oracleConfig = new OracleConfig().load(connectionContext.getConnectionConfig());
        isConnectorStarted(connectionContext, connectorContext -> {
            firstConnectorId = (String) connectorContext.getStateMap().get("firstConnectorId");
            if (EmptyKit.isNull(firstConnectorId)) {
                firstConnectorId = UUID.randomUUID().toString().replace("-", "");
                connectorContext.getStateMap().put("firstConnectorId", firstConnectorId);
            }
            oracleConfig.load(connectorContext.getNodeConfig());
        });
        oracleJdbcContext = new OracleJdbcContext(oracleConfig);
        String sysTimezone = oracleJdbcContext.querySysTimezone();
        if (sysTimezone != null) {
            oracleConfig.setSysZoneId(ZoneId.of(sysTimezone));
        }
        if (oracleConfig.getOldVersionTimezone() && sysTimezone != null) {
            oracleConfig.setZoneId(ZoneId.of(sysTimezone));
            oracleConfig.setTimezone(sysTimezone);
            oracleConfig.setZoneOffsetHour(TimeZone.getTimeZone("GMT" + sysTimezone).getRawOffset() / 3600000);
        }
        commonDbConfig = oracleConfig;
        jdbcContext = oracleJdbcContext;
        commonSqlMaker = new OracleSqlMaker().closeNotNull(oracleConfig.getCloseNotNull());
        oracleVersion = oracleJdbcContext.queryVersion();
        oracleProxyConnection = new OracleProxyConnection(oracleJdbcContext.getConnection());
        ddlSqlGenerator = new OracleDDLSqlGenerator();
        exceptionCollector = new OracleExceptionCollector();
        fieldDDLHandlers = new BiClassHandlers<>();
        fieldDDLHandlers.register(TapNewFieldEvent.class, this::newField);
        fieldDDLHandlers.register(TapAlterFieldAttributesEvent.class, this::alterFieldAttr);
        fieldDDLHandlers.register(TapAlterFieldNameEvent.class, this::alterFieldName);
        fieldDDLHandlers.register(TapDropFieldEvent.class, this::dropField);
        sqlExecuteCommandFunction = new SqlExecuteCommandFunction(new DefaultSqlExecutor(a -> processDataMap(a, null)));
    }

    @Override
    public void onStart(TapConnectionContext connectionContext) throws SQLException {
        initConnection(connectionContext);
        clearLogCache();
        if (EmptyKit.isNotNull(firstConnectorId)) {
            clearBridgeRunner(5);
        }
    }

    @Override
    public void onStop(TapConnectionContext connectionContext) {
        if (EmptyKit.isNotNull(cdcRunner)) {
            ErrorKit.ignoreAnyError(cdcRunner::closeCdcRunner);
        }
        if (EmptyKit.isNotNull(oracleProxyConnection)) {
            ErrorKit.ignoreAnyError(oracleProxyConnection::close);
        }
        if (EmptyKit.isNotNull(oracleJdbcContext)) {
            ErrorKit.ignoreAnyError(() -> oracleJdbcContext.close());
        }
        clearLogCache();
    }

    private void onDestroy(TapConnectorContext connectorContext) throws Exception {
        try {
            onStart(connectorContext);
            clearLogCache();
        } finally {
            onStop(connectorContext);
            if (EmptyKit.isNotNull(firstConnectorId)) {
                clearBridgeRunner(5);
            }
        }
    }

    protected void clearLogCache() {
        File cacheDir = new File("cacheTransaction" + File.separator + firstConnectorId);
        if (cacheDir.exists()) {
            FileSystemUtils.deleteRecursively(cacheDir);
        }
    }

    protected void clearBridgeRunner(int seconds) {
        if ("bridge".equals(oracleConfig.getLogPluginName())) {
            TapSimplify.sleep(TimeUnit.SECONDS.toMillis(seconds));
            int maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries) {
                try (
                        BridgeService bridgeService = BridgeService.create(oracleConfig.getRawLogServerHost(), oracleConfig.getRawLogServerPort(), firstConnectorId)
                ) {
                    bridgeService.setTapLogger(tapLogger);
                    bridgeService.initTask();
                    bridgeService.clearTask();
                    break;
                } catch (Exception e) {
                    attempt++;
                    tapLogger.warn("Attempt " + attempt + " failed to clear bridge cache directory", e);
                    if (attempt >= maxRetries) {
                        tapLogger.error("Clear bridge cache directory failed after " + maxRetries + " attempts", e);
                    } else {
                        try {
                            Thread.sleep(10000);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            }
            TapSimplify.sleep(TimeUnit.SECONDS.toMillis(seconds));
        }
    }

    @Override
    public void registerCapabilities(ConnectorFunctions connectorFunctions, TapCodecsRegistry codecRegistry) {
        //test
        connectorFunctions.supportReleaseExternalFunction(this::onDestroy);
        connectorFunctions.supportErrorHandleFunction(this::errorHandle);
        //target
        connectorFunctions.supportWriteRecord(this::writeRecord);
        connectorFunctions.supportCreateTableV2(this::createTableV2);
        connectorFunctions.supportClearTable(this::clearTable);
        connectorFunctions.supportDropTable(this::dropTable);
        //source
        connectorFunctions.supportBatchCount(this::batchCount);
        connectorFunctions.supportBatchRead(this::batchReadWithoutOffset);
        connectorFunctions.supportStreamRead(this::streamRead);
        connectorFunctions.supportStreamReadMultiConnectionFunction(this::streamReadMultiConnection);
        connectorFunctions.supportTimestampToStreamOffset(this::timestampToStreamOffset);
        connectorFunctions.supportGetStreamOffsetFunction(this::getStreamOffsetFromString);
        //query
        connectorFunctions.supportQueryByFilter(this::queryByFilter);
        connectorFunctions.supportQueryByAdvanceFilter(this::queryByAdvanceFilterWithOffsetV2);
        connectorFunctions.supportGetTableNamesFunction(this::getTableNames);
        //ddl
        connectorFunctions.supportNewFieldFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldNameFunction(this::fieldDDLHandler);
        connectorFunctions.supportAlterFieldAttributesFunction(this::fieldDDLHandler);
        connectorFunctions.supportDropFieldFunction(this::fieldDDLHandler);
        connectorFunctions.supportCreateIndex(this::createIndex);
        connectorFunctions.supportQueryIndexes(this::queryIndexes);
        connectorFunctions.supportExecuteCommandFunction((a, b, c) -> sqlExecuteCommandFunction.executeSqlCommand(a, b, () -> oracleJdbcContext.getConnection(), this::isAlive, c));
        connectorFunctions.supportRunRawCommandFunction(this::runRawCommand);
        connectorFunctions.supportCountRawCommandFunction(this::countRawCommand);
        connectorFunctions.supportCountByPartitionFilterFunction(this::countByAdvanceFilterV2);
        connectorFunctions.supportQueryFieldMinMaxValueFunction(this::minMaxValue);
        connectorFunctions.supportGetReadPartitionsFunction(this::getReadPartitions);
        //hash verify
        connectorFunctions.supportQueryHashByAdvanceFilterFunction(this::queryHash);

        codecRegistry.registerFromTapValue(TapRawValue.class, "CLOB", tapRawValue -> {
            if (tapRawValue != null && tapRawValue.getValue() != null) return tapRawValue.getValue().toString();
            return "null";
        });
        codecRegistry.registerFromTapValue(TapMapValue.class, "CLOB", tapMapValue -> {
            if (tapMapValue != null && tapMapValue.getValue() != null) return toJson(tapMapValue.getValue());
            return "null";
        });
        codecRegistry.registerFromTapValue(TapArrayValue.class, "CLOB", tapValue -> {
            if (tapValue != null && tapValue.getValue() != null) return toJson(tapValue.getValue());
            return "null";
        });
        codecRegistry.registerFromTapValue(TapBooleanValue.class, "INTEGER", tapValue -> {
            if (tapValue != null && tapValue.getValue() != null)
                return Boolean.TRUE.equals(tapValue.getValue()) ? 1 : 0;
            return 0;
        });

        codecRegistry.registerToTapValue(TIMESTAMP.class, (value, tapType) -> {
            try {
                if (oracleConfig.getOldVersionTimezone()) {
                    return new TapDateTimeValue(new DateTime(((TIMESTAMP) value).timestampValue()));
                } else {
                    return new TapDateTimeValue(new DateTime(((TIMESTAMP) value).toLocalDateTime().minusHours(oracleConfig.getZoneOffsetHour())));
                }
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
        codecRegistry.registerToTapValue(XMLType.class, (value, tapType) -> {
            try {
                return new TapStringValue(((XMLType) value).getString());
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
        codecRegistry.registerToTapValue(TIMESTAMPTZ.class, (value, tapType) -> {
            try {
                return new TapDateTimeValue(new DateTime(((TIMESTAMPTZ) value).toZonedDateTime()));
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
        codecRegistry.registerToTapValue(TIMESTAMPLTZ.class, (value, tapType) -> {
            try {
                return new TapDateTimeValue(new DateTime(((TIMESTAMPLTZ) value).toLocalDateTime(oracleProxyConnection.getOracleConnection())));
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
        codecRegistry.registerToTapValue(INTERVALDS.class, (value, tapType) -> new TapStringValue(((INTERVALDS) value).stringValue()));
        codecRegistry.registerToTapValue(INTERVALYM.class, (value, tapType) -> new TapStringValue(((INTERVALYM) value).stringValue()));
//        codecRegistry.registerToTapValue(CLOB.class, (value, tapType) -> {
//            try {
//                return new TapStringValue(((CLOB) value).stringValue());
//            } catch (SQLException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        codecRegistry.registerToTapValue(NCLOB.class, (value, tapType) -> {
//            try {
//                return new TapStringValue(((NCLOB) value).stringValue());
//            } catch (SQLException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        codecRegistry.registerToTapValue(BLOB.class, (value, tapType) -> new TapBinaryValue(DbKit.blobToBytes((BLOB) value)));
        //TapTimeValue, TapDateTimeValue and TapDateValue's value is DateTime, need convert into Date object.
        codecRegistry.registerFromTapValue(TapTimeValue.class, "CHAR(8)", tapTimeValue -> formatTapDateTime(tapTimeValue.getValue(), "HH:mm:ss"));
        codecRegistry.registerFromTapValue(TapDateTimeValue.class, tapDateTimeValue -> {
            if (EmptyKit.isNotNull(tapDateTimeValue.getValue().getTimeZone())) {
                return tapDateTimeValue.getValue().toInstant().atZone(tapDateTimeValue.getValue().getTimeZone().toZoneId());
            } else {
                if (oracleConfig.getOldVersionTimezone()) {
                    return tapDateTimeValue.getValue().toTimestamp();
                } else {
                    return Timestamp.valueOf(tapDateTimeValue.getValue().toInstant().atZone(oracleConfig.getZoneId()).toLocalDateTime());
                }
            }
        });
        codecRegistry.registerFromTapValue(TapYearValue.class, "CHAR(4)", TapValue::getOriginValue);
        codecRegistry.registerFromTapValue(TapDateValue.class, "CHAR(10)", tapDateValue -> formatTapDateTime(tapDateValue.getValue(), "yyyy-MM-dd"));
        connectorFunctions.supportGetTableInfoFunction(this::getTableInfo);
        connectorFunctions.supportTransactionBeginFunction(this::beginTransaction);
        connectorFunctions.supportTransactionCommitFunction(this::commitTransaction);
        connectorFunctions.supportTransactionRollbackFunction(this::rollbackTransaction);
    }

    private void writeRecord(TapConnectorContext connectorContext, List<TapRecordEvent> tapRecordEvents, TapTable tapTable, Consumer<WriteListResult<TapRecordEvent>> writeListResultConsumer) throws SQLException {
        String insertDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_INSERT_POLICY);
        if (insertDmlPolicy == null) {
            insertDmlPolicy = ConnectionOptions.DML_INSERT_POLICY_UPDATE_ON_EXISTS;
        }
        String updateDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_UPDATE_POLICY);
        if (updateDmlPolicy == null) {
            updateDmlPolicy = ConnectionOptions.DML_UPDATE_POLICY_IGNORE_ON_NON_EXISTS;
        }
        if (isTransaction) {
            String threadName = Thread.currentThread().getName();
            Connection connection;
            if (transactionConnectionMap.containsKey(threadName)) {
                connection = transactionConnectionMap.get(threadName);
            } else {
                connection = oracleJdbcContext.getConnection();
                transactionConnectionMap.put(threadName, connection);
            }
            new OracleRecordWriter(oracleJdbcContext, connection, tapTable)
                    .setVersion(oracleVersion)
                    .setInsertPolicy(insertDmlPolicy)
                    .setUpdatePolicy(updateDmlPolicy)
                    .setTapLogger(tapLogger)
                    .write(tapRecordEvents, writeListResultConsumer, this::isAlive);

        } else {
            new OracleRecordWriter(oracleJdbcContext, tapTable)
                    .setVersion(oracleVersion)
                    .setInsertPolicy(insertDmlPolicy)
                    .setUpdatePolicy(updateDmlPolicy)
                    .setTapLogger(tapLogger)
                    .write(tapRecordEvents, writeListResultConsumer, this::isAlive);
        }
    }

    private void batchRead(TapConnectorContext tapConnectorContext, TapTable tapTable, Object offsetState, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) throws Throwable {
        OracleOffset oracleOffset;
        //beginning
        if (null == offsetState) {
            oracleOffset = new OracleOffset(commonSqlMaker.getOrderByUniqueKey(tapTable), 0L);
        }
        //with offset
        else {
            oracleOffset = (OracleOffset) offsetState;
        }
        String sql = "SELECT * FROM (SELECT a.*,ROWNUM row_no FROM\"" + oracleConfig.getSchema() + "\".\"" + tapTable.getId() + "\" a " + oracleOffset.getSortString() + ") WHERE row_no>" + oracleOffset.getOffsetValue();
        oracleJdbcContext.query(sql, resultSet -> {
            List<TapEvent> tapEvents = list();
            //get all column names
            List<String> columnNames = DbKit.getColumnsFromResultSet(resultSet);
            while (isAlive() && resultSet.next()) {
                DataMap dataMap = DbKit.getRowFromResultSet(resultSet, columnNames);
                dataMap.remove("ROW_NO");
                tapEvents.add(insertRecordEvent(dataMap, tapTable.getId()));
                if (tapEvents.size() == eventBatchSize) {
                    oracleOffset.setOffsetValue(oracleOffset.getOffsetValue() + eventBatchSize);
                    eventsOffsetConsumer.accept(tapEvents, oracleOffset);
                    tapEvents = list();
                }
            }
            //last events those less than eventBatchSize
            if (EmptyKit.isNotEmpty(tapEvents)) {
                oracleOffset.setOffsetValue(oracleOffset.getOffsetValue() + tapEvents.size());
                eventsOffsetConsumer.accept(tapEvents, oracleOffset);
            }
        });

    }

    private void batchReadV2(TapConnectorContext tapConnectorContext, TapTable tapTable, Object offsetState, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) throws Throwable {
        String columns = tapTable.getNameFieldMap().keySet().stream().map(c -> "\"" + c + "\"").collect(Collectors.joining(","));
        String sql = String.format("SELECT %s FROM\"" + oracleConfig.getSchema() + "\".\"" + tapTable.getId() + "\"", columns);

        oracleJdbcContext.query(sql, resultSet -> {
            List<TapEvent> tapEvents = list();
            //get all column names
            List<String> columnNames = DbKit.getColumnsFromResultSet(resultSet);
            while (isAlive() && resultSet.next()) {
                DataMap dataMap = DbKit.getRowFromResultSet(resultSet, columnNames);
                tapEvents.add(insertRecordEvent(dataMap, tapTable.getId()));
                if (tapEvents.size() == eventBatchSize) {
                    eventsOffsetConsumer.accept(tapEvents, new OracleOffset());
                    tapEvents = list();
                }
            }
            //last events those less than eventBatchSize
            if (EmptyKit.isNotEmpty(tapEvents)) {
                eventsOffsetConsumer.accept(tapEvents, new OracleOffset());
            }
        });

    }

    protected String getHashSplitStringSql(TapTable tapTable) {
        return "ORA_HASH(ROWID)";
    }

    @Override
    public ConnectionOptions connectionTest(TapConnectionContext connectionContext, Consumer<TestItem> consumer) {
        oracleConfig = new OracleConfig().load(connectionContext.getConnectionConfig());
        ConnectionOptions connectionOptions = ConnectionOptions.create();
        connectionOptions.connectionString(oracleConfig.getConnectionString());
        try (
                OracleTest oracleTest = OracleTest.create(oracleConfig, consumer, connectionOptions)
        ) {
            oracleTest.testOneByOne();
            connectionOptions.setDbVersion(oracleTest.queryVersion());
        } catch (Exception e) {
            connectionContext.getLog().warn(e.getMessage());
        }

        Optional.ofNullable(oracleConfig.getThinType()).map(thinType -> {
            String thinValue;
            switch (thinType) {
                case "SID":
                    thinValue = oracleConfig.getSid();
                    break;
                case "SERVICE_NAME":
                    thinValue = oracleConfig.getDatabase();
                    break;
                default:
                    return null;
            }

            if (oracleConfig.getPort() <= 0) return null;
            if (StringUtils.isAnyBlank(oracleConfig.getHost(), oracleConfig.getSchema())) return null;

            connectionOptions.setInstanceUniqueId(StringKit.md5(String.join("|"
                    , oracleConfig.getUser()
                    , oracleConfig.getHost()
                    , String.valueOf(oracleConfig.getPort())
                    , oracleConfig.getThinType()
                    , thinValue
                    , oracleConfig.getLogPluginName()
            )));
            connectionOptions.setNamespaces(Collections.singletonList(oracleConfig.getSchema()));

            return null;
        });

        return connectionOptions;
    }

    private void streamRead(TapConnectorContext nodeContext, List<String> tableList, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        cdcRunner = new OracleCdcRunner(oracleJdbcContext, firstConnectorId, tapLogger).init(
                tableList,
                nodeContext.getTableMap(),
                offsetState,
                recordSize,
                consumer
        );
        cdcRunner.startCdcRunner();
    }

    private void streamReadMultiConnection(TapConnectorContext nodeContext, List<ConnectionConfigWithTables> connectionConfigWithTables, Object offsetState, int batchSize, StreamReadConsumer consumer) throws Throwable {
        cdcRunner = new OracleCdcRunner(oracleJdbcContext, firstConnectorId, tapLogger).multiInit(
                connectionConfigWithTables,
                nodeContext.getTableMap(),
                offsetState,
                batchSize,
                consumer
        );
        cdcRunner.startCdcRunner();
    }

    private Object timestampToStreamOffset(TapConnectorContext connectorContext, Long offsetStartTime) throws Throwable {
        OracleOffset oracleOffset = new OracleOffset();
        if (EmptyKit.isNotNull(offsetStartTime)) {
            oracleJdbcContext.queryWithNext(ORACLE_CURRENT_TIMESTAMP, resultSet ->
                    oracleOffset.setTimestamp(resultSet.getTimestamp(1).getTime() + offsetStartTime - System.currentTimeMillis()));
        } else {
            //需要考虑未提交事务，暂时向前找半小时
            try {
                oracleJdbcContext.queryWithNext(ORACLE_PENDING_SCN, resultSet -> {
                    if (resultSet.getLong(1) > 0) {
                        tapLogger.warn("Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: {}", resultSet.getLong(1));
                        oracleJdbcContext.queryWithNext(ORACLE_PENDING_SCN_HALF_HOUR, rs -> {
                            if (rs.getLong(1) > 0) {
                                oracleOffset.setLastScn(rs.getLong(1) - 1);
                            }
                        });
                    }
                });
            } catch (Exception e) {
                tapLogger.warn("Query pending transaction failed, maybe no permission, please check if there are any earlier transactions to prevent data loss", e);
            }
            if (EmptyKit.isNull(oracleOffset.getLastScn()) || oracleOffset.getLastScn() == 0) {
                oracleJdbcContext.queryWithNext(CHECK_CURRENT_SCN, resultSet -> oracleOffset.setLastScn(resultSet.getLong(1)));
            }
        }
        return oracleOffset;
    }

    private Object getStreamOffsetFromString(TapConnectorContext connectorContext, String offset) {
        try {
            long scn = Long.parseLong(offset);
            OracleOffset oracleOffset = new OracleOffset();
            oracleOffset.setLastScn(scn);
            oracleOffset.setPendingScn(scn);
            return oracleOffset;
        } catch (Exception e) {
            throw new RuntimeException("Oracle use scn as offset, invalid scn: " + offset, e);
        }
    }

    protected TapField makeTapField(DataMap dataMap) {
        return new OracleColumn(dataMap).getTapField();
    }

    @Override
    protected Map<String, Object> getSpecificAttr(DataMap dataMap) {
        return TapSimplify.map(TapSimplify.entry("capacity", dataMap.getValue("capacity", 0L)));
    }

    private TableInfo getTableInfo(TapConnectionContext tapConnectorContext, String tableName) throws Throwable {
        DataMap dataMap = oracleJdbcContext.getTableInfo(tableName);
        TableInfo tableInfo = TableInfo.create();
        String rowsStr = dataMap.getString("NUM_ROWS");
        long rows = EmptyKit.isNull(rowsStr) ? 0 : Long.parseLong(rowsStr);
        tableInfo.setNumOfRows(rows);
        String avgRowLenStr = dataMap.getString("AVG_ROW_LEN");
        long avgRowLen = EmptyKit.isNull(avgRowLenStr) ? 0 : Long.parseLong(avgRowLenStr);
        tableInfo.setStorageSize(avgRowLen * rows);
        return tableInfo;
    }

    @Override
    protected void processDataMap(DataMap dataMap, TapTable tapTable) throws RuntimeException {
        try {
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                Object value = entry.getValue();
                if (value instanceof Timestamp && !oracleConfig.getOldVersionTimezone()) {
                    entry.setValue(((Timestamp) value).toLocalDateTime().minusHours(oracleConfig.getZoneOffsetHour()));
                } else if (value instanceof CLOB) {
                    // CLOB or NCLOB
                    entry.setValue(((CLOB) value).stringValue());
                } else if (value instanceof BLOB) {
                    entry.setValue(DbKit.blobToBytes((BLOB) value));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    private void getReadPartitions(TapConnectorContext connectorContext, TapTable table, GetReadPartitionOptions options) throws Throwable {
        DatabaseReadPartitionSplitter.calculateDatabaseReadPartitions(connectorContext, table, options)
                .queryFieldMinMaxValue(this::minMaxValue)
                .typeSplitterMap(options.getTypeSplitterMap().registerSplitter(TypeSplitterMap.TYPE_STRING, StringCaseInsensitiveSplitter.INSTANCE))
                .startSplitting();
    }

    private FieldMinMaxValue minMaxValue(TapConnectorContext tapConnectorContext, TapTable tapTable, TapAdvanceFilter tapPartitionFilter, String fieldName) {
        FieldMinMaxValue fieldMinMaxValue = FieldMinMaxValue.create().fieldName(fieldName);
        String selectSql = commonSqlMaker.buildSelectClause(tapTable, tapPartitionFilter, true) + commonSqlMaker.buildRowNumberPreClause(tapPartitionFilter) + getSchemaAndTable(tapTable.getId()) + commonSqlMaker.buildSqlByAdvanceFilterV2(tapPartitionFilter);
        // min value
        String minSql = selectSql.replaceFirst("SELECT .* FROM \\(", String.format("SELECT MIN(\"%s\") AS MIN_VALUE FROM (", fieldName));
        AtomicReference<Object> minObj = new AtomicReference<>();
        try {
            oracleJdbcContext.query(minSql, rs -> {
                if (rs.next()) {
                    minObj.set(rs.getObject("MIN_VALUE"));
                }
            });
        } catch (SQLException e) {
            throw new RuntimeException("Query min value failed, sql: " + minSql, e);
        }
        Optional.ofNullable(minObj.get()).ifPresent(min -> fieldMinMaxValue.min(min).detectType(min));
        // max value
        String maxSql = selectSql.replaceFirst("SELECT .* FROM \\(", String.format("SELECT MAX(\"%s\") AS MAX_VALUE FROM (", fieldName));
        AtomicReference<Object> maxObj = new AtomicReference<>();
        try {
            oracleJdbcContext.query(maxSql, rs -> {
                if (rs.next()) {
                    maxObj.set(rs.getObject("MAX_VALUE"));
                }
            });
        } catch (SQLException e) {
            throw new RuntimeException("Query max value failed, sql: " + maxSql, e);
        }
        Optional.ofNullable(maxObj.get()).ifPresent(max -> fieldMinMaxValue.max(max).detectType(max));
        return fieldMinMaxValue;
    }

    //Oracle in verify hash inspect by [ MD5 ]
    protected void queryHash(TapConnectorContext context, TapAdvanceFilter tapAdvanceFilter, TapTable table, Consumer<TapHashResult<String>> tapHashResultConsumer) throws SQLException {
        QueryEntity queryEntity = new QueryEntity()
                .withJdbcContext(jdbcContext)
                .withIsAliveSupplier(this::isAlive);
        QueryTableHash.getQuery(queryEntity)
                .hashByAdvanceFilterFunction(tapAdvanceFilter, table, tapHashResultConsumer);
    }
}
