package io.tapdata.connector.oracle;

import io.tapdata.common.JdbcContext;
import io.tapdata.connector.oracle.config.OracleConfig;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;
import org.junit.jupiter.api.*;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;

public class TestOracleTest {
    @DisplayName("test query version not throw exception")
    @Test
    void test1() throws SQLException {
        JdbcContext jdbcContext = mock(JdbcContext.class);
        OracleTest oracleTest = mock(OracleTest.class);
        ReflectionTestUtils.setField(oracleTest, "jdbcContext", jdbcContext);
        Connection connection = mock(Connection.class);
        when(jdbcContext.getConnection()).thenReturn(connection);
        DatabaseMetaData databaseMetaData = mock(DatabaseMetaData.class);
        when(connection.getMetaData()).thenReturn(databaseMetaData);
        when(databaseMetaData.getDatabaseMajorVersion()).thenReturn(11);
        when(databaseMetaData.getDatabaseMinorVersion()).thenReturn(1);
        doCallRealMethod().when(oracleTest).queryVersion();
        String version = oracleTest.queryVersion();
        assertEquals("11.1",version);
    }
    @Nested
    class TestTimeDifference{
        OracleTest oracleTest;
        OracleJdbcContext oracleJdbcContext;
        @BeforeEach
        void init(){
            OracleConfig oracleConfig = new OracleConfig();
            oracleConfig.setUser("user");
            oracleConfig.setPassword("123456");
            oracleConfig.setDatabase("test");
            oracleConfig.setHost("localhost");
            oracleConfig.setPort(1521);
            Consumer<TestItem> consumer = testItem -> {};
            oracleTest = new OracleTest(oracleConfig,consumer,new ConnectionOptions());
            oracleJdbcContext = mock(OracleJdbcContext.class);
            ReflectionTestUtils.setField(oracleTest,"jdbcContext",oracleJdbcContext);
        }
        @Test
        void test_Pass(){
            when(oracleJdbcContext.queryTimestamp()).thenReturn(System.currentTimeMillis());
            oracleTest.testTimeDifference();
            ConnectionOptions connectionOptions = (ConnectionOptions) ReflectionTestUtils.getField(oracleTest,"connectionOptions");
            Assertions.assertEquals(0,connectionOptions.getTimeDifference());
        }
        @Test
        void test_Fail(){
            when(oracleJdbcContext.queryTimestamp()).thenReturn(System.currentTimeMillis() + 2000);
            oracleTest.testTimeDifference();
            ConnectionOptions connectionOptions = (ConnectionOptions) ReflectionTestUtils.getField(oracleTest,"connectionOptions");
            Assertions.assertTrue(connectionOptions.getTimeDifference() > 1000);
        }

        @Test
        void testQuerySourceTimeFail(){
            when(oracleJdbcContext.queryTimestamp()).thenThrow(new SQLException());
            oracleTest.testTimeDifference();
            ConnectionOptions connectionOptions = (ConnectionOptions) ReflectionTestUtils.getField(oracleTest,"connectionOptions");
            Assertions.assertNull(connectionOptions.getTimeDifference());
        }
    }


}
