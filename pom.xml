<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<groupId>io.tapdata</groupId>
	<version>1.0-SNAPSHOT</version>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>daas</artifactId>
    <name>daas</name>
    <packaging>pom</packaging>
    <modules>
        <module>manager</module>
        <module>iengine</module>
        <module>tapdata-test</module>
        <module>tapdata-cli</module>
    </modules>

    <properties>
        <sonar.coverage.exclusions>
            manager/**/dto/*,
            manager/**/vo/*,
            manager/**/param/*,
            manager/tm/**/controller/*,
            manager/**/constant/*,
            manager/tm/**/ex/*,
            **/*Dto.java,
            **/*Enum.java,
            tapdata-cli/**,
            iengine/iengine-common/src/main/java/com/tapdata/exception/**,
            manager/tm-common/src/main/java/com/tapdata/tm/commons/task/dto/**,
            manager/tm/src/main/java/com/tapdata/tm/init/patches/daas/*,
            manager/tm/src/main/java/com/tapdata/tm/init/patches/dfs/*,
            **/*Entity.java,
            **/*VO.java,
            **/src/main/java/io/tapdata/**/exception/**,
            iengine/api/src/main/java/io/tapdata/aspect/CreateTableFuncAspect.java,
            iengine/api/src/main/java/io/tapdata/aspect/DropTableFuncAspect.java,
            **/*Context.java,
            **/*Exception.java,
            **/*Controller.java,
            iengine/iengine-common/src/main/java/io/tapdata/log/CustomPatternLayout.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/task/preview/operation/**,
            **/entity/**,
            iengine/iengine-common/src/main/java/io/tapdata/log/CustomPatternLayout.java,
            iengine/iengine-common/src/main/java/io/tapdata/flow/engine/V2/log/CollectLog.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/exactlyonce/write/ExactlyOnceWriteCleaner.java,
            iengine/iengine-common/src/main/java/com/tapdata/processor/Log4jScriptLogger.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/script/ObsScriptLogger.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/StopTaskOnErrorLog.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/script/TapScriptLogger.java,
            iengine/api/src/main/java/io/tapdata/aspect/TaskBatchSplitAspect.java,
            manager/tm/src/main/java/com/tapdata/tm/config/*,
            manager/tm-api/src/main/java/com/tapdata/tm/inspect/bean/*,
            manager/tm/src/main/java/com/tapdata/tm/TMApplication.java,
            manager/tm-common/src/main/java/com/tapdata/tm/commons/util/PdkSchemaConvert.java,
            manager/tm-api/src/main/java/com/tapdata/tm/permissions/constants/DataPermissionEnumsName.java,
            **/vo/*.java,
            **/cons/*.java,
            manager/tm-oss/src/main/java/com/tapdata/tm/inspect/service/InspectServiceImpl.java
        </sonar.coverage.exclusions>
        <sonar.exclusions>
            manager/**/dto/*,
            manager/**/vo/*,
            manager/**/param/*,
            manager/tm/**/controller/*,
            manager/tm/**/constant/*,
            manager/tm/**/ex/*,
            **/*Dto.java,
            **/*Enum.java,
            **/src/main/java/io/tapdata/**/exception/**,
            tapdata-cli/**,
            manager/tm-common/src/main/java/com/tapdata/tm/commons/task/dto/**,
            manager/tm/src/main/java/com/tapdata/tm/init/patches/daas/*,
            manager/tm/src/main/java/com/tapdata/tm/init/patches/dfs/*,
            **/*Context.java,
            **/*Exception.java,
            **/*Controller.java,
            iengine/iengine-common/src/main/java/io/tapdata/log/CustomPatternLayout.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/task/preview/operation/**,
            **/entity/**,
            iengine/iengine-common/src/main/java/io/tapdata/log/CustomPatternLayout.java,
            iengine/iengine-common/src/main/java/io/tapdata/flow/engine/V2/log/CollectLog.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/exactlyonce/write/ExactlyOnceWriteCleaner.java,
            iengine/iengine-common/src/main/java/com/tapdata/processor/Log4jScriptLogger.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/script/ObsScriptLogger.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/StopTaskOnErrorLog.java,
            iengine/iengine-app/src/main/java/io/tapdata/flow/engine/V2/script/TapScriptLogger.java,
            iengine/api/src/main/java/io/tapdata/aspect/TaskBatchSplitAspect.java,
            manager/tm/src/main/java/com/tapdata/tm/config/*,
            manager/tm-api/src/main/java/com/tapdata/tm/inspect/bean/*,
            manager/tm/src/main/java/com/tapdata/tm/TMApplication.java,
            manager/tm-common/src/main/java/com/tapdata/tm/commons/util/PdkSchemaConvert.java,
            manager/tm-api/src/main/java/com/tapdata/tm/permissions/constants/DataPermissionEnumsName.java
            **/vo/*.java,
            **/cons/*.java,
            manager/tm-oss/src/main/java/com/tapdata/tm/inspect/service/InspectServiceImpl.java
        </sonar.exclusions>
        <sonar.test.exclusions>
            **/src/test/**
        </sonar.test.exclusions>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

        </plugins>
    </build>
    <repositories>
        <repository>
            <id>tapdata-wendangshujuku-mongo</id>
            <name>mongo</name>
            <url>https://tapdata-maven.pkg.coding.net/repository/wendangshujuku/mongo/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>tapdata-tapdata-maven</id>
            <name>maven</name>
            <url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>

