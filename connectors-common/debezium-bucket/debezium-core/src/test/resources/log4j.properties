# Direct log messages to stdout
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{ISO8601} %-5p     %m (%c)%n

# Direct Zookeeper log messages to stdout with special prefix
log4j.appender.zk=org.apache.log4j.ConsoleAppender
log4j.appender.zk.Target=System.out
log4j.appender.zk.layout=org.apache.log4j.PatternLayout
log4j.appender.zk.layout.ConversionPattern=%d{ISO8601} %-5p ZOO %m%n
#log4j.appender.zk.layout.ConversionPattern=%d{ISO8601} %-5p ZOO %m (%c)%n

# Direct Zookeeper Client log messages to stdout with special prefix
log4j.appender.zkclient=org.apache.log4j.ConsoleAppender
log4j.appender.zkclient.Target=System.out
log4j.appender.zkclient.layout=org.apache.log4j.PatternLayout
log4j.appender.zkclient.layout.ConversionPattern=%d{ISO8601} %-5p ZKC %m%n
#log4j.appender.zkclient.layout.ConversionPattern=%d{ISO8601} %-5p ZKC %m (%c)%n

# Direct Kafka log messages to stdout with special prefix
log4j.appender.kafka=org.apache.log4j.ConsoleAppender
log4j.appender.kafka.Target=System.out
log4j.appender.kafka.layout=org.apache.log4j.PatternLayout
log4j.appender.kafka.layout.ConversionPattern=%d{ISO8601} %-5p KAF %m%n
#log4j.appender.kafka.layout.ConversionPattern=%d{ISO8601} %-5p KAF %m (%c)%n

# Direct Debezium log messages to stdout with special prefix
log4j.appender.debezium=org.apache.log4j.ConsoleAppender
log4j.appender.debezium.Target=System.out
log4j.appender.debezium.layout=org.apache.log4j.PatternLayout
log4j.appender.debezium.layout.ConversionPattern=%d{ISO8601} %-5p DBZ %m (%c)%n

# Root logger option
log4j.rootLogger=INFO, stdout

# Set up the default logging to be INFO level, then override specific units
log4j.logger.io.debezium=INFO, debezium
log4j.additivity.io.debezium=false

# Kafka is pretty verbose at INFO level, so for brevity use ERROR everywhere except INFO at kafka.server.KafkaServer
log4j.logger.org.apache.kafka=ERROR, kafka
log4j.additivity.org.apache.kafka=false
log4j.logger.org.apache.kafka.common.utils=WARN, kafka
log4j.additivity.org.apache.kafka=false
log4j.logger.kafka=ERROR, kafka
log4j.additivity.kafka=false
log4j.logger.kafka.server.KafkaServer=ERROR, kafka
log4j.additivity.kafka.server.KafkaServer=false
log4j.logger.state.change.logger=ERROR, kafka
log4j.additivity.state.change.logger=false
#log4j.logger.org.apache.kafka.clients.consumer=DEBUG, kafka
#log4j.additivity.org.apache.kafka.clients.consumer=false
#log4j.logger.org.apache.kafka.clients.producer=DEBUG, kafka
#log4j.additivity.org.apache.kafka.clients.producer=false

# Zookeeper is pretty verbose at INFO level, so for brevity use ERROR everywhere except INFO at org.apache.zookeeper.server.ZooKeeperServer
log4j.logger.org.apache.zookeeper=ERROR, zk
log4j.additivity.org.apache.zookeeper=false
log4j.logger.org.apache.zookeeper.server.ZooKeeperServer=ERROR, zk
log4j.additivity.org.apache.zookeeper.server.ZooKeeperServer=false

# Zookeeper Client is pretty verbose at INFO level, so for brevity use ERROR everywhere ...
log4j.logger.org.I0Itec.zkclient=ERROR, zkclient
log4j.additivity.org.I0Itec.zkclient=false