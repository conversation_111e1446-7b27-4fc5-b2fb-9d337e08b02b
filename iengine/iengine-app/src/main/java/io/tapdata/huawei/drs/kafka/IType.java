package io.tapdata.huawei.drs.kafka;

import java.util.Map;

/**
 * 字段类型序列化
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/2/21 17:22 Create
 */
public interface IType {
    String type();

    Object decode(Object value);

    default void append2(Map<String, IType> map) {
        String type = type();
        if (map.containsKey(type))
            throw new RuntimeException("duplicate type '" + type + "'");
        map.put(type, this);
    }
}
